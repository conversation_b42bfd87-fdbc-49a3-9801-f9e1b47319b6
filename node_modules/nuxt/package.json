{"name": "nuxt", "version": "4.0.2", "repository": {"type": "git", "url": "git+https://github.com/nuxt/nuxt.git", "directory": "packages/nuxt"}, "homepage": "https://nuxt.com", "description": "Nuxt is a free and open-source framework with an intuitive and extendable way to create type-safe, performant and production-grade full-stack web applications and websites with Vue.js.", "license": "MIT", "type": "module", "types": "./types.d.ts", "bin": {"nuxi": "bin/nuxt.mjs", "nuxt": "bin/nuxt.mjs"}, "exports": {".": {"types": "./types.d.mts", "import": "./dist/index.mjs"}, "./config": {"types": "./config.d.ts", "import": "./config.js", "require": "./config.cjs"}, "./schema": {"types": "./schema.d.ts", "import": "./schema.js"}, "./kit": {"types": "./kit.d.ts", "import": "./kit.js"}, "./app": {"types": "./dist/app/index.d.ts", "import": "./dist/app/index.js"}, "./package.json": "./package.json"}, "imports": {"#app": {"types": "./dist/app/index.d.ts", "import": "./dist/app/index.js"}, "#app/nuxt": {"types": "./dist/app/nuxt.d.ts", "import": "./dist/app/nuxt.js"}, "#unhead/composables": {"types": "./dist/head/runtime/composables.d.ts", "import": "./dist/head/runtime/composables.js"}}, "files": ["app.d.ts", "bin", "types.d.ts", "types.d.mts", "dist", "config.*", "kit.*", "schema.*"], "dependencies": {"@nuxt/cli": "^3.27.0", "@nuxt/devalue": "^2.0.2", "@nuxt/devtools": "^2.6.2", "@nuxt/telemetry": "^2.6.6", "@unhead/vue": "^2.0.12", "@vue/shared": "^3.5.18", "c12": "^3.1.0", "chokidar": "^4.0.3", "compatx": "^0.2.0", "consola": "^3.4.2", "cookie-es": "^2.0.0", "defu": "^6.1.4", "destr": "^2.0.5", "devalue": "^5.1.1", "errx": "^0.1.0", "esbuild": "^0.25.8", "escape-string-regexp": "^5.0.0", "estree-walker": "^3.0.3", "exsolve": "^1.0.7", "h3": "^1.15.3", "hookable": "^5.5.3", "ignore": "^7.0.5", "impound": "^1.0.0", "jiti": "^2.5.1", "klona": "^2.0.6", "knitwork": "^1.2.0", "magic-string": "^0.30.17", "mlly": "^1.7.4", "mocked-exports": "^0.1.1", "nanotar": "^0.2.0", "nitropack": "^2.12.4", "nypm": "^0.6.0", "ofetch": "^1.4.1", "ohash": "^2.0.11", "on-change": "^5.0.1", "oxc-minify": "^0.78.0", "oxc-parser": "^0.78.0", "oxc-transform": "^0.78.0", "oxc-walker": "^0.4.0", "pathe": "^2.0.3", "perfect-debounce": "^1.0.0", "pkg-types": "^2.2.0", "radix3": "^1.1.2", "scule": "^1.3.0", "semver": "^7.7.2", "std-env": "^3.9.0", "strip-literal": "^3.0.0", "tinyglobby": "0.2.14", "ufo": "^1.6.1", "ultrahtml": "^1.6.0", "uncrypto": "^0.1.3", "unctx": "^2.4.1", "unimport": "^5.2.0", "unplugin": "^2.3.5", "unplugin-vue-router": "^0.14.0", "unstorage": "^1.16.1", "untyped": "^2.0.0", "vue": "^3.5.18", "vue-bundle-renderer": "^2.1.2", "vue-devtools-stub": "^0.1.0", "vue-router": "^4.5.1", "@nuxt/kit": "4.0.2", "@nuxt/schema": "4.0.2", "@nuxt/vite-builder": "4.0.2"}, "devDependencies": {"@nuxt/scripts": "0.11.10", "@parcel/watcher": "2.5.1", "@types/estree": "1.0.8", "@vitejs/plugin-vue": "6.0.1", "@vitejs/plugin-vue-jsx": "5.0.1", "@vue/compiler-sfc": "3.5.18", "unbuild": "3.6.0", "vite": "7.0.6", "vitest": "3.2.4", "vue-sfc-transformer": "0.1.16"}, "peerDependencies": {"@parcel/watcher": "^2.1.0", "@types/node": ">=18.12.0"}, "peerDependenciesMeta": {"@parcel/watcher": {"optional": true}, "@types/node": {"optional": true}}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"test:attw": "attw --pack"}}