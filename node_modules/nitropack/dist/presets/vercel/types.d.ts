/**
 * Vercel Build Output Configuration
 * @see https://vercel.com/docs/build-output-api/v3
 */
export interface VercelBuildConfigV3 {
    version: 3;
    routes?: ({
        src: string;
        headers: {
            "cache-control": string;
        };
        continue: boolean;
    } | {
        handle: string;
    } | {
        src: string;
        dest: string;
    })[];
    images?: {
        sizes: number[];
        domains: string[];
        remotePatterns?: {
            protocol?: "http" | "https";
            hostname: string;
            port?: string;
            pathname?: string;
        }[];
        minimumCacheTTL?: number;
        formats?: ("image/avif" | "image/webp")[];
        dangerouslyAllowSVG?: boolean;
        contentSecurityPolicy?: string;
    };
    wildcard?: Array<{
        domain: string;
        value: string;
    }>;
    overrides?: Record<string, {
        path?: string;
        contentType?: string;
    }>;
    cache?: string[];
    bypassToken?: string;
    crons?: {
        path: string;
        schedule: string;
    }[];
}
/**
 * https://vercel.com/docs/build-output-api/v3/primitives#serverless-function-configuration
 */
export interface VercelServerlessFunctionConfig {
    /**
     * Amount of memory (RAM in MB) that will be allocated to the Serverless Function.
     */
    memory?: number;
    /**
     * Maximum execution duration (in seconds) that will be allowed for the Serverless Function.
     */
    maxDuration?: number;
    /**
     * True if a custom runtime has support for Lambda runtime wrappers.
     */
    supportsWrapper?: boolean;
    /**
     * When true, the Serverless Function will stream the response to the client.
     */
    supportsResponseStreaming?: boolean;
    [key: string]: unknown;
}
export interface VercelOptions {
    config: VercelBuildConfigV3;
    /**
     * If you are using `vercel-edge`, you can specify the region(s) for your edge function.
     * @see https://vercel.com/docs/concepts/functions/edge-functions#edge-function-regions
     */
    regions?: string[];
    functions?: VercelServerlessFunctionConfig;
}
/**
 * https://vercel.com/docs/build-output-api/v3/primitives#prerender-configuration-file
 */
export type PrerenderFunctionConfig = {
    /**
     * Expiration time (in seconds) before the cached asset will be re-generated by invoking the Serverless Function. Setting the value to `false` means it will never expire.
     */
    expiration: number | false;
    /**
     * Option group number of the asset. Prerender assets with the same group number will all be re-validated at the same time.
     */
    group?: number;
    /** Random token assigned to the `__prerender_bypass` cookie when Draft Mode is enabled, in order to safely bypass the Edge Network cache */
    bypassToken?: string;
    /**
     * Name of the optional fallback file relative to the configuration file.
     */
    fallback?: string;
    /**
     * List of query string parameter names that will be cached independently. If an empty array, query values are not considered for caching. If undefined each unique query value is cached independently
     */
    allowQuery?: string[];
    /**
     * When `true`, the query string will be present on the `request` argument passed to the invoked function. The `allowQuery` filter still applies.
     */
    passQuery?: boolean;
};
