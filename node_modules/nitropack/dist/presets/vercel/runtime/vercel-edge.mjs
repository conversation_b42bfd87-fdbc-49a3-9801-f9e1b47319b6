import "#nitro-internal-pollyfills";
import { useNitroApp } from "nitropack/runtime";
const nitroApp = useNitroApp();
export default async function handleEvent(request, event) {
  const url = new URL(request.url);
  let body;
  if (request.body) {
    body = await request.arrayBuffer();
  }
  return nitroApp.localFetch(url.pathname + url.search, {
    host: url.hostname,
    protocol: url.protocol,
    headers: request.headers,
    method: request.method,
    body,
    context: {
      _platform: {
        vercel: {
          event
        }
      }
    }
  });
}
