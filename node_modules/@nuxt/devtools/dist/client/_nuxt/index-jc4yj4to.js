const l=/\d/,C=["-","_","/","."];function h(e=""){if(!l.test(e))return e!==e.toLowerCase()}function c(e,p){const i=C,n=[];if(!e||typeof e!="string")return n;let t="",s,a;for(const r of e){const u=i.includes(r);if(u===!0){n.push(t),t="",s=void 0;continue}const o=h(r);if(a===!1){if(s===!1&&o===!0){n.push(t),t=r,s=o;continue}if(s===!0&&o===!1&&t.length>1){const f=t.at(-1);n.push(t.slice(0,Math.max(0,t.length-1))),t=f+r,s=o;continue}}t+=r,s=o,a=u}return n.push(t),n}function y(e){return e?e[0].toUpperCase()+e.slice(1):""}function A(e,p){return e?(Array.isArray(e)?e:c(e)).map(i=>y(i)).join(""):""}function R(e,p){return e?(Array.isArray(e)?e:c(e)).map(i=>i.toLowerCase()).join("-"):""}export{R as k,A as p};
