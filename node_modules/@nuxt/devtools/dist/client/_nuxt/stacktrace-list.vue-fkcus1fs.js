import{_ as p}from"./filepath-item.vue-dx8apiq4.js";import{A as m}from"./czf9xkmw.js";import{p as f,J as r,a3 as n,F as s,ag as _,S as o,aa as h,V as d,U as g}from"./vendor/json-editor-vue-m9gzt21j.js";const k={mt2:"",grid:"~ cols-[max-content_1fr] gap-x-4","font-mono":""},x={"text-right":""},N={"ws-nowrap":""},B=f({__name:"StacktraceList",props:{stacktrace:{}},setup($){const i=m();function c(a){try{let t=new URL(a).pathname;return t.startsWith("/_nuxt/")&&(t=t.slice(6)),t.startsWith("/@id/virtual:nuxt:")?`#build/${t.split("/.nuxt/")[1]}`.replace(/\.m?js$/,""):t.includes("/@fs/")?`/${t.split("/@fs/")[1]}`:(i.value?.rootDir||"")+t}catch{return a}}return(a,t)=>{const l=p;return n(),r("div",k,[(n(!0),r(s,null,_(a.stacktrace,(e,u)=>(n(),r(s,{key:u},[o("div",x,h(e.functionName||"(anonymous)"),1),o("div",N,[e.fileName?(n(),d(l,{key:0,filepath:`${c(e.fileName)}:${e.lineNumber}:${e.columnNumber}`,subpath:""},null,8,["filepath"])):g("",!0)])],64))),128))])}}});export{B as _};
