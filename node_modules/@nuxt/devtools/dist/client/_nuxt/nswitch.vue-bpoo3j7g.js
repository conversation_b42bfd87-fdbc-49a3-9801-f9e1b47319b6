import{p as n,aq as o,ar as i,J as c,a3 as r,ac as u,S as l,W as h,as as b,Y as p}from"./vendor/json-editor-vue-m9gzt21j.js";const m=["checked","disabled"],v=["disabled"],k=n({__name:"NSwitch",props:o({disabled:{type:Boolean,default:!1}},{modelValue:{type:Boolean,default:!1},modelModifiers:{}}),emits:["update:modelValue"],setup(t){const s=i(t,"modelValue");return(a,e)=>(r(),c("label",{class:"n-switch n-switch-base hover:n-switch-hover n-disabled:n-disabled",checked:s.value||null,disabled:a.disabled||null},[u(l("input",{"onUpdate:modelValue":e[0]||(e[0]=d=>s.value=d),type:"checkbox",class:"peer absolute op0",disabled:a.disabled,onKeypress:e[1]||(e[1]=p(d=>s.value=!s.value,["enter"]))},null,40,v),[[b,s.value]]),e[2]||(e[2]=l("div",{class:"n-switch-slider n-transition n-checked:n-switch-slider-checked peer-active:n-active-base peer-focus-visible:n-focus-base"},[l("div",{class:"n-switch-thumb n-transition n-checked:n-switch-thumb-checked"})],-1)),h(a.$slots,"default")],8,m))}});export{k as _};
