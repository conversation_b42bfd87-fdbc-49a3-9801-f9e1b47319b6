import{G as $,I as B,b as C}from"./czf9xkmw.js";import{_}from"./nicon-title.vue-ejocqf9t.js";import{p as b,J as l,a3 as n,F as u,S as s,U as p,Z as r,a5 as h,u as i,a4 as w,W as t,V as N,ab as c,aa as f,ac as k,ad as S}from"./vendor/json-editor-vue-m9gzt21j.js";const V=["open"],x={"text-base":""},I={key:0,"text-sm":"",op50:""},T=b({__name:"NSectionBlock",props:{icon:{},text:{},description:{},containerClass:{default:""},headerClass:{},collapse:{type:Boolean,default:!0},open:{type:Boolean,default:!0},padding:{type:[Boolean,String],default:!0}},setup(v){const a=$(v,"open",void 0,{passive:!0});function d(e){a.value=e.target.open}return(e,o)=>{const m=B,g=_;return n(),l(u,null,[s("details",{open:i(a),onToggle:o[0]||(o[0]=(...y)=>d&&d(...y))},[s("summary",{class:r(["cursor-pointer select-none p4 hover:bg-active",e.collapse?"":"pointer-events-none"])},[h(g,{icon:e.icon,text:e.text,"text-xl":"",transition:"",class:r([i(a)?"op100":"op60",e.headerClass])},{default:w(()=>[s("div",null,[s("div",x,[t(e.$slots,"text",{},()=>[c(f(e.text),1)],!0)]),e.description||e.$slots.description?(n(),l("div",I,[t(e.$slots,"description",{},()=>[c(f(e.description),1)],!0)])):p("",!0)]),o[1]||(o[1]=s("div",{class:"flex-auto"},null,-1)),t(e.$slots,"actions",{},void 0,!0),e.collapse?(n(),N(m,{key:0,icon:"carbon-chevron-down",class:"chevron","cursor-pointer":"","place-self-start":"","text-base":"",op75:"",transition:"","duration-500":""})):p("",!0)]),_:3,__:[1]},8,["icon","text","class"])],2),o._lazyshow1||i(a)?(o._lazyshow1=!0,n(),l(u,null,[k(s("div",{class:r(["flex flex-col flex-gap2 pb6 pt2",typeof e.padding=="string"?e.padding:e.padding?"px4":""])},[t(e.$slots,"details",{},void 0,!0),s("div",{class:r([e.containerClass,"mt1"])},[t(e.$slots,"default",{},void 0,!0)],2),t(e.$slots,"footer",{},void 0,!0)],2),[[S,i(a)]])],64)):p("v-show-if",!0)],40,V),o[2]||(o[2]=s("div",{class:"x-divider"},null,-1))],64)}}}),G=C(T,[["__scopeId","data-v-95ac5573"]]);export{G as _};
