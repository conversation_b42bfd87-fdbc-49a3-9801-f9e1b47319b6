import{G as i,I as r}from"./czf9xkmw.js";import{p,J as b,a3 as u,ac as h,S as n,as as m,u as o,Y as k,j as f,a5 as v,W as _,Z as x}from"./vendor/json-editor-vue-m9gzt21j.js";const V=["checked","disabled"],y=["disabled"],B={class:"n-checkbox-box n-transition n-checked:n-checkbox-box-checked peer-active:n-active-base peer-focus-visible:n-focus-base"},I=p({__name:"NCheckbox",props:{modelValue:{type:[Boolean,null],default:!1},disabled:{type:Boolean,default:!1}},setup(c,{emit:t}){const e=i(c,"modelValue",t,{passive:!0});return(a,s)=>{const d=r;return u(),b("label",{class:"n-checkbox select-none items-center hover:n-checkbox-hover n-disabled:n-disabled",checked:o(e)||null,disabled:a.disabled||null},[h(n("input",{"onUpdate:modelValue":s[0]||(s[0]=l=>f(e)?e.value=l:null),type:"checkbox",class:"peer absolute op0",disabled:a.disabled,onKeypress:s[1]||(s[1]=k(l=>e.value=!o(e),["enter"]))},null,40,y),[[m,o(e)]]),n("span",B,[v(d,{class:"n-checkbox-icon scale-0 transform op0 n-transition n-checked:scale-100 n-checked:op100"})]),n("span",{class:x([o(e)?"":"op50","n-transition"])},[_(a.$slots,"default")],2)],8,V)}}});export{I as _};
