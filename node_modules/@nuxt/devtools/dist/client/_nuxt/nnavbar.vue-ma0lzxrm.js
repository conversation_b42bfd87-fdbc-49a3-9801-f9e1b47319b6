import{v as l}from"./czf9xkmw.js";import{p,J as i,a3 as s,Z as o,S as d,W as a,V as m,U as u}from"./vendor/json-editor-vue-m9gzt21j.js";const f={flex:"~ gap4 wrap","items-center":""},b=p({__name:"NNavbar",props:{search:{},noPadding:{type:Boolean}},emits:["update:search"],setup(h,{emit:n}){const r=n;function t(e){r("update:search",e.target.value)}return(e,v)=>{const c=l;return s(),i("div",{flex:"~ col gap2 wrap",border:"b base","n-navbar-glass":"","flex-1":"",class:o([{p4:!e.noPadding}])},[d("div",f,[a(e.$slots,"search",{},()=>[e.search!==void 0?(s(),m(c,{key:0,placeholder:"Search...",icon:"carbon-search",n:"primary","flex-auto":"",class:o({"px-3 py-2":!e.noPadding}),value:e.search,onInput:t},null,8,["class","value"])):u("",!0)]),a(e.$slots,"actions")]),a(e.$slots,"default")],2)}}});export{b as _};
