import{b6 as S,r as k,E as M,bl as P}from"./czf9xkmw.js";import{p as A,aD as R,E as L,M as C,J as U,a3 as T,u as x}from"./vendor/json-editor-vue-m9gzt21j.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";var H=class{listeners;constructor(){this.listeners=Object.create(null)}on(t,e){return this.listeners[t]={callback:e,next:this.listeners[t]||null},this}once(t,e){return this.on(t,function s(...a){e.apply(this,a),this.off(t,s)})}off(t,e){let s=this.listeners[t]||null,a=null;for(;s!==null;){if(s.callback===e){s.callback=null,a?a.next=s.next:this.listeners[t]=s.next;break}a=s,s=s.next}return this}emit(t,...e){let s=this.listeners[t]||null,a=!1;for(;s!==null;)typeof s.callback=="function"&&s.callback.apply(this,e),a=!0,s=s.next;return a}},u=class{subscriber;value;constructor(t,e){this.subscriber=null,this.value=t,this.shouldUpdate=typeof e=="function"?e:this.shouldUpdate}get readonly(){let t=this;return{subscribe:this.subscribe.bind(this),subscribeSync:this.subscribeSync.bind(this),unsubscribe:this.unsubscribe.bind(this),get value(){return t.value}}}subscribe(t){return this.subscriber={callback:t,subscriber:this.subscriber},()=>this.unsubscribe(t)}subscribeSync(t){let e=this.subscribe(t);return t(this.value,e),e}unsubscribe(t){let e=this,s=this.subscriber;for(;s!==null;){if(s.callback===t){s.callback=null,e.subscriber=s.subscriber;break}e=s,s=s.subscriber}}shouldUpdate(t,e){return t!==e}set(t){return this.#e(t)!==!1}asyncSet(t){let e=this.#e(t);return e===!1?Promise.resolve(!1):Promise.all(e).then(()=>!0)}#e(t){if(!this.shouldUpdate(t,this.value))return!1;let e=[],s=this.subscriber;for(this.value=t;s!==null;){let{callback:a}=s;a!==null&&e.push(a(t,()=>this.unsubscribe(a))),s=s.subscriber}return e}};function b(){return[performance.timeOrigin.toString(16),(1e4*performance.now()).toString(16),Math.random().toString(16).slice(2)].join("-")}new TextDecoder;var B=typeof Object.hasOwn=="function"?Object.hasOwn:(t,e)=>Object.hasOwnProperty.call(t,e),O={8:"\\b",9:"\\t",10:"\\n",12:"\\f",13:"\\r",34:'\\"',92:"\\\\"};Uint8Array.from({length:2048},(t,e)=>B(O,e)?2:e<32?6:e<128?1:2);new Uint8Array(256).map((t,e)=>{for(let s=0;s<8;s++)t+=e>>s&1;return t});new TextDecoder("utf8",{ignoreBOM:!0});function j(t){try{return new URL(t,location.origin).origin===location.origin}catch{return!1}}function z(t){return t.ok}function D(t){return t.headers.get("x-file-encoded-size")||t.headers.get("content-length")}function E(t){return t.headers.get("x-file-size")||(j(t.url)&&!t.headers.get("content-encoding")?t.headers.get("content-length"):void 0)}function N(t){return t.headers.get("x-file-created-at")||t.headers.get("last-modified")||void 0}function I(t,e){if(t instanceof Response){let s=e?.isResponseOk||z,a=e?.getContentSize||E,n=e?.getContentEncodedSize||D,r=e?.getContentCreatedAt||N;if(s(t))return{type:"url",name:t.url,size:Number(a(t))||null,encodedSize:Number(n(t)),createdAt:r(t)}}if(t instanceof File)return{type:"file",name:t.name,size:t.size,createdAt:t.lastModified};if(t instanceof Blob)return{size:t.size};if(ArrayBuffer.isView(t))return{size:t.byteLength};if(typeof t=="string")return{size:t.length}}function $(t){let e=t;return(typeof e=="string"||ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e&&!(Symbol.iterator in e)&&!(Symbol.asyncIterator in e))&&(e=[e]),e&&Symbol.iterator in e?new Blob(e):t}function _(t){if(t instanceof ReadableStream)return t;if(t instanceof Response){if(t.body===null)throw new Error("Response has no body");return t.body}return t=$(t),t instanceof Blob?t.stream():new ReadableStream({start(){let e=t!==null&&typeof t=="object"&&Symbol.asyncIterator in t?t[Symbol.asyncIterator]:void 0;if(typeof e!="function")throw new Error("Bad value type (can't convert to a stream)");this.iterator=e()},async pull(e){let{value:s,done:a}=await this.iterator.next();a?(this.iterator=null,e.close()):e.enqueue(s)},cancel(){this.iterator=null}})}function W(t,e){let s=globalThis.location,a=[],n=({newURL:r,oldURL:i})=>{let o=new URL(r).hash||"#",c=new URL(i).hash||"#";o!==a.shift()&&(a.length=0,t(o,c))};return addEventListener("hashchange",n),{set(r,i){let o=r||"#";(s.hash||"#")!==o&&(a.push(r),i?s.replace(r):s.hash=r)},dispose(){removeEventListener("hashchange",n)}}}var p="[Discovery/embed-host]",g=()=>{},J=(()=>{try{let t=new ReadableStream;return new MessageChannel().port1.postMessage(t,[t]),!0}catch{return!1}})(),m=class extends H{window;id;actions;dataLoadToken;constructor(t,e,s){super(),this.window=t,this.id=e,this.actions=s,this.dataLoadToken=null}sendMessage(t,e,s){let a={id:this.id,from:"discoveryjs-app",type:t,payload:e||null};this.window.postMessage(a,"*",s)}destroy(){this.destroy=g,this.emit("destroy"),this.dataLoadToken=null,this.window=null,this.sendMessage=g}},V=class y extends m{publicApi;static createPublicApi(e){return Object.freeze({on:e.on.bind(e),once:e.once.bind(e),off:e.off.bind(e),defineAction(s,a){e.actions.set(s,a),e.sendMessage("defineAction",s)},setPageHash(s,a=!1){e.sendMessage("setPageHash",{hash:s,replace:a})},setRouterPreventLocationUpdate(s=!0){e.sendMessage("setRouterPreventLocationUpdate",s)}})}constructor(e,s,a){super(e,s,a),this.publicApi=y.createPublicApi(this)}processMessage(e){switch(e.type){case"loadingState":{this.emit("loadingStateChanged",e.payload);break}}}},q=class w extends m{commandMap;dataLoadToken;pageHash;pageId;pageRef;pageParams;pageAnchor;locationSync;colorScheme;publicApi;static createPublicApi(e){let s={primary:f("primary",e.sendMessage.bind(e),e.commandMap),secondary:f("secondary",e.sendMessage.bind(e),e.commandMap),menu:f("menu",e.sendMessage.bind(e),e.commandMap)};return Object.freeze({pageHash:e.pageHash.readonly,pageId:e.pageId.readonly,pageRef:e.pageRef.readonly,pageAnchor:e.pageAnchor.readonly,pageParams:e.pageParams.readonly,colorScheme:e.colorScheme.readonly,on:e.on.bind(e),once:e.once.bind(e),off:e.off.bind(e),nav:Object.assign(s.secondary,s),notify(a,n){e.sendMessage("notification",{name:a,details:n})},defineAction(a,n){e.actions.set(a,n),e.sendMessage("defineAction",a)},setPageHash(a,n=!1){e.sendMessage("setPageHash",{hash:a,replace:n})},setPageHashState(a,n=!1){e.sendMessage("setPageHashState",{...a,replace:n})},setPageHashStateWithAnchor(a,n=!1){e.sendMessage("setPageHashStateWithAnchor",{...a,replace:n})},setPage(a,n,r,i=!1){e.sendMessage("setPage",{id:a,ref:n,params:r,replace:i})},setPageRef(a,n=!1){e.sendMessage("setPageRef",{ref:a,replace:n})},setPageParams(a,n=!1){e.sendMessage("setPageParams",{params:a,replace:n})},setPageAnchor(a,n=!1){e.sendMessage("setPageAnchor",{anchor:a,replace:n})},setColorSchemeState(a){e.sendMessage("setColorSchemeState",a)},setRouterPreventLocationUpdate(a=!0){e.sendMessage("setRouterPreventLocationUpdate",a)},setLocationSync(a=!0){a&&!e.locationSync?(e.locationSync=W(n=>e.publicApi.setPageHash(n)),e.on("pageHashChanged",e.locationSync.set)):!a&&e.locationSync&&(e.off("pageHashChanged",e.locationSync.set),e.locationSync.dispose(),e.locationSync=null)},unloadData(){e.sendMessage("unloadData",null)},async uploadData(a,n){let r=b();e.dataLoadToken=r;try{return await G(e,a,n)}finally{e.dataLoadToken===r&&(e.dataLoadToken=null)}}})}constructor(e,s,a){super(e,s,a),this.commandMap=new Map,this.dataLoadToken=null,this.pageHash=new u("#"),this.pageId=new u(""),this.pageRef=new u(null),this.pageParams=new u({}),this.pageAnchor=new u(null),this.locationSync=null,this.colorScheme=new u({state:"unknown",value:"unknown"},(n,r)=>n.state!==r.state||n.value!==r.value),this.publicApi=w.createPublicApi(this)}async processMessage(e){switch(e.type){case"destroy":{this.destroy();break}case"action":{let{callId:s,name:a,args:n}=e.payload,r=this.actions.get(a);if(typeof r=="function")try{this.sendMessage("actionResult",{callId:s,value:await r(...n)})}catch(i){this.sendMessage("actionResult",{callId:s,error:i})}else console.warn(`${p} Action "${a}" was not found`);break}case"navMethod":{let s=this.commandMap.get(e.payload);typeof s=="function"?s():console.warn(`${p} Nav command "${e.payload}" was not found`);break}case"pageHashChanged":{let{replace:s,hash:a,id:n,ref:r,params:i,anchor:o}=e.payload||{},c=String(a).startsWith("#")?a:"#"+a;this.pageHash.set(c),this.pageId.set(n),this.pageRef.set(r),this.pageParams.set(i),this.pageAnchor.set(o),this.emit("pageHashChanged",c,s);break}case"colorSchemeChanged":{let s=e.payload;this.colorScheme.set(s),this.emit("colorSchemeChanged",s);break}case"unloadData":{this.emit("unloadData");break}case"data":{this.emit("data");break}case"loadingState":{this.emit("loadingStateChanged",e.payload);break}default:console.error(`${p} Unknown embed message type "${e.type}"`)}}destroy(){this.locationSync&&(this.locationSync.dispose(),this.locationSync=null),super.destroy()}};function F(t,e,s){let a=Object.assign(new Map,{id:""}),n=null,r,i=typeof e=="function"&&typeof s!="function"?{onPreinit:void 0,onConnect:e}:{onPreinit:e,onConnect:s};return addEventListener("message",c),()=>{removeEventListener("message",c),o()};function o(){n!==null&&(n.destroy(),typeof r=="function"&&r(),n=null,r=void 0)}async function c(d){let l=d.data||{};if(d.isTrusted&&(d.source===t.contentWindow||d.source===null)&&l.from==="discoveryjs-app"){if(l.type==="ready"){o(),a.id!==l.id&&(a.clear(),a.id=l.id);let{colorScheme:v,page:h}=l.payload;n=new q(t.contentWindow,l.id,a),n.pageHash.set(h.hash),n.pageId.set(h.id),n.pageRef.set(h.ref),n.pageParams.set(h.params),n.pageAnchor.set(h.anchor),n.colorScheme.set(v),n.once("destroy",o),r=i.onConnect(n.publicApi);return}if(l.type==="preinit"){o(),typeof i.onPreinit=="function"&&(a.id!==l.id&&(a.clear(),a.id=l.id),n=new V(t.contentWindow,l.id,a),n.once("destroy",o),r=i.onPreinit(n.publicApi));return}if(n?.id===l.id){n.processMessage(l);return}}}}async function G(t,e,s=I){let a=t.dataLoadToken,n=()=>{if(t?.dataLoadToken!==a)throw new Error("Data upload aborted")};if(!a)throw new Error("No acceptToken specified");let r=typeof e=="function"?await e():await e;n();let i=typeof s=="function"?s(r)||{}:{},o=_(r);if(J)t.sendMessage("dataStream",{stream:o,resource:i},[o]);else{let c=o.getReader();t.sendMessage("startChunkedDataUpload",{acceptToken:a,resource:i});try{for(;;){let{value:d,done:l}=await c.read();if(n(),t.sendMessage("dataChunk",{acceptToken:a,value:d,done:l},typeof d!="string"&&d?.buffer?[d.buffer]:void 0),l)break}}catch(d){throw t.sendMessage("cancelChunkedDataUpload",{acceptToken:a,error:d}),d}finally{c.releaseLock()}}}function f(t,e,s){function a(n){let r=[];return{commands:r,config:JSON.parse(JSON.stringify(n,(i,o)=>{if(typeof o=="function"){let c="nav-command-"+b();return r.push(c),s.set(c,o),c}return o}))}}return{insert(n,r,i){e("changeNavButtons",{section:t,action:"insert",name:i,position:r,...a(n)})},prepend(n){e("changeNavButtons",{section:t,action:"prepend",...a(n)})},append(n){e("changeNavButtons",{section:t,action:"append",...a(n)})},before(n,r){e("changeNavButtons",{section:t,action:"before",name:n,...a(r)})},after(n,r){e("changeNavButtons",{section:t,action:"after",name:n,...a(r)})},replace(n,r){e("changeNavButtons",{section:t,action:"replace",name:n,...a(r)})},remove(n){e("changeNavButtons",{section:t,action:"remove",name:n})}}}const Z=["src"],ee=A({__name:"server-discovery",setup(t){const s=S().app.baseURL,a=R("iframe");return L(()=>{const n=F(a.value,r=>((async()=>{const i=await k.getServerData(await M()),o=`{${Object.entries(i).map(([c,d])=>`"${c}": ${P(d)}`).join(",")}}`;r.uploadData(o)})(),()=>{}));C(()=>{n()})}),(n,r)=>(T(),U("iframe",{ref_key:"iframe",ref:a,src:`${x(s)}discovery/index.html`,"h-full":"","w-full":""},null,8,Z))}});export{ee as default};
