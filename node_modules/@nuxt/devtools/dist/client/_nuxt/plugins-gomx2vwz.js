import{_ as T}from"./nbadge-bu0b8pjx.js";import{_ as w}from"./filepath-item.vue-dx8apiq4.js";import{_ as k}from"./duration-display.vue-mr4ha0rw.js";import{A as D,a3 as B,I as V,b as I,x as F,f as H}from"./czf9xkmw.js";import{p as v,q as h,J as _,a3 as s,S as n,a5 as c,U as d,aa as C,a4 as f,V as p,$ as W,W as P,ab as x,F as b,ag as E,Z as A}from"./vendor/json-editor-vue-m9gzt21j.js";import{_ as L}from"./nsection-block-m4vpsvnn.js";import{_ as M}from"./help-fab.vue-b6h1gmzk.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";import"./nicon-title.vue-ejocqf9t.js";const j={flex:"~ gap2","items-center":""},q={w8:"","text-right":"","text-sm":"",op25:""},z={key:0,"flex-auto":"","text-right":""},J=v({__name:"PluginItem",props:{plugin:{},index:{}},setup(m){const e=m,r=D(),a=h(()=>B(e.plugin.src,r.value?.rootDir||"").path);return(t,l)=>{const o=T,u=w,i=k;return s(),_("div",j,[n("div",q,C(t.index),1),c(u,{filepath:e.plugin.src,subpath:!0},{default:f(()=>[n("div",null,[a.value.startsWith("#")?(s(),p(o,{key:0,n:"rose",textContent:"virtual"})):a.value.startsWith(".")?d("",!0):(s(),p(o,{key:1,n:"gray",textContent:"module"})),t.plugin.mode==="server"?(s(),p(o,{key:2,n:"teal",textContent:"server"})):d("",!0),t.plugin.mode==="client"?(s(),p(o,{key:3,n:"orange",textContent:"client"})):d("",!0)])]),_:1},8,["filepath"]),t.plugin.metric?.duration!=null?(s(),_("div",z,[c(i,{duration:t.plugin.metric?.duration,factor:10},null,8,["duration"])])):d("",!0)])}}}),R={class:"n-tip n-tip-base",flex:"~ col items-start","mx--4":"",px4:"",py4:""},U={flex:"~ items-center gap-1","font-bold":""},Y=v({__name:"HelpTip",props:{title:{},icon:{}},setup(m){return(e,r)=>{const a=V,t=W("ContentSlot");return s(),_("div",R,[n("div",U,[e.icon?(s(),p(a,{key:0,icon:e.icon,class:"n-tip-icon"},null,8,["icon"])):d("",!0),n("div",null,C(e.title),1)]),n("div",null,[P(e.$slots,"default",{},()=>[c(t,{use:e.$slots.default,unwrap:"p"},null,8,["use"])])])])}}}),Z={};function G(m,e){const r=Y;return s(),p(r,{title:"Performance Tip",icon:"carbon-meter",n:"lime6 dark:lime5"},{default:f(()=>[P(m.$slots,"default")]),_:3})}const K=I(Z,[["render",G]]),O={class:"markdown-body"},Q={__name:"plugins",setup(m,{expose:e}){return e({frontmatter:{}}),(a,t)=>{const l=K;return s(),_("div",O,[t[1]||(t[1]=n("h1",null,"Plugins",-1)),t[2]||(t[2]=n("p",null,[x("Nuxt plugins allow you to extend the functionality of the Nuxt runtime and the Vue instance. You can add plugins to the "),n("code",null,"plugins/"),x(" directory and they will be automatically imported and registered.")],-1)),c(l,null,{default:f(()=>t[0]||(t[0]=[x(" Plugins always run before your application's runtime. The loading time of each plugin will directly impact your application's initial loading time. ")])),_:1,__:[0]}),t[3]||(t[3]=n("p",null,[n("a",{href:"https://nuxt.com/docs/guide/directory-structure/plugins",target:"_blank",rel:"noopener"},"Learn more in the documentation")],-1))])}}},X={pt4:""},tt={class:"text-sm",flex:"~ gap-1 items-center justify-start","mt-3":""},pt=v({__name:"plugins",setup(m){const e=F(),r=H(),a=h(()=>{const l=e.value?.plugins||[],o=r.value?.metrics.clientPlugins()||[];return l.map(u=>{const i=typeof u=="string"?{src:u}:u;return{...i,metric:o.find(g=>g.src===i.src||g.src.startsWith(i.src))}})}),t=h(()=>{const l=r.value?.metrics.clientPlugins()||[],o=Math.min(...l.map(i=>i.start));return Math.max(...l.map(i=>i.end))-o});return(l,o)=>{const u=J,i=k,g=L,N=Q,S=M;return s(),_(b,null,[c(g,{icon:"carbon-plug",text:"Plugins",description:`Total plugins: ${a.value.length}`},{default:f(()=>[n("div",X,[(s(!0),_(b,null,E(a.value,(y,$)=>(s(),p(u,{key:y.src,plugin:y,index:$+1,"ml--4":"","border-base":"",py2:"",class:A($?"border-t":"")},null,8,["plugin","index","class"]))),128)),n("div",tt,[o[0]||(o[0]=n("div",{"i-carbon-timer":"","text-lg":"",op75:""},null,-1)),o[1]||(o[1]=n("span",{op50:""},"Total execution time:",-1)),c(i,{duration:t.value,factor:10},null,8,["duration"])])])]),_:1},8,["description"]),c(S,null,{default:f(()=>[c(N)]),_:1})],64)}}});export{pt as default};
