import{_ as h}from"./nicon-title.vue-ejocqf9t.js";import{_ as ee}from"./nswitch.vue-bpoo3j7g.js";import{g as j,f as le,k as oe,l as ne,m as te,_ as ae,n as se,t as A,r as ie,o as ue}from"./czf9xkmw.js";import{_ as de}from"./nselect.vue-heccp04i.js";import{_ as re}from"./ncheckbox.vue-nc9ppn5r.js";import{_ as pe}from"./nlink.vue-d4id0o0s.js";import{p as me,w as P,J as m,a3 as d,a5 as n,S as o,F as v,ag as k,u as t,a4 as a,ab as N,aa as _,j as p,V as F,U as M,Z as R}from"./vendor/json-editor-vue-m9gzt21j.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";const fe={px8:"",py6:""},ve={grid:"~ lg:cols-2 gap-x-10 gap-y-3","max-w-300":""},ge={flex:"~ col gap-2"},be={flex:"~ gap-2","flex-auto":"","items-center":"","justify-start":""},_e={capitalize:"",op75:""},xe={"flex-auto":"","overflow-hidden":"","text-ellipsis":"","ws-nowrap":""},ye={flex:"~ col gap-2"},ce=["value"],Ve=["value"],ke=["value"],Ce={flex:"~ gap-2"},we={flex:"~ gap-2"},Be=me({__name:"settings",setup(Se){const{interactionCloseOnOutsideClick:C,showPanel:I,showHelpButtons:$,scale:z,hiddenTabs:x,pinnedTabs:u,hiddenTabCategories:y,minimizePanelInactive:w,sidebarExpanded:S,sidebarScrollable:D}=j("ui"),{openInEditor:E}=j("behavior"),c=le(),W=[["Auto",void 0],["VS Code","vscode"],["VS Code Insider","vscode-insider"],["Cursor","cursor"],["Zed","zed"],["WebStorm","webstorm"],["Sublime Text","sublime"],["Atom","atom"],["Windsurf","windsurf"]],H=[["Tiny",12/15],["Small",14/15],["Normal",1],["Large",16/15],["Huge",18/15]],Z=[["Always",0],["1s",1e3],["2s",2e3],["5s",5e3],["10s",1e4],["Never",-1]],J=oe(ne());function q(s,e){e?x.value=x.value.filter(r=>r!==s):x.value.push(s)}function G(s,e){e?y.value=y.value.filter(r=>r!==s):y.value.push(s)}function K(s){u.value.includes(s)?u.value=u.value.filter(e=>e!==s):u.value.push(s)}function L(s,e){const r=u.value.indexOf(s);if(r===-1)return;const g=r+e;if(g<0||g>=u.value.length)return;const V=[...u.value];V.splice(r,1),V.splice(g,0,s),u.value=V}async function Q(){confirm("Are you sure you to reset all local settings & state? The app will reload.")&&(Object.keys(localStorage).forEach(s=>{s.startsWith("nuxt-devtools-")&&localStorage.removeItem(s)}),await ie.clearOptions(),c.value?.app?.reload?.(),window.location.reload())}return P(()=>{c.value&&(c.value.app.frameState.value.closeOnOutsideClick=C.value)}),P(()=>{c.value&&(c.value.app.frameState.value.minimizePanelInactive=w.value)}),(s,e)=>{const r=h,g=ee,V=ue,f=ae,T=se,X=te,B=de,b=re,Y=pe;return d(),m("div",fe,[n(r,{class:"mb-5 text-xl op75",icon:"i-carbon-settings-adjust",text:"DevTools Settings"}),o("div",ve,[o("div",ge,[e[10]||(e[10]=o("h3",{"text-lg":""}," Tabs ",-1)),(d(!0),m(v,null,k(t(J),([l,O])=>(d(),m(v,{key:l},[O.length?(d(),F(T,{key:0,p3:"",flex:"~ col gap-1",class:R(t(y).includes(l)?"op50 grayscale":"")},{default:a(()=>[n(g,{flex:"~ row-reverse",py1:"",pl2:"",pr1:"","n-lime":"","model-value":!t(y).includes(l),"onUpdate:modelValue":i=>G(l,i)},{default:a(()=>[o("div",be,[o("span",_e,_(l),1)])]),_:2},1032,["model-value","onUpdate:modelValue"]),e[9]||(e[9]=o("div",{"mx--1":"",my1:"","h-1px":"",border:"b base",op75:""},null,-1)),(d(!0),m(v,null,k(O,i=>(d(),F(g,{key:i.name,flex:"~ row-reverse",py1:"",pl2:"",pr1:"","n-primary":"","model-value":!t(x).includes(i.name),"onUpdate:modelValue":U=>q(i.name,U)},{default:a(()=>[o("div",{flex:"~ gap-2","flex-auto":"","items-center":"","justify-start":"","of-hidden":"","pr-4":"",class:R(t(x).includes(i.name)?"op25":"")},[n(V,{"text-xl":"",icon:i.icon,title:i.title},null,8,["icon","title"]),o("span",xe,_(i.title),1),t(u).includes(i.name)?(d(),m(v,{key:0},[n(f,{icon:"i-carbon-caret-up",disabled:t(u).indexOf(i.name)===0,border:!1,onClick:U=>L(i.name,-1)},null,8,["disabled","onClick"]),n(f,{icon:"i-carbon-caret-down",disabled:t(u).indexOf(i.name)===t(u).length-1,border:!1,onClick:U=>L(i.name,1)},null,8,["disabled","onClick"])],64)):M("",!0),n(f,{icon:t(u).includes(i.name)?" i-carbon-pin-filled rotate--45":" i-carbon-pin op50",border:!1,onClick:U=>K(i.name)},null,8,["icon","onClick"])],2)]),_:2},1032,["model-value","onUpdate:modelValue"]))),128))]),_:2,__:[9]},1032,["class"])):M("",!0)],64))),128))]),o("div",ye,[e[29]||(e[29]=o("h3",{"text-lg":""}," Appearance ",-1)),n(T,{p4:"",flex:"~ col gap-2"},{default:a(()=>[o("div",null,[n(X,null,{default:a(({toggle:l,isDark:O})=>[n(f,{n:"primary",onClick:l},{default:a(()=>[e[11]||(e[11]=o("div",{"i-carbon-sun":"","dark:i-carbon-moon":"","translate-y--1px":""},null,-1)),N(" "+_(O.value?"Dark":"Light"),1)]),_:2,__:[11]},1032,["onClick"])]),_:1})]),e[14]||(e[14]=o("div",{"mx--2":"",my1:"","h-1px":"",border:"b base",op75:""},null,-1)),e[15]||(e[15]=o("p",null,"UI Scale",-1)),n(B,{modelValue:t(z),"onUpdate:modelValue":e[0]||(e[0]=l=>p(z)?z.value=l:null),n:"primary"},{default:a(()=>[(d(),m(v,null,k(H,l=>o("option",{key:l[0],value:l[1]},_(l[0]),9,ce)),64))]),_:1},8,["modelValue"]),e[16]||(e[16]=o("div",{"mx--2":"",my1:"","h-1px":"",border:"b base",op75:""},null,-1)),n(b,{modelValue:t(S),"onUpdate:modelValue":e[1]||(e[1]=l=>p(S)?S.value=l:null),"n-primary":""},{default:a(()=>e[12]||(e[12]=[o("span",null," Expand Sidebar ",-1)])),_:1,__:[12]},8,["modelValue"]),n(b,{modelValue:t(D),"onUpdate:modelValue":e[2]||(e[2]=l=>p(D)?D.value=l:null),disabled:t(S),"n-primary":""},{default:a(()=>e[13]||(e[13]=[o("span",null," Scrollable Sidebar ",-1)])),_:1,__:[13]},8,["modelValue","disabled"])]),_:1,__:[14,15,16]}),e[30]||(e[30]=o("h3",{mt2:"","text-lg":""}," Features ",-1)),n(T,{p4:"",flex:"~ col gap-2"},{default:a(()=>[n(b,{modelValue:t(C),"onUpdate:modelValue":e[3]||(e[3]=l=>p(C)?C.value=l:null),"n-primary":""},{default:a(()=>e[17]||(e[17]=[o("span",null,"Close DevTools when clicking outside",-1)])),_:1,__:[17]},8,["modelValue"]),n(b,{modelValue:t($),"onUpdate:modelValue":e[4]||(e[4]=l=>p($)?$.value=l:null),"n-primary":""},{default:a(()=>e[18]||(e[18]=[o("span",null,"Show help buttons",-1)])),_:1,__:[18]},8,["modelValue"]),n(b,{modelValue:t(I),"onUpdate:modelValue":e[5]||(e[5]=l=>p(I)?I.value=l:null),"n-primary":""},{default:a(()=>e[19]||(e[19]=[o("span",null,"Show the floating panel",-1)])),_:1,__:[19]},8,["modelValue"]),e[20]||(e[20]=o("div",{"mx--2":"",my1:"","h-1px":"",border:"b base",op75:""},null,-1)),e[21]||(e[21]=o("p",null,"Minimize floating panel on inactive",-1)),n(B,{modelValue:t(w),"onUpdate:modelValue":e[6]||(e[6]=l=>p(w)?w.value=l:null),modelModifiers:{number:!0},"n-primary":""},{default:a(()=>[(d(),m(v,null,k(Z,l=>o("option",{key:l[0],value:l[1]},_(l[0]),9,Ve)),64))]),_:1},8,["modelValue"]),e[22]||(e[22]=o("div",{"mx--2":"",my1:"","h-1px":"",border:"b base",op75:""},null,-1)),e[23]||(e[23]=o("p",null,"Open In Editor",-1)),n(B,{modelValue:t(E),"onUpdate:modelValue":e[7]||(e[7]=l=>p(E)?E.value=l:null),"n-primary":""},{default:a(()=>[(d(),m(v,null,k(W,l=>o("option",{key:l[0],value:l[1]},_(l[0]),9,ke)),64))]),_:1},8,["modelValue"])]),_:1,__:[20,21,22,23]}),e[31]||(e[31]=o("h3",{mt2:"","text-lg":""}," Feedback ",-1)),n(T,{p4:"",flex:"~ col gap-2"},{default:a(()=>[n(b,{modelValue:t(A),"onUpdate:modelValue":e[8]||(e[8]=l=>p(A)?A.value=l:null),"n-primary":""},{default:a(()=>[e[24]||(e[24]=o("span",null,"Send anonymous statistics, help us improving DevTools",-1)),n(Y,{href:"https://github.com/nuxt/devtools#anonymous-usage-analytics",target:"_blank",ml1:"",op50:"",textContent:"Learn more"})]),_:1,__:[24]},8,["modelValue"]),e[27]||(e[27]=o("div",{"mx--2":"",my1:"","h-1px":"",border:"b base",op75:""},null,-1)),o("div",Ce,[n(f,{n:"blue",to:"https://github.com/nuxt/devtools/discussions/29",target:"_blank"},{default:a(()=>e[25]||(e[25]=[o("div",{"i-carbon-data-enrichment":""},null,-1),N(" Ideas & Suggestions ")])),_:1,__:[25]}),n(f,{n:"orange",to:"https://github.com/nuxt/devtools/issues",target:"_blank"},{default:a(()=>e[26]||(e[26]=[o("div",{"i-carbon-debug":""},null,-1),N(" Bug Reports ")])),_:1,__:[26]})])]),_:1,__:[27]}),e[32]||(e[32]=o("h3",{mt2:"","text-lg":""}," Debug ",-1)),o("div",we,[n(f,{n:"orange",onClick:Q},{default:a(()=>e[28]||(e[28]=[o("div",{"i-carbon-breaking-change":""},null,-1),N(" Reset Local Settings & State ")])),_:1,__:[28]})])])])])}}});export{Be as default};
