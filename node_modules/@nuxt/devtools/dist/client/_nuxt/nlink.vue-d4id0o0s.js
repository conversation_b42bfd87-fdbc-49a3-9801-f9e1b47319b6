import{e as s}from"./czf9xkmw.js";import{p as l,q as i,V as p,a3 as r,a6 as u,a4 as c,W as m,J as _,U as k}from"./vendor/json-editor-vue-m9gzt21j.js";const d={key:0,"i-carbon:arrow-up-right":"","translate-y--1":"","text-xs":"",op50:""},v=l({__name:"NLink",props:{to:{},href:{},target:{},underline:{type:Boolean}},setup(a){const t=a,n=i(()=>t.href||t.to);return(e,f)=>{const o=s;return r(),p(o,u(n.value?{href:n.value,target:e.target,rel:e.target==="_blank"?"noopener noreferrer":null}:{},{class:{"n-link n-transition hover:n-link-hover n-link-base":n.value||e.underline}}),{default:c(()=>[m(e.$slots,"default"),n.value&&e.target==="_blank"?(r(),_("div",d)):k("",!0)]),_:3},16,["class"])}}});export{v as _};
