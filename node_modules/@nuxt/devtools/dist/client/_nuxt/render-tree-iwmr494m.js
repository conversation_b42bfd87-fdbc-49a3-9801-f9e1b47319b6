import{f as m,w as d,F as f}from"./czf9xkmw.js";import{u as c,f as _}from"./vue-devtools-d89ywfb1.js";import{p as v,J as C,a3 as n,V as a,u as o,a4 as E,ab as I}from"./vendor/json-editor-vue-m9gzt21j.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";import"./vue-virtual-scroller.esm-k5helfir.js";const k={class:"h-full w-full"},N=v({__name:"render-tree",setup(x){const{connected:l}=c(),t=m(),p=d();function s(r){r?t.value.devtools.open():t.value.devtools.close()}return(r,e)=>{const i=f;return n(),C("div",k,[o(l)?(n(),a(o(_),{key:0,onOnInspectComponentStart:e[0]||(e[0]=u=>s(!1)),onOnInspectComponentEnd:e[1]||(e[1]=u=>s(!0)),onOpenInEditor:o(p)},null,8,["onOpenInEditor"])):(n(),a(i,{key:1},{default:E(()=>e[2]||(e[2]=[I(" Connecting.... ")])),_:1,__:[2]}))])}}});export{N as default};
