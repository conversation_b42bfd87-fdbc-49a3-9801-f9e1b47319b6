import{P as j,H,am as Y,b as U,a4 as q,a as X,_ as z,V as Z,f as K,an as Q,A as ee,aj as se,w as ne,i as oe,Q as te,r as R,J as ae}from"./czf9xkmw.js";import{_ as W}from"./duration-display.vue-mr4ha0rw.js";import{g as M}from"./color-mt9xuhgy.js";import{p as B,q as w,J as i,a3 as t,X as T,S as o,aa as m,u as g,F as f,ag as E,V as v,k as C,x as le,z as re,U as x,a5 as u,Z as O,$ as G,a4 as h,av as ie,a6 as pe,ab as F,am as ce}from"./vendor/json-editor-vue-m9gzt21j.js";import{_ as P}from"./nbadge-bu0b8pjx.js";import{_ as de}from"./composable-item.vue-olv2xw90.js";import{_ as ue}from"./stacktrace-list.vue-fkcus1fs.js";import{_ as me}from"./ndrawer.vue-dr3o1y9k.js";import{_ as ke}from"./launch-page.vue-k0b466z9.js";import{_ as ye}from"./nlink.vue-d4id0o0s.js";import{_ as fe}from"./code-diff.vue-m3dd16hs.js";import{_ as ve}from"./help-fab.vue-b6h1gmzk.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";import"./nmarkdown.vue-mk2gi3ky.js";import"./client-oeqdl4pb.js";import"./filepath-item.vue-dx8apiq4.js";import"./index-jc4yj4to.js";import"./constants-b32h69zq.js";import"./ncode-block.vue-ctkw4rc7.js";const he=B({__name:"TimelineItemFunction",props:{item:{}},setup(y){const d=y,p=w(()=>M(d.item.name,50,60,"_op_")),n=w(()=>p.value.replace(/_op_/,"1")),e=w(()=>M(d.item.name,50,40)),l=w(()=>p.value.replace(/_op_/,"0.2"));return(a,r)=>(t(),i("button",{class:"group",style:T({color:n.value,borderLeft:`2px solid ${n.value}`}),relative:"","bg-base":"","text-sm":"",transition:"","hover:z-1000":""},[o("div",{style:T({backgroundColor:l.value}),absolute:"","bottom-0":"","left--1px":"","top-0":"","w-full":"","text-sm":"","transition-all":"","duration-300":""},null,4),o("div",{style:T({color:e.value,"--c":n.value}),border:"r-2 t-2 y-2 transparent","min-w-max":"",px1:"","text-left":"","group-hover":"border-$c"},m(a.item.name),5)],4))}}),_e={relative:"","h-full":""},be={absolute:"","left-2":"","top-2.3em":"","text-xs":"",op50:""},ge=B({__name:"TimelineSegment",props:{segment:{}},emits:["select"],setup(y,{emit:d}){const p=y,n=d,e=j(()=>p.segment.start,{updateInterval:1e3,showSecond:!0,controls:!1,messages:{justNow:"",past:l=>l,future:l=>l,invalid:"-",second:l=>l?`${l}s`:"",minute:l=>`${l}m`,hour:l=>`${l}h`,week:l=>`${l}w`,day:l=>`${l}d`,month:l=>`${l}mo`,year:l=>`${l}y`}});return(l,a)=>{const r=he;return t(),i("div",_e,[o("button",{absolute:"","left-0":"","right-0":"","top-0":"","bg-green:5":"",px2:"",py1:"","text-left":"","text-xs":"","text-green6":"","font-mono":"",onClick:a[0]||(a[0]=s=>l.segment.route?n("select",l.segment.route):void 0)},m(l.segment.route?.event.to),1),o("div",be,m(g(e))+" ago ",1),o("div",null,[(t(!0),i(f,null,E(l.segment.functions,(s,c)=>(t(),v(r,{key:c,item:s.event,style:T({position:"absolute",minWidth:`${s.relativeWidth*100}%`,maxWidth:`${s.relativeWidth*100}%`,top:`${4+s.layer*1.6}em`,left:`${s.relativeStart*100}%`}),onClick:k=>n("select",s)},null,8,["item","style","onClick"]))),128))])])}}}),xe=3e3,De=50;function Ae(y){const d=[];let p={start:0,end:0,events:[],functions:[],duration:0};for(const n of y){const e=n.end||n.start;(n.start-p.end>xe||n.type==="route")&&(p={start:n.start,end:e,events:[],functions:[],duration:0},d.push(p)),p.events.push(n),p.end=e+De}return d.forEach((n,e)=>{const l=n.end-n.start,a=[];n.duration=l,n.previousGap=e>0?n.start-d[e-1].end:0,n.events.forEach(r=>{const s=r.end||r.start;let c=0;r.type!=="route"&&(c=a.findIndex(D=>D<=r.start),c===-1?(c=a.length,a.push(s+1e3)):a[c]=s+1e3);const k={event:r,segment:n,relativeStart:(r.start-n.start)/l,relativeWidth:(s-r.start)/l,layer:c};r.type==="function"?n.functions.push(k):n.route=k})}),d}const $e={relative:""},Ce={key:0,border:"x base","h-full":"","flex-inline":"","bg-true-gray-1":"",py15:"","text-xs":"","write-vertical-left":"",op50:"","dark:bg-true-gray-9":""},Be=B({__name:"TimelineTable",props:{data:{}},emits:["select"],setup(y,{emit:d}){const p=y,n=d,e=C(),l=C(),a=C(),r=C(),s=C(!0),c=C(1.5),k=w(()=>Ae(p.data.events)),D=w(()=>(p.data.events.length,e.value?.scrollWidth||window.innerWidth));function A(){r.value&&(r.value.style.width=`${D.value}px`)}function S(){s.value&&e.value&&(e.value.scrollTo({left:e.value.scrollWidth-e.value.clientWidth,behavior:"smooth"}),a.value.scrollTo({left:e.value.scrollWidth-e.value.clientWidth,behavior:"smooth"}))}return le(()=>p.data.events.length,async()=>{await re(),A(),S()},{flush:"post"}),H(e,"scroll",()=>{a.value.scrollLeft!==e.value.scrollLeft&&(A(),a.value.scrollLeft=e.value.scrollLeft,s.value=e.value.scrollLeft>=e.value.scrollWidth-e.value.clientWidth)}),H(a,"scroll",()=>{a.value.scrollLeft!==e.value.scrollLeft&&(A(),e.value.scrollLeft=a.value.scrollLeft)}),H(e,"wheel",_=>{_.altKey?(c.value=Y(c.value+_.deltaY/200,.5,3),A()):e.value.scrollLeft+=_.deltaY}),(_,L)=>{const $=W,V=ge;return t(),i(f,null,[o("div",$e,[o("div",{ref_key:"minimap",ref:l,border:"t b base",relative:"","h-50px":"","ws-nowrap":"","border-base":""},[(t(!0),i(f,null,E(k.value,(b,N)=>(t(),i("div",{key:N,relative:"","h-full":"","flex-inline":"",style:T({width:`${Math.max(100,b.duration/10)/D.value*100}%`})},[(t(!0),i(f,null,E(b.functions,(I,J)=>(t(),i("div",{key:J,"h-3px":"",rounded:"",style:T({width:`max(${I.relativeWidth*100}%, 10px)`,position:"absolute",top:`${I.layer*4}px`,left:`${I.relativeStart*100}%`,backgroundColor:("getHslColorFromStringHash"in _?_.getHslColorFromStringHash:g(M))(I.event.name,50,60)})},null,4))),128)),b.route?(t(),i("div",{key:0,absolute:"","top-0":"","h-full":"","w-px":"","border-l":"","border-green6":"",op10:"",style:T({left:`${b.route.relativeStart*100}%`})},null,4)):x("",!0)],4))),128))],512),o("div",{ref_key:"minimapScroller",ref:a,class:"timeline-scroller",absolute:"","inset-0":"","h-full":"","w-full":"","of-x-scroll":""},[o("div",{ref_key:"minimapScrollerInner",ref:r,"h-1px":""},null,512)],512)]),o("div",{ref_key:"scroller",ref:e,relative:"","h-full":"","w-full":"","of-x-scroll":"","of-y-hidden":"","ws-nowrap":"","n-panel-grids":""},[(t(!0),i(f,null,E(k.value,(b,N)=>(t(),i(f,{key:N},[b.previousGap&&b.previousGap>=200?(t(),i("div",Ce,[u($,{op50:"",duration:b.previousGap,color:!1},null,8,["duration"])])):x("",!0),u(V,{"flex-inline":"","of-x-hidden":"","bg-base":"","hover:of-x-visible":"",class:O(N===k.value.length-1?"border-r border-base":""),segment:b,style:T({width:`${Math.max(50,b.duration/10)*c.value}px`}),onSelect:L[0]||(L[0]=I=>n("select",I))},null,8,["class","segment","style"])],64))),128))],512)],64)}}}),we=U(Be,[["__scopeId","data-v-da163089"]]),Te={border:"t base",flex:"~ col","h-full":"","of-y-auto":"","text-sm":""},Se=["onClick"],Ne={flex:"~","ml--1":"","font-mono":""},Fe={key:0,mr2:"",op30:""},Ee={op75:""},Le={flex:"~ col items-start"},Ie={"text-xs":"","font-mono":"",op30:""},Ve={"text-green":"","font-bold":"","font-mono":""},Me=B({__name:"TimelineList",props:{data:{}},emits:["select"],setup(y,{emit:d}){const p=d;return(n,e)=>{const l=W;return t(),i("div",Te,[(t(!0),i(f,null,E(n.data.events,(a,r)=>(t(),i("button",{key:r,border:"b base",px3:"",py2:"",flex:"~ items-center gap-2",hover:"bg-active",onClick:s=>p("select",a)},[a.type==="function"?(t(),i(f,{key:0},[e[2]||(e[2]=o("div",{"i-carbon-function":"",op50:""},null,-1)),o("div",{"font-mono":"",style:T({color:a.type==="function"?("getHslColorFromStringHash"in n?n.getHslColorFromStringHash:g(M))(a.name,50,60):""})},m(a.name),5),o("div",Ne,[e[0]||(e[0]=o("div",{op30:""}," ( ",-1)),(t(!0),i(f,null,E(a.args,(s,c)=>(t(),i(f,{key:c},[c?(t(),i("div",Fe," , ")):x("",!0),o("div",Ee,m(s===null?"null":s===void 0?"undefined":typeof s=="function"?"[function]":Array.isArray(s)?"[Array]":typeof s=="object"?"[object]":JSON.stringify(s)),1)],64))),128)),e[1]||(e[1]=o("div",{op30:""}," ) ",-1))])],64)):(t(),i(f,{key:1},[e[3]||(e[3]=o("div",{"mr-1":"","h-7":"","w-7":"",flex:"","rounded-lg":"","bg-primary:5":"",p1:"","text-green6":""},[o("div",{"i-carbon-direction-rotary-right":"",ma:"","text-lg":""})],-1)),o("div",Le,[o("div",Ie,m(a.from),1),o("div",Ve,m(a.to),1)])],64)),e[4]||(e[4]=o("div",{"flex-auto":""},null,-1)),a.end?(t(),v(l,{key:2,duration:a.end-a.start,color:a.type==="function"},null,8,["duration","color"])):x("",!0)],8,Se))),128)),e[5]||(e[5]=o("div",{"min-h-100":""},null,-1))])}}}),We={key:0,"text-blue":""},He={key:1,"text-purple":""},je={key:2,"text-green":""},ze={key:3,"text-gray":""},Oe={key:4,"text-gray":""},Re={key:5,"text-gray":""},Ge={key:0,"bg-red:10":"",px2:"",py1:"","text-red":""},Pe={key:1},Je={p2:"","text-sm":"",border:"t base"},Ye=B({__name:"TimelineArgumentView",props:{value:{}},setup(y){const d=y,p=C(),n=C();function e(){try{p.value=JSON.parse(JSON.stringify(d.value))}catch(s){console.error(s),n.value=s}}const l=B({emits:["setup"],setup(s,{emit:c}){return c("setup"),()=>null}}),a=q(),r=X();return(s,c)=>{const k=z,D=G("VMenu");return typeof s.value=="string"?(t(),i("div",We,m(JSON.stringify(s.value)),1)):typeof s.value=="number"?(t(),i("div",He,m(s.value),1)):typeof s.value=="boolean"?(t(),i("div",je,m(s.value),1)):typeof s.value>"u"?(t(),i("div",ze," undefined ")):typeof s.value=="function"?(t(),i("div",Oe," [Function"+m(s.value.name?`: ${s.value.name}`:"")+"] ",1)):s.value===null?(t(),i("div",Re," null ")):(t(),v(D,{key:6,placement:"top"},{popper:h(()=>[u(g(l),{onSetup:e}),n.value?(t(),i("div",Ge," Failed to display object: "+m(n.value),1)):p.value?(t(),i("div",Pe,[u(g(ie),pe({"model-value":p.value},s.$attrs,{class:["json-editor-vue",[g(a)==="dark"?"jse-theme-dark":""]],"main-menu-bar":!1,"navigation-bar":!1,"status-bar":!1,"read-only":!0,indentation:2,"tab-size":2}),null,16,["model-value","class"]),o("div",Je,[u(k,{title:"Copy to clipboard",icon:"carbon-copy",onClick:c[0]||(c[0]=A=>g(r)(JSON.stringify(p.value,null,2),"timeline-argument"))},{default:h(()=>c[1]||(c[1]=[F(" Copy ")])),_:1,__:[1]})])])):x("",!0)]),default:h(()=>[o("span",{"rounded-sm":"",px1:"","py0.5":"","text-sm":"",class:O([Array.isArray(s.value)?"text-amber bg-amber:10":"text-orange bg-orange:10"])},m(Array.isArray(s.value)?`[Array(${s.value.length})]`:"[Object]"),3)]),_:1}))}}}),Ue={key:0,"p-4":"",flex:"~ col gap-2","text-base":""},qe={"mx--1":""},Xe={flex:"~ gap-1","font-mono":""},Ze={key:1},Ke={key:0,op30:""},Qe={flex:"~ gap-1","text-sm":""},es={class:"text-sm text-gray-400"},ss=B({__name:"TimelineDetailsFunction",props:{record:{}},setup(y){const d=y,p=j(()=>d.record.start,{showSecond:!0}),n=Z(),e=w(()=>n.value?.metadata),l=w(()=>n.value?.imports.find(a=>a.as===d.record.name));return(a,r)=>{const s=P,c=de,k=Ye,D=W,A=ue;return a.record?(t(),i("div",Ue,[o("div",qe,[u(s,{n:"yellow",textContent:"Function call"})]),o("div",Xe,[l.value?(t(),v(c,{key:0,item:l.value,metadata:e.value,counter:!1,classes:"px2 py1","mx--2":""},null,8,["item","metadata"])):(t(),i("span",Ze,m(a.record.name),1)),r[0]||(r[0]=o("span",{ml1:"",op30:""},"(",-1)),(t(!0),i(f,null,E(a.record.args,(S,_)=>(t(),i(f,{key:_},[_?(t(),i("span",Ke,", ")):x("",!0),u(k,{value:S},null,8,["value"])],64))),128)),r[1]||(r[1]=o("span",{op30:""},")",-1))]),o("div",Qe,[a.record.end?(t(),v(D,{key:0,duration:a.record.end-a.record.start},null,8,["duration"])):x("",!0),r[2]||(r[2]=o("span",{mx1:"",op50:""},"·",-1)),o("div",es,m(g(p)),1)]),a.record.stacktrace?(t(),v(A,{key:0,stacktrace:a.record.stacktrace,class:"text-xs text-gray-400"},null,8,["stacktrace"])):x("",!0)])):x("",!0)}}}),ns={key:0,"p-4":"",flex:"~ col gap-2"},os={"mx--1":""},ts={flex:"~ gap-1 items-center","font-mono":""},as={op50:""},ls={flex:"~ gap-1","text-sm":""},rs={class:"text-sm text-gray-400"},is=B({__name:"TimelineDetailsRoute",props:{record:{}},setup(y){const d=y,p=j(()=>d.record.start,{showSecond:!0});return(n,e)=>{const l=P,a=W;return n.record?(t(),i("div",ns,[o("div",os,[u(l,{n:"green",textContent:"Route Change"})]),o("div",ts,[o("span",as,m(n.record.from),1),e[0]||(e[0]=o("span",{"i-carbon-arrow-right":"",op50:""},null,-1)),o("span",null,m(n.record.to),1)]),o("div",ls,[n.record.end?(t(),v(a,{key:0,duration:n.record.end-n.record.start},null,8,["duration"])):x("",!0),e[1]||(e[1]=o("span",{mx1:"",op50:""},"·",-1)),o("div",rs,m(g(p)),1)])])):x("",!0)}}}),ps={key:0,"h-screen":"","of-hidden":""},cs={"h-screen":"","w-full":"",flex:"","flex-col":""},ds={"h-10":"",flex:"~ gap-2 items-center justify-end",p2:"",px3:""},us={"text-sm":""},ms={"min-h-50":"",px3:"",py2:""},ks=B({__name:"TimelineView",setup(y){const d=K(),p=C("table"),n=C(),e=w(()=>d.value?.metrics.clientTimeline());function l(){e.value&&(e.value.events=[])}function a(){p.value=p.value==="table"?"list":"table"}return(r,s)=>{const c=G("VTooltip"),k=z,D=we,A=Me,S=ss,_=is,L=me;return e.value?(t(),i("div",ps,[o("div",cs,[o("div",ds,[u(c,{flex:""},{popper:h(()=>[o("div",us,m(e.value.options.enabled?"Recording...":"Paused"),1)]),default:h(()=>[o("div",{"text-lg":"",class:O(e.value.options.enabled?"i-carbon-radio-button-checked text-primary animate-pulse":"i-carbon-pause-outline op30")},null,2)]),_:1}),e.value.options.enabled?(t(),v(k,{key:1,size:"small",ml1:"","text-sm":"",n:"orange",icon:"i-carbon-stop",onClick:s[1]||(s[1]=$=>e.value.options.enabled=!1)},{default:h(()=>s[6]||(s[6]=[F(" Stop Tracking ")])),_:1,__:[6]})):(t(),v(k,{key:0,size:"small",ml1:"","text-sm":"",n:"primary",icon:"i-carbon-play",onClick:s[0]||(s[0]=$=>e.value.options.enabled=!0)},{default:h(()=>s[5]||(s[5]=[F(" Start Tracking ")])),_:1,__:[5]})),s[7]||(s[7]=o("div",{"flex-auto":""},null,-1)),u(k,{icon:p.value==="table"?"i-carbon-roadmap":"i-carbon-list",class:"ml-2",title:"Toggle View",border:!1,onClick:a},null,8,["icon"]),u(k,{icon:"i-carbon-trash-can","hover-text-red":"",class:"ml-2",border:!1,onClick:l})]),p.value==="table"?(t(),v(D,{key:0,data:{...e.value},onSelect:s[2]||(s[2]=$=>n.value=$.event)},null,8,["data"])):(t(),v(A,{key:1,data:{...e.value},onSelect:s[3]||(s[3]=$=>n.value=$)},null,8,["data"]))]),u(L,{"model-value":!!n.value,"auto-close":"",transition:"bottom",left:"#nuxt-devtools-side-nav",onClose:s[4]||(s[4]=$=>n.value=void 0)},{default:h(()=>[o("div",ms,[n.value?.type==="function"?(t(),v(S,{key:0,record:n.value},null,8,["record"])):n.value?.type==="route"?(t(),v(_,{key:1,record:n.value},null,8,["record"])):x("",!0)])]),_:1},8,["model-value"])])):x("",!0)}}}),ys={class:"markdown-body"},fs={__name:"timeline",setup(y,{expose:d}){return d({frontmatter:{}}),(n,e)=>(t(),i("div",ys,e[0]||(e[0]=[o("h1",null,"Timeline",-1),o("blockquote",{"text-orange":"","bg-orange:10":"",py1:""}," This is an experimental feature ",-1),ce(`<p>Timeline tracks your route navigations and functions calls in your Nuxt application. It can be used to debug performance issues and to understand how your application works.</p><h2>Function calls</h2><p>Nuxt DevTools tracks function calls by wrapping them in the build time. It works for functions referenced by auto-imports, or explicit imports through <code>import {} from &#39;#imports&#39;</code>.</p><pre class="shiki shiki-themes vitesse-light vitesse-dark" style="background-color:#ffffff;--shiki-dark-bg:#121212;color:#393a34;--shiki-dark:#dbd7caee;" tabindex="0"><code><span class="line"><span style="color:#999999;--shiki-dark:#666666;">&lt;</span><span style="color:#1E754F;--shiki-dark:#4D9375;">script</span><span style="color:#B07D48;--shiki-dark:#BD976A;"> setup</span><span style="color:#999999;--shiki-dark:#666666;">&gt;</span></span>
<span class="line"><span style="color:#1E754F;--shiki-dark:#4D9375;">import</span><span style="color:#999999;--shiki-dark:#666666;"> {</span><span style="color:#B07D48;--shiki-dark:#BD976A;"> useRoute</span><span style="color:#999999;--shiki-dark:#666666;"> }</span><span style="color:#1E754F;--shiki-dark:#4D9375;"> from</span><span style="color:#B5695977;--shiki-dark:#C98A7D77;"> &#39;</span><span style="color:#B56959;--shiki-dark:#C98A7D;">#app/composables/router</span><span style="color:#B5695977;--shiki-dark:#C98A7D77;">&#39;</span></span>
<span class="line"><span style="color:#1E754F;--shiki-dark:#4D9375;">import</span><span style="color:#999999;--shiki-dark:#666666;"> {</span><span style="color:#B07D48;--shiki-dark:#BD976A;"> useNuxtApp</span><span style="color:#999999;--shiki-dark:#666666;"> }</span><span style="color:#1E754F;--shiki-dark:#4D9375;"> from</span><span style="color:#B5695977;--shiki-dark:#C98A7D77;"> &#39;</span><span style="color:#B56959;--shiki-dark:#C98A7D;">#imports</span><span style="color:#B5695977;--shiki-dark:#C98A7D77;">&#39;</span></span>
<span class="line"><span style="color:#1E754F;--shiki-dark:#4D9375;">import</span><span style="color:#999999;--shiki-dark:#666666;"> {</span><span style="color:#B07D48;--shiki-dark:#BD976A;"> useMouse</span><span style="color:#999999;--shiki-dark:#666666;"> }</span><span style="color:#1E754F;--shiki-dark:#4D9375;"> from</span><span style="color:#B5695977;--shiki-dark:#C98A7D77;"> &#39;</span><span style="color:#B56959;--shiki-dark:#C98A7D;">@vueuse/core</span><span style="color:#B5695977;--shiki-dark:#C98A7D77;">&#39;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#AB5959;--shiki-dark:#CB7676;">const</span><span style="color:#B07D48;--shiki-dark:#BD976A;"> route</span><span style="color:#999999;--shiki-dark:#666666;"> =</span><span style="color:#59873A;--shiki-dark:#80A665;"> useRoute</span><span style="color:#999999;--shiki-dark:#666666;">()</span><span style="color:#A0ADA0;--shiki-dark:#758575DD;"> // tracked</span></span>
<span class="line"><span style="color:#AB5959;--shiki-dark:#CB7676;">const</span><span style="color:#B07D48;--shiki-dark:#BD976A;"> app</span><span style="color:#999999;--shiki-dark:#666666;"> =</span><span style="color:#59873A;--shiki-dark:#80A665;"> useNuxtApp</span><span style="color:#999999;--shiki-dark:#666666;">()</span><span style="color:#A0ADA0;--shiki-dark:#758575DD;"> // tracked</span></span>
<span class="line"></span>
<span class="line"><span style="color:#A0ADA0;--shiki-dark:#758575DD;">// NOT tracked because it&#39;s directly imported</span></span>
<span class="line"><span style="color:#AB5959;--shiki-dark:#CB7676;">const</span><span style="color:#B07D48;--shiki-dark:#BD976A;"> mouse</span><span style="color:#999999;--shiki-dark:#666666;"> =</span><span style="color:#59873A;--shiki-dark:#80A665;"> useMouse</span><span style="color:#999999;--shiki-dark:#666666;">()</span></span>
<span class="line"><span style="color:#999999;--shiki-dark:#666666;">&lt;/</span><span style="color:#1E754F;--shiki-dark:#4D9375;">script</span><span style="color:#999999;--shiki-dark:#666666;">&gt;</span></span>
<span class="line"></span></code></pre><p>By default, it tracks Nuxt provided composables as well as user defined functions. You can include/exclude functions by using the <code>include</code> and <code>exclude</code> options in the <code>nuxt.config.js</code> file.</p><pre class="shiki shiki-themes vitesse-light vitesse-dark" style="background-color:#ffffff;--shiki-dark-bg:#121212;color:#393a34;--shiki-dark:#dbd7caee;" tabindex="0"><code><span class="line"><span style="color:#1E754F;--shiki-dark:#4D9375;">export</span><span style="color:#1E754F;--shiki-dark:#4D9375;"> default</span><span style="color:#59873A;--shiki-dark:#80A665;"> defineNuxtConfig</span><span style="color:#999999;--shiki-dark:#666666;">({</span></span>
<span class="line"><span style="color:#998418;--shiki-dark:#B8A965;">  devtools</span><span style="color:#999999;--shiki-dark:#666666;">:</span><span style="color:#999999;--shiki-dark:#666666;"> {</span></span>
<span class="line"><span style="color:#998418;--shiki-dark:#B8A965;">    timeline</span><span style="color:#999999;--shiki-dark:#666666;">:</span><span style="color:#999999;--shiki-dark:#666666;"> {</span></span>
<span class="line"><span style="color:#998418;--shiki-dark:#B8A965;">      functions</span><span style="color:#999999;--shiki-dark:#666666;">:</span><span style="color:#999999;--shiki-dark:#666666;"> {</span></span>
<span class="line"><span style="color:#998418;--shiki-dark:#B8A965;">        include</span><span style="color:#999999;--shiki-dark:#666666;">:</span><span style="color:#999999;--shiki-dark:#666666;"> [</span></span>
<span class="line"><span style="color:#A0ADA0;--shiki-dark:#758575DD;">          // track \`useMouse\`</span></span>
<span class="line"><span style="color:#B5695977;--shiki-dark:#C98A7D77;">          &#39;</span><span style="color:#B56959;--shiki-dark:#C98A7D;">useMouse</span><span style="color:#B5695977;--shiki-dark:#C98A7D77;">&#39;</span><span style="color:#999999;--shiki-dark:#666666;">,</span></span>
<span class="line"><span style="color:#A0ADA0;--shiki-dark:#758575DD;">          // track all functions starting with \`use\`</span></span>
<span class="line"><span style="color:#B5695977;--shiki-dark:#C98A7D77;">          /</span><span style="color:#1E754F;--shiki-dark:#4D9375;">^</span><span style="color:#AB5E3F;--shiki-dark:#C4704F;">use</span><span style="color:#999999;--shiki-dark:#666666;">[</span><span style="color:#A65E2B;--shiki-dark:#C99076;">A-Z</span><span style="color:#999999;--shiki-dark:#666666;">]</span><span style="color:#B5695977;--shiki-dark:#C98A7D77;">/</span><span style="color:#999999;--shiki-dark:#666666;">,</span></span>
<span class="line"><span style="color:#A0ADA0;--shiki-dark:#758575DD;">          // track all functions from @vueuse/core</span></span>
<span class="line"><span style="color:#B07D48;--shiki-dark:#BD976A;">          entry</span><span style="color:#999999;--shiki-dark:#666666;"> =&gt;</span><span style="color:#B07D48;--shiki-dark:#BD976A;"> entry</span><span style="color:#999999;--shiki-dark:#666666;">.</span><span style="color:#B07D48;--shiki-dark:#BD976A;">from</span><span style="color:#AB5959;--shiki-dark:#CB7676;"> ===</span><span style="color:#B5695977;--shiki-dark:#C98A7D77;"> &#39;</span><span style="color:#B56959;--shiki-dark:#C98A7D;">@vueuse/core</span><span style="color:#B5695977;--shiki-dark:#C98A7D77;">&#39;</span><span style="color:#999999;--shiki-dark:#666666;">,</span></span>
<span class="line"><span style="color:#999999;--shiki-dark:#666666;">        ],</span></span>
<span class="line"><span style="color:#998418;--shiki-dark:#B8A965;">        exclude</span><span style="color:#999999;--shiki-dark:#666666;">:</span><span style="color:#999999;--shiki-dark:#666666;"> [</span></span>
<span class="line"><span style="color:#B5695977;--shiki-dark:#C98A7D77;">          &#39;</span><span style="color:#B56959;--shiki-dark:#C98A7D;">useRouter</span><span style="color:#B5695977;--shiki-dark:#C98A7D77;">&#39;</span></span>
<span class="line"><span style="color:#999999;--shiki-dark:#666666;">        ]</span></span>
<span class="line"><span style="color:#999999;--shiki-dark:#666666;">      }</span></span>
<span class="line"><span style="color:#999999;--shiki-dark:#666666;">    }</span></span>
<span class="line"><span style="color:#999999;--shiki-dark:#666666;">  }</span></span>
<span class="line"><span style="color:#999999;--shiki-dark:#666666;">})</span></span>
<span class="line"></span></code></pre>`,6)])))}},vs={flex:"~ col gap-2","w-150":"",p4:"",border:"t base"},hs={op50:""},_s={flex:"~ gap-3",mt2:"","justify-end":""},js=B({__name:"timeline",setup(y){const d=Q(),p=ee(),n=se(),e=ne();async function l(){try{const[a,r]=await R.enableTimeline(!0);if(!await n.start(a,r))return;await R.enableTimeline(!1)}catch{ae({message:"Failed to enable timeline automatically. Check the terminal for more details.",icon:"i-carbon-warning",classes:"text-red"})}}return(a,r)=>{const s=ks,c=ke,k=oe,D=ye,A=fe,S=z,_=te,L=fs,$=ve;return t(),i(f,null,[g(d)?.timeline?.enabled?(t(),v(s,{key:0})):(t(),i(f,{key:1},[u(k,null,{default:h(()=>[u(c,{icon:"i-carbon-roadmap",name:"feature-timeline",title:"Timeline",description:"Timeline enables the inspection of when composable being executed and the route changes.",actions:[{label:"Enable"}],onAction:l})]),_:1}),u(g(n),null,{default:h(({resolve:V,args:b})=>[u(_,{"model-value":!0,onClose:N=>V(!1)},{default:h(()=>[o("div",vs,[r[5]||(r[5]=o("h2",{"text-xl":""},[o("span",{capitalize:""},"Enable Timeline?")],-1)),o("p",hs,[r[1]||(r[1]=F(" Your ")),u(D,{role:"button",n:"primary",underline:"",onClick:r[0]||(r[0]=N=>g(e)(g(p)?._nuxtConfigFile)),textContent:"Nuxt config"}),r[2]||(r[2]=F(" will be updated as: "))]),u(A,{from:b[0],to:b[1],"max-h-80":"","of-auto":"",py2:"",border:"~ base rounded",lang:"ts"},null,8,["from","to"]),r[6]||(r[6]=o("p",null,[o("span",{op50:""},"Then Nuxt will "),o("span",{"text-orange":""},"restart automatically"),F(". ")],-1)),o("div",_s,[u(S,{onClick:N=>V(!1)},{default:h(()=>r[3]||(r[3]=[F(" Cancel ")])),_:2,__:[3]},1032,["onClick"]),u(S,{n:"solid primary",capitalize:"",onClick:N=>V(!0)},{default:h(()=>r[4]||(r[4]=[F(" Enable ")])),_:2,__:[4]},1032,["onClick"])])])]),_:2},1032,["onClose"])]),_:1})],64)),u($,null,{default:h(()=>[u(L)]),_:1})],64)}}});export{js as default};
