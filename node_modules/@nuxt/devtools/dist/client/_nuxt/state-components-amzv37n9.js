import{f as m,al as o,r as n}from"./czf9xkmw.js";import{q as s}from"./vendor/json-editor-vue-m9gzt21j.js";function u(){const p=m(),a=o("getComponents",()=>n.getComponents()),l=s(()=>Object.entries(p.value?.nuxt?.vueApp._context.components||{}).map(([e])=>({pascalName:e,global:!0})).filter(e=>!/^Lazy[A-Z]/.test(e.pascalName)).filter(e=>!(a.value||[]).find(t=>t.pascalName===e.pascalName)));return s(()=>[...l.value,...a.value||[]].sort((e,t)=>e.pascalName.localeCompare(t.pascalName)))}function i(){return o("getComponentsRelationships",()=>n.getComponentsRelationships())}export{i as a,u};
