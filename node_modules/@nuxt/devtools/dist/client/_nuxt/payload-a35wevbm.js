import{_ as k}from"./state-editor.vue-dxli1s6t.js";import{p as x,J as i,a3 as e,F as $,ag as N,V as d,a4 as a,W as B,a6 as V,S as b,q as h,ai as w,U as _,a5 as o,u as F,ac as O,ab as E}from"./vendor/json-editor-vue-m9gzt21j.js";import{_ as P}from"./nsection-block-m4vpsvnn.js";import{f as R,_ as j}from"./czf9xkmw.js";import{_ as A}from"./help-fab.vue-b6h1gmzk.js";import"./data-schema-button.vue-i6u1wh6f.js";import"./nicon-title.vue-ejocqf9t.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";const G={key:0,flex:"~ col gap-1"},H={key:1,mt2:"",px4:"",italic:"",op35:""},K=x({__name:"StateGroup",props:{state:{},revision:{},prefix:{default:""}},setup(f){return(t,n)=>{const c=k;return e(),i("div",null,[t.state&&Object.keys(t.state).length>0?(e(),i("div",G,[(e(!0),i($,null,N(t.state,(s,r)=>(e(),d(c,{key:r,revision:t.revision,state:s,name:r.startsWith(t.prefix)?r.slice(t.prefix.length):r},{actions:a(l=>[B(t.$slots,"actions",V({ref_for:!0},l))]),_:2},1032,["revision","state","name"]))),128))])):(e(),i("div",H," No data "))])}}}),T={class:"markdown-body"},W={__name:"payload",setup(f,{expose:t}){return t({frontmatter:{}}),(c,s)=>(e(),i("div",T,s[0]||(s[0]=[b("h1",null,"State & Async Data",-1),b("p",null,"// TODO",-1)])))}},q={key:0},tt=x({__name:"payload",setup(f){const t=R(),n=h(()=>t.value?.nuxt.payload),c=h(()=>t.value?.revision.value);async function s(r){await t.value?.nuxt.hooks.callHookParallel("app:data:refresh",r)}return(r,l)=>{const m=K,p=P,v=j,S=k,g=W,D=A,C=w("tooltip");return e(),i($,null,[F(t)?(e(),i("div",q,[o(p,{icon:"carbon-data-set",text:"State",description:"Keyed state from `useState`",padding:!1},{default:a(()=>[o(m,{state:n.value.state,revision:c.value,prefix:"$s"},null,8,["state","revision"])]),_:1}),o(p,{icon:"carbon-data-blob",text:"Data",description:"Keyed state from `useAsyncData`",padding:!1},{actions:a(()=>[o(v,{n:"xs primary","self-start":"",icon:"i-carbon-recycle",onClick:l[0]||(l[0]=y=>s())},{default:a(()=>l[1]||(l[1]=[E(" Re-fetch all data ")])),_:1,__:[1]})]),default:a(()=>[o(m,{state:n.value.data,revision:c.value},{actions:a(({isOpen:y,name:u})=>[y&&u?O((e(),d(v,{key:0,title:`Re-fetch '${u}'`,icon:"carbon-recycle",border:!1,onClick:J=>s([u])},null,8,["title","onClick"])),[[C,`Re-fetch '${u}'`,void 0,{bottom:!0}]]):_("",!0)]),_:1},8,["state","revision"])]),_:1}),n.value.functions&&Object.keys(n.value.functions).length?(e(),d(p,{key:0,icon:"carbon-function",text:"Functions",description:"State for functions"},{default:a(()=>[o(S,{"ml--6":"",state:n.value.functions,revision:c.value},null,8,["state","revision"])]),_:1})):_("",!0)])):_("",!0),o(D,null,{default:a(()=>[o(g)]),_:1})],64)}}});export{tt as default};
