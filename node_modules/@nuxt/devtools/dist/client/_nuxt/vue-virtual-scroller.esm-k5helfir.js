import{z as J,a0 as re,a1 as ne,a2 as oe,V as P,a3 as $,aA as le,s as ae,$ as ue,ai as ce,ac as de,J as M,U as W,a5 as fe,W as A,a4 as K,F as he,ag as me,af as q,a6 as pe,aB as ye,Z as X,X as ve}from"./vendor/json-editor-vue-m9gzt21j.js";function be(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);var i=e.indexOf("Trident/");if(i>0){var s=e.indexOf("rv:");return parseInt(e.substring(s+3,e.indexOf(".",s)),10)}var r=e.indexOf("Edge/");return r>0?parseInt(e.substring(r+5,e.indexOf(".",r)),10):-1}let R;function H(){H.init||(H.init=!0,R=be()!==-1)}var E={name:"ResizeObserver",props:{emitOnMount:{type:Boolean,default:!1},ignoreWidth:{type:Boolean,default:!1},ignoreHeight:{type:Boolean,default:!1}},emits:["notify"],mounted(){H(),J(()=>{this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.emitOnMount&&this.emitSize()});const e=document.createElement("object");this._resizeObject=e,e.setAttribute("aria-hidden","true"),e.setAttribute("tabindex",-1),e.onload=this.addResizeHandlers,e.type="text/html",R&&this.$el.appendChild(e),e.data="about:blank",R||this.$el.appendChild(e)},beforeUnmount(){this.removeResizeHandlers()},methods:{compareAndNotify(){(!this.ignoreWidth&&this._w!==this.$el.offsetWidth||!this.ignoreHeight&&this._h!==this.$el.offsetHeight)&&(this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.emitSize())},emitSize(){this.$emit("notify",{width:this._w,height:this._h})},addResizeHandlers(){this._resizeObject.contentDocument.defaultView.addEventListener("resize",this.compareAndNotify),this.compareAndNotify()},removeResizeHandlers(){this._resizeObject&&this._resizeObject.onload&&(!R&&this._resizeObject.contentDocument&&this._resizeObject.contentDocument.defaultView.removeEventListener("resize",this.compareAndNotify),this.$el.removeChild(this._resizeObject),this._resizeObject.onload=null,this._resizeObject=null)}}};const ge=oe();re("data-v-b329ee4c");const Se={class:"resize-observer",tabindex:"-1"};ne();const ze=ge((e,t,i,s,r,a)=>($(),P("div",Se)));E.render=ze;E.__scopeId="data-v-b329ee4c";E.__file="src/components/ResizeObserver.vue";function C(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?C=function(t){return typeof t}:C=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},C(e)}function _e(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function we(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function Ie(e,t,i){return t&&we(e.prototype,t),e}function Y(e){return $e(e)||Te(e)||Oe(e)||Ve()}function $e(e){if(Array.isArray(e))return N(e)}function Te(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function Oe(e,t){if(e){if(typeof e=="string")return N(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);if(i==="Object"&&e.constructor&&(i=e.constructor.name),i==="Map"||i==="Set")return Array.from(e);if(i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return N(e,t)}}function N(e,t){(t==null||t>e.length)&&(t=e.length);for(var i=0,s=new Array(t);i<t;i++)s[i]=e[i];return s}function Ve(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xe(e){var t;return typeof e=="function"?t={callback:e}:t=e,t}function ke(e,t){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s,r,a,u=function(n){for(var d=arguments.length,p=new Array(d>1?d-1:0),S=1;S<d;S++)p[S-1]=arguments[S];if(a=p,!(s&&n===r)){var w=i.leading;typeof w=="function"&&(w=w(n,r)),(!s||n!==r)&&w&&e.apply(void 0,[n].concat(Y(a))),r=n,clearTimeout(s),s=setTimeout(function(){e.apply(void 0,[n].concat(Y(a))),s=0},t)}};return u._clear=function(){clearTimeout(s),s=null},u}function Z(e,t){if(e===t)return!0;if(C(e)==="object"){for(var i in e)if(!Z(e[i],t[i]))return!1;return!0}return!1}var Me=function(){function e(t,i,s){_e(this,e),this.el=t,this.observer=null,this.frozen=!1,this.createObserver(i,s)}return Ie(e,[{key:"createObserver",value:function(i,s){var r=this;if(this.observer&&this.destroyObserver(),!this.frozen){if(this.options=xe(i),this.callback=function(c,n){r.options.callback(c,n),c&&r.options.once&&(r.frozen=!0,r.destroyObserver())},this.callback&&this.options.throttle){var a=this.options.throttleOptions||{},u=a.leading;this.callback=ke(this.callback,this.options.throttle,{leading:function(n){return u==="both"||u==="visible"&&n||u==="hidden"&&!n}})}this.oldResult=void 0,this.observer=new IntersectionObserver(function(c){var n=c[0];if(c.length>1){var d=c.find(function(S){return S.isIntersecting});d&&(n=d)}if(r.callback){var p=n.isIntersecting&&n.intersectionRatio>=r.threshold;if(p===r.oldResult)return;r.oldResult=p,r.callback(p,n)}},this.options.intersection),J(function(){r.observer&&r.observer.observe(r.el)})}}},{key:"destroyObserver",value:function(){this.observer&&(this.observer.disconnect(),this.observer=null),this.callback&&this.callback._clear&&(this.callback._clear(),this.callback=null)}},{key:"threshold",get:function(){return this.options.intersection&&typeof this.options.intersection.threshold=="number"?this.options.intersection.threshold:0}}]),e}();function Q(e,t,i){var s=t.value;if(s)if(typeof IntersectionObserver>"u")console.warn("[vue-observe-visibility] IntersectionObserver API is not available in your browser. Please install this polyfill: https://github.com/w3c/IntersectionObserver/tree/master/polyfill");else{var r=new Me(e,s,i);e._vue_visibilityState=r}}function Ae(e,t,i){var s=t.value,r=t.oldValue;if(!Z(s,r)){var a=e._vue_visibilityState;if(!s){ee(e);return}a?a.createObserver(s,i):Q(e,{value:s},i)}}function ee(e){var t=e._vue_visibilityState;t&&(t.destroyObserver(),delete e._vue_visibilityState)}var Re={beforeMount:Q,updated:Ae,unmounted:ee},Ce={itemsLimit:1e3},Ee=/(auto|scroll)/;function te(e,t){return e.parentNode===null?t:te(e.parentNode,t.concat([e]))}var L=function(t,i){return getComputedStyle(t,null).getPropertyValue(i)},Le=function(t){return L(t,"overflow")+L(t,"overflow-y")+L(t,"overflow-x")},Pe=function(t){return Ee.test(Le(t))};function G(e){if(e instanceof HTMLElement||e instanceof SVGElement){for(var t=te(e.parentNode,[]),i=0;i<t.length;i+=1)if(Pe(t[i]))return t[i];return document.scrollingElement||document.documentElement}}function B(e){"@babel/helpers - typeof";return B=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},B(e)}var He={items:{type:Array,required:!0},keyField:{type:String,default:"id"},direction:{type:String,default:"vertical",validator:function(t){return["vertical","horizontal"].includes(t)}},listTag:{type:String,default:"div"},itemTag:{type:String,default:"div"}};function Ne(){return this.items.length&&B(this.items[0])!=="object"}var j=!1;if(typeof window<"u"){j=!1;try{var Be=Object.defineProperty({},"passive",{get:function(){j=!0}});window.addEventListener("test",null,Be)}catch{}}let je=0;var ie={name:"RecycleScroller",components:{ResizeObserver:E},directives:{ObserveVisibility:Re},props:{...He,itemSize:{type:Number,default:null},gridItems:{type:Number,default:void 0},itemSecondarySize:{type:Number,default:void 0},minItemSize:{type:[Number,String],default:null},sizeField:{type:String,default:"size"},typeField:{type:String,default:"type"},buffer:{type:Number,default:200},pageMode:{type:Boolean,default:!1},prerender:{type:Number,default:0},emitUpdate:{type:Boolean,default:!1},updateInterval:{type:Number,default:0},skipHover:{type:Boolean,default:!1},listTag:{type:String,default:"div"},itemTag:{type:String,default:"div"},listClass:{type:[String,Object,Array],default:""},itemClass:{type:[String,Object,Array],default:""}},emits:["resize","visible","hidden","update","scroll-start","scroll-end"],data(){return{pool:[],totalSize:0,ready:!1,hoverKey:null}},computed:{sizes(){if(this.itemSize===null){const e={"-1":{accumulator:0}},t=this.items,i=this.sizeField,s=this.minItemSize;let r=1e4,a=0,u;for(let c=0,n=t.length;c<n;c++)u=t[c][i]||s,u<r&&(r=u),a+=u,e[c]={accumulator:a,size:u};return this.$_computedMinItemSize=r,e}return[]},simpleArray:Ne,itemIndexByKey(){const{keyField:e,items:t}=this,i={};for(let s=0,r=t.length;s<r;s++)i[t[s][e]]=s;return i}},watch:{items(){this.updateVisibleItems(!0)},pageMode(){this.applyPageMode(),this.updateVisibleItems(!1)},sizes:{handler(){this.updateVisibleItems(!1)},deep:!0},gridItems(){this.updateVisibleItems(!0)},itemSecondarySize(){this.updateVisibleItems(!0)}},created(){this.$_startIndex=0,this.$_endIndex=0,this.$_views=new Map,this.$_unusedViews=new Map,this.$_scrollDirty=!1,this.$_lastUpdateScrollPosition=0,this.prerender&&(this.$_prerender=!0,this.updateVisibleItems(!1)),this.gridItems&&!this.itemSize&&console.error("[vue-recycle-scroller] You must provide an itemSize when using gridItems")},mounted(){this.applyPageMode(),this.$nextTick(()=>{this.$_prerender=!1,this.updateVisibleItems(!0),this.ready=!0})},activated(){const e=this.$_lastUpdateScrollPosition;typeof e=="number"&&this.$nextTick(()=>{this.scrollToPosition(e)})},beforeUnmount(){this.removeListeners()},methods:{addView(e,t,i,s,r){const a=le({id:je++,index:t,used:!0,key:s,type:r}),u=ae({item:i,position:0,nr:a});return e.push(u),u},unuseView(e,t=!1){const i=this.$_unusedViews,s=e.nr.type;let r=i.get(s);r||(r=[],i.set(s,r)),r.push(e),t||(e.nr.used=!1,e.position=-9999)},handleResize(){this.$emit("resize"),this.ready&&this.updateVisibleItems(!1)},handleScroll(e){if(!this.$_scrollDirty){if(this.$_scrollDirty=!0,this.$_updateTimeout)return;const t=()=>requestAnimationFrame(()=>{this.$_scrollDirty=!1;const{continuous:i}=this.updateVisibleItems(!1,!0);i||(clearTimeout(this.$_refreshTimout),this.$_refreshTimout=setTimeout(this.handleScroll,this.updateInterval+100))});t(),this.updateInterval&&(this.$_updateTimeout=setTimeout(()=>{this.$_updateTimeout=0,this.$_scrollDirty&&t()},this.updateInterval))}},handleVisibilityChange(e,t){this.ready&&(e||t.boundingClientRect.width!==0||t.boundingClientRect.height!==0?(this.$emit("visible"),requestAnimationFrame(()=>{this.updateVisibleItems(!1)})):this.$emit("hidden"))},updateVisibleItems(e,t=!1){const i=this.itemSize,s=this.gridItems||1,r=this.itemSecondarySize||i,a=this.$_computedMinItemSize,u=this.typeField,c=this.simpleArray?null:this.keyField,n=this.items,d=n.length,p=this.sizes,S=this.$_views,w=this.$_unusedViews,V=this.pool,se=this.itemIndexByKey;let y,f,T,g,z;if(!d)y=f=g=z=T=0;else if(this.$_prerender)y=g=0,f=z=Math.min(this.prerender,n.length),T=null;else{const o=this.getScroll();if(t){let m=o.start-this.$_lastUpdateScrollPosition;if(m<0&&(m=-m),i===null&&m<a||m<i)return{continuous:!0}}this.$_lastUpdateScrollPosition=o.start;const v=this.buffer;o.start-=v,o.end+=v;let h=0;if(this.$refs.before&&(h=this.$refs.before.scrollHeight,o.start-=h),this.$refs.after){const m=this.$refs.after.scrollHeight;o.end+=m}if(i===null){let m,O=0,F=d-1,b=~~(d/2),U;do U=b,m=p[b].accumulator,m<o.start?O=b:b<d-1&&p[b+1].accumulator>o.start&&(F=b),b=~~((O+F)/2);while(b!==U);for(b<0&&(b=0),y=b,T=p[d-1].accumulator,f=b;f<d&&p[f].accumulator<o.end;f++);for(f===-1?f=n.length-1:(f++,f>d&&(f=d)),g=y;g<d&&h+p[g].accumulator<o.start;g++);for(z=g;z<d&&h+p[z].accumulator<o.end;z++);}else{y=~~(o.start/i*s);const m=y%s;y-=m,f=Math.ceil(o.end/i*s),g=Math.max(0,Math.floor((o.start-h)/i*s)),z=Math.floor((o.end-h)/i*s),y<0&&(y=0),f>d&&(f=d),g<0&&(g=0),z>d&&(z=d),T=Math.ceil(d/s)*i}}f-y>Ce.itemsLimit&&this.itemsLimitError(),this.totalSize=T;let l;const x=y<=this.$_endIndex&&f>=this.$_startIndex;if(x)for(let o=0,v=V.length;o<v;o++)l=V[o],l.nr.used&&(e&&(l.nr.index=se[l.item[c]]),(l.nr.index==null||l.nr.index<y||l.nr.index>=f)&&this.unuseView(l));const D=x?null:new Map;let I,_,k;for(let o=y;o<f;o++){I=n[o];const v=c?I[c]:I;if(v==null)throw new Error(`Key is ${v} on item (keyField is '${c}')`);if(l=S.get(v),!i&&!p[o].size){l&&this.unuseView(l);continue}_=I[u];let h=w.get(_),m=!1;if(!l)x?h&&h.length?l=h.pop():l=this.addView(V,o,I,v,_):(k=D.get(_)||0,(!h||k>=h.length)&&(l=this.addView(V,o,I,v,_),this.unuseView(l,!0),h=w.get(_)),l=h[k],D.set(_,k+1)),S.delete(l.nr.key),l.nr.used=!0,l.nr.index=o,l.nr.key=v,l.nr.type=_,S.set(v,l),m=!0;else if(!l.nr.used&&(l.nr.used=!0,m=!0,h)){const O=h.indexOf(l);O!==-1&&h.splice(O,1)}l.item=I,m&&(o===n.length-1&&this.$emit("scroll-end"),o===0&&this.$emit("scroll-start")),i===null?(l.position=p[o-1].accumulator,l.offset=0):(l.position=Math.floor(o/s)*i,l.offset=o%s*r)}return this.$_startIndex=y,this.$_endIndex=f,this.emitUpdate&&this.$emit("update",y,f,g,z),clearTimeout(this.$_sortTimer),this.$_sortTimer=setTimeout(this.sortViews,this.updateInterval+300),{continuous:x}},getListenerTarget(){let e=G(this.$el);return window.document&&(e===window.document.documentElement||e===window.document.body)&&(e=window),e},getScroll(){const{$el:e,direction:t}=this,i=t==="vertical";let s;if(this.pageMode){const r=e.getBoundingClientRect(),a=i?r.height:r.width;let u=-(i?r.top:r.left),c=i?window.innerHeight:window.innerWidth;u<0&&(c+=u,u=0),u+c>a&&(c=a-u),s={start:u,end:u+c}}else i?s={start:e.scrollTop,end:e.scrollTop+e.clientHeight}:s={start:e.scrollLeft,end:e.scrollLeft+e.clientWidth};return s},applyPageMode(){this.pageMode?this.addListeners():this.removeListeners()},addListeners(){this.listenerTarget=this.getListenerTarget(),this.listenerTarget.addEventListener("scroll",this.handleScroll,j?{passive:!0}:!1),this.listenerTarget.addEventListener("resize",this.handleResize)},removeListeners(){this.listenerTarget&&(this.listenerTarget.removeEventListener("scroll",this.handleScroll),this.listenerTarget.removeEventListener("resize",this.handleResize),this.listenerTarget=null)},scrollToItem(e){let t;const i=this.gridItems||1;this.itemSize===null?t=e>0?this.sizes[e-1].accumulator:0:t=Math.floor(e/i)*this.itemSize,this.scrollToPosition(t)},scrollToPosition(e){const t=this.direction==="vertical"?{scroll:"scrollTop",start:"top"}:{scroll:"scrollLeft",start:"left"};let i,s,r;if(this.pageMode){const a=G(this.$el),u=a.tagName==="HTML"?0:a[t.scroll],c=a.getBoundingClientRect(),d=this.$el.getBoundingClientRect()[t.start]-c[t.start];i=a,s=t.scroll,r=e+u+d}else i=this.$el,s=t.scroll,r=e;i[s]=r},itemsLimitError(){throw setTimeout(()=>{console.log("It seems the scroller element isn't scrolling, so it tries to render all the items at once.","Scroller:",this.$el),console.log("Make sure the scroller has a fixed height (or width) and 'overflow-y' (or 'overflow-x') set to 'auto' so it can scroll correctly and only render the items visible in the scroll viewport.")}),new Error("Rendered items limit reached")},sortViews(){this.pool.sort((e,t)=>e.nr.index-t.nr.index)}}};const De={key:0,ref:"before",class:"vue-recycle-scroller__slot"},Fe={key:1,ref:"after",class:"vue-recycle-scroller__slot"};function Ue(e,t,i,s,r,a){const u=ue("ResizeObserver"),c=ce("observe-visibility");return de(($(),M("div",{class:X(["vue-recycle-scroller",{ready:r.ready,"page-mode":i.pageMode,[`direction-${e.direction}`]:!0}]),onScrollPassive:t[0]||(t[0]=(...n)=>a.handleScroll&&a.handleScroll(...n))},[e.$slots.before?($(),M("div",De,[A(e.$slots,"before")],512)):W("v-if",!0),($(),P(q(i.listTag),{ref:"wrapper",style:ve({[e.direction==="vertical"?"minHeight":"minWidth"]:r.totalSize+"px"}),class:X(["vue-recycle-scroller__item-wrapper",i.listClass])},{default:K(()=>[($(!0),M(he,null,me(r.pool,n=>($(),P(q(i.itemTag),pe({key:n.nr.id,style:r.ready?{transform:`translate${e.direction==="vertical"?"Y":"X"}(${n.position}px) translate${e.direction==="vertical"?"X":"Y"}(${n.offset}px)`,width:i.gridItems?`${e.direction==="vertical"&&i.itemSecondarySize||i.itemSize}px`:void 0,height:i.gridItems?`${e.direction==="horizontal"&&i.itemSecondarySize||i.itemSize}px`:void 0}:null,class:["vue-recycle-scroller__item-view",[i.itemClass,{hover:!i.skipHover&&r.hoverKey===n.nr.key}]]},ye(i.skipHover?{}:{mouseenter:()=>{r.hoverKey=n.nr.key},mouseleave:()=>{r.hoverKey=null}})),{default:K(()=>[A(e.$slots,"default",{item:n.item,index:n.nr.index,active:n.nr.used})]),_:2},1040,["style","class"]))),128)),A(e.$slots,"empty")]),_:3},8,["style","class"])),e.$slots.after?($(),M("div",Fe,[A(e.$slots,"after")],512)):W("v-if",!0),fe(u,{onNotify:a.handleResize},null,8,["onNotify"])],34)),[[c,a.handleVisibilityChange]])}ie.render=Ue;ie.__file="src/components/RecycleScroller.vue";export{ie as s};
