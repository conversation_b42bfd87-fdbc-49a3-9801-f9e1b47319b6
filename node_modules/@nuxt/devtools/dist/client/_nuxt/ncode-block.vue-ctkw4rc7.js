import{d as g}from"./client-oeqdl4pb.js";import{p as k,q as d,z as v,J as n,a3 as t,Z as c,S as s,F as i,ag as x,aa as _}from"./vendor/json-editor-vue-m9gzt21j.js";const y=["innerHTML"],B=["textContent"],b=k({__name:"NCodeBlock",props:{code:{},lang:{},lines:{type:Boolean,default:!0},inline:{type:Boolean},grammarContextCode:{},transformRendered:{}},emits:["loaded"],setup(p,{emit:u}){const e=p,m=u,a=d(()=>{const o=e.lang==="text"?{code:e.code,supported:!1}:g.value?.devtools.renderCodeHighlight(e.code,e.lang,{grammarContextCode:e.grammarContextCode})||{code:e.code,supported:!1};return o.supported&&e.transformRendered&&(o.code=e.transformRendered(o.code)),o.supported&&v(()=>m("loaded")),o}),r=d(()=>["n-code-block shiki",e.lines&&!e.inline?"n-code-block-lines":""]);return(o,l)=>o.lang&&a.value.supported?(t(),n("pre",{key:0,class:c(r.value)},[s("code",{innerHTML:a.value.code},null,8,y)],2)):(t(),n("pre",{key:1,class:c(r.value)},[s("code",null,[(t(!0),n(i,null,x(o.code.split(`
`),(C,f)=>(t(),n(i,{key:f},[s("span",{class:"line",textContent:_(C)},null,8,B),l[0]||(l[0]=s("br",null,null,-1))],64))),128))])],2))}});export{b as _};
