import{bh as n,a2 as b,_ as h}from"./czf9xkmw.js";import{p as g,k as y,V as v,a3 as l,a4 as k,J as B,U as C,a6 as i,u as o,a5 as V,S as _,W as $,H as w}from"./vendor/json-editor-vue-m9gzt21j.js";const x=["border"],N={relative:"","h-full":"","w-full":"","of-auto":""},T=g({__name:"NDrawer",props:{modelValue:{type:Boolean},top:{},left:{},autoClose:{type:Boolean},transition:{default:"right"}},emits:["close"],setup(m,{emit:c}){const e=m,f=c,a=y(),{height:u}=n(()=>e.top,void 0,{box:"border-box"}),s=typeof e.left=="string"&&e.left.startsWith("#")?document.querySelector(e.left)?.getBoundingClientRect().width:n(()=>e.left,void 0,{box:"border-box"}).width;b(a,()=>{e.modelValue&&e.autoClose&&f("close")},{ignore:["a","button","summary",'[role="dialog"]']});const d={right:{"enter-from-class":"transform translate-x-1/1","leave-to-class":"transform translate-x-1/1"},top:{"enter-from-class":"transform translate-y--1/1","leave-to-class":"transform translate-y--1/1"},bottom:{"enter-from-class":"transform translate-y-1/1","leave-to-class":"transform translate-y-1/1"}};return(t,r)=>{const p=h;return l(),v(w,i(d[t.transition],{"enter-active-class":"duration-200 ease-in","enter-to-class":"opacity-100","leave-active-class":"duration-200 ease-out","leave-from-class":"opacity-100"}),{default:k(()=>[t.modelValue?(l(),B("div",i({key:0,ref_key:"el",ref:a,border:`${t.transition==="right"?"l":t.transition==="bottom"?"t":"b"} base`,flex:"~ col gap-1",class:{"right-0":t.transition==="right"||t.transition==="bottom"},absolute:"","bottom-0":"","z-10":"","z-20":"","of-auto":"","n-glass-effect":"","text-sm":"",style:{top:t.transition==="bottom"?"auto":`${o(u)}px`,left:t.transition==="right"&&!o(s)?"auto":`${o(s)}px`}},t.$attrs),[V(p,{absolute:"","right-2":"","top-2":"","z-20":"","text-xl":"",icon:"carbon-close",border:!1,onClick:r[0]||(r[0]=z=>t.$emit("close"))}),_("div",N,[$(t.$slots,"default")])],16,x)):C("",!0)]),_:3},16)}}});export{T as _};
