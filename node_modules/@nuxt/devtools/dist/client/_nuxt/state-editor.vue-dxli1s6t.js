import{G as C,a4 as O,_ as z}from"./czf9xkmw.js";import{_ as D}from"./data-schema-button.vue-i6u1wh6f.js";import{p as E,n as g,E as J,x as M,ai as P,J as c,a3 as r,u as o,S as p,U as u,W as R,Z as d,aa as b,a7 as $,a8 as j,F as k,ac as x,V as _,a5 as A,av as F,a6 as U,z as G}from"./vendor/json-editor-vue-m9gzt21j.js";const T=["open"],W={flex:"~ gap2","select-none":"","items-center":"",px4:""},Z={key:0,class:"bg-red:10 px5 py3 text-red"},L=E({__name:"StateEditor",props:{name:{},open:{type:Boolean},revision:{},state:{},readonly:{type:Boolean}},emits:["update:open"],setup(h,{emit:V}){const n=h,s=C(n,"open",V,{passive:!0}),S=O(),i=g(),l=g();function m(e){return["number","bigint","string","boolean"].includes(typeof e)}function v(){l.value=void 0;try{i.value=m(n.state)?n.state:JSON.parse(JSON.stringify(n.state||{}))}catch(e){console.error(e),l.value=e}}J(()=>{v(),M(()=>[n.revision,n.state],([e,t])=>{m(t)?i.value=n.state:y(t,n.state)},{deep:!0})});function y(e,t){for(const a in e)Array.isArray(e[a])?t[a]=e[a].slice():typeof e[a]=="object"&&e[a]!==null?y(e[a],t[a]):t[a]=e[a]}async function B(){v(),await G()}return(e,t)=>{const a=z,w=D,N=P("tooltip");return r(),c("div",{class:"state-editor-details",open:e.name?o(s):!0},[p("div",W,[e.name?(r(),c("button",{key:0,flex:"~","cursor-pointer":"","items-center":"",class:d(o(s)?"":"op50"),onClick:t[0]||(t[0]=f=>s.value=!o(s))},[p("div",{"i-carbon-chevron-right":"",transition:"",class:d(o(s)?"rotate-90 op0":"")},null,2),p("code",{px3:"",py1:"","font-mono":"",class:d(o(s)?"bg-[#8881] rounded-t":"rounded hover:bg-active")},b(e.name),3)],2)):u("",!0),R(e.$slots,"actions",$(j({isOpen:o(s),name:e.name,state:e.state}))),o(s)?(r(),c(k,{key:1},[x(A(a,{title:"Refresh View",icon:"carbon-renew",border:!1,onClick:B},null,512),[[N,"Refresh View",void 0,{bottom:!0}]]),i.value&&!l.value?(r(),_(w,{key:0,getter:()=>({name:e.name,input:JSON.stringify(i.value)})},null,8,["getter"])):u("",!0)],64)):u("",!0)]),o(s)||!e.name?(r(),c(k,{key:0},[l.value?(r(),c("div",Z," Error: "+b(l.value),1)):(r(),_(o(F),U({key:1,modelValue:i.value,"onUpdate:modelValue":t[1]||(t[1]=f=>i.value=f)},e.$attrs,{class:["json-editor-vue",[o(S)==="dark"?"jse-theme-dark":"",(e.name,"")]],"main-menu-bar":!1,"navigation-bar":!1,"status-bar":!1,"read-only":n.readonly,indentation:2,"tab-size":2}),null,16,["modelValue","class","read-only"]))],64)):u("",!0)],8,T)}}});export{L as _};
