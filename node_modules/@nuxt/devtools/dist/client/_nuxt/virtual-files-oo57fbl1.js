import{_ as w}from"./nnavbar.vue-ma0lzxrm.js";import{_ as S}from"./ncode-block.vue-ctkw4rc7.js";import{bi as D,bj as B,S as j,i as P,n as E,a8 as I}from"./czf9xkmw.js";import{J as c,a3 as i,S as a,p as U,k as m,w as W,q as _,a5 as l,a4 as u,V as q,aa as v,F as d,ag as z,Z as G}from"./vendor/json-editor-vue-m9gzt21j.js";import{_ as H}from"./help-fab.vue-b6h1gmzk.js";import"./client-oeqdl4pb.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";const J={class:"markdown-body"},L={__name:"virtual-files",setup(h,{expose:r}){return r({frontmatter:{}}),(p,o)=>(i(),c("div",J,o[0]||(o[0]=[a("h1",null,"Virtual Files",-1),a("p",null,"Virtual files are generated on the fly to support the conventions of the framework, and to provide a better developer experience.",-1)])))}},R=["onClick"],Z={key:0,"h-full":"","of-hidden":"",flex:"~ col"},A={border:"b base","flex-none":"",px4:"",py2:"","text-sm":"",op75:""},oe=U({__name:"virtual-files",setup(h){const r=m(""),n=D(),p=B(),o=m();W(()=>{if(!p.value)return;const e=`/_vfs.json/${encodeURIComponent(p.value)}`;fetch(e,{headers:{accept:"application/json"}}).then(t=>t.json()).then(t=>o.value=t.current)});function x(e){if(n.value?.rootDir)return e.startsWith(n.value?.rootDir)?e.slice(n.value.rootDir.length):e}const f=_(()=>n.value?n.value.entries.filter(e=>!e.id.startsWith(`${n.value?.rootDir||""}/.nuxt/`)).sort((e,t)=>e.id.localeCompare(t.id)):[]),k=_(()=>new j(f.value,{keys:["id","path"]})),b=_(()=>r.value?k.value.search(r.value).map(e=>e.item):f.value);return(e,t)=>{const y=w,g=S,C=E,F=P,$=I,N=L,V=H;return i(),c(d,null,[l($,{class:"virtual-files","storage-key":"tab-virtual-files"},{left:u(()=>[l(y,{search:r.value,"onUpdate:search":t[0]||(t[0]=s=>r.value=s),"no-padding":"",p3:""},null,8,["search"]),(i(!0),c(d,null,z(b.value,s=>(i(),c(d,{key:s.id},[a("button",{block:"","w-full":"","select-none":"",truncate:"",px2:"",py1:"","text-start":"","text-sm":"","font-mono":"",class:G(s.id===o.value?.id?"text-primary n-bg-active":"text-secondary hover:n-bg-hover"),onClick:K=>p.value=s.id},v(x(s.id)),11,R),t[1]||(t[1]=a("div",{"x-divider":""},null,-1))],64))),128))]),right:u(()=>[o.value?.content?(i(),c("div",Z,[a("div",A,[a("code",null,v(o.value.id),1)]),l(g,{"h-full":"","of-auto":"","text-sm":"",code:o.value.content,lang:"typescript"},null,8,["code"])])):(i(),q(F,{key:1},{default:u(()=>[l(C,{px6:"",py2:""},{default:u(()=>t[2]||(t[2]=[a("span",{op75:""},"Select a file to start",-1)])),_:1,__:[2]})]),_:1}))]),_:1}),l(V,null,{default:u(()=>[l(N)]),_:1})],64)}}});export{oe as default};
