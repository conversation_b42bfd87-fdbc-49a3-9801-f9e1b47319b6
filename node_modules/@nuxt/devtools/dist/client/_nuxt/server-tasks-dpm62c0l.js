import{b1 as ne,I as re,g as ue,b2 as xe,a4 as Oe,A as Te,w as Se,ay as we,b3 as Xe,a as Ee,_ as le,b4 as De,v as Ce,i as ce,F as Me,M as Ie,b5 as $e,S as Re,n as Ue,a8 as Le}from"./czf9xkmw.js";import{_ as Ne}from"./nnavbar.vue-ma0lzxrm.js";import{_ as fe}from"./nbadge-bu0b8pjx.js";import{a as Ae,p as K,k as B,q as R,J as I,a3 as g,S as m,U as j,a5 as x,aa as N,F as H,ag as G,Z,u as E,r as ie,w as Ve,ai as pe,V as A,ac as z,a4 as M,ab as P,a6 as je,av as He,W as We,X as Be,K as Fe}from"./vendor/json-editor-vue-m9gzt21j.js";import{_ as de}from"./server-route-inputs.vue-miharh1u.js";import{_ as qe}from"./ncode-block.vue-ctkw4rc7.js";import{S as ve}from"./constants-b32h69zq.js";import{_ as Pe}from"./nsection-block-m4vpsvnn.js";import{_ as Ye}from"./ndrawer.vue-dr3o1y9k.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";import"./ncheckbox.vue-nc9ppn5r.js";import"./nselect.vue-heccp04i.js";import"./client-oeqdl4pb.js";import"./nicon-title.vue-ejocqf9t.js";var te={exports:{}},se;function Ze(){return se||(se=1,function(F,U){(function(O,S){F.exports=S()})(globalThis,()=>(()=>{var L={949:(b,p,_)=>{Object.defineProperty(p,"__esModule",{value:!0}),p.CronParser=void 0;var o=_(515),v=function(){function y(t,e,a){e===void 0&&(e=!0),a===void 0&&(a=!1),this.expression=t,this.dayOfWeekStartIndexZero=e,this.monthStartIndexZero=a}return y.prototype.parse=function(){var t,e,a=(t=this.expression)!==null&&t!==void 0?t:"";if(a==="@reboot")return e=["@reboot","","","","","",""],e;if(a.startsWith("@")){var r=this.parseSpecial(this.expression);e=this.extractParts(r)}else e=this.extractParts(this.expression);return this.normalize(e),this.validate(e),e},y.prototype.parseSpecial=function(t){var e={"@yearly":"0 0 1 1 *","@annually":"0 0 1 1 *","@monthly":"0 0 1 * *","@weekly":"0 0 * * 0","@daily":"0 0 * * *","@midnight":"0 0 * * *","@hourly":"0 * * * *","@reboot":"@reboot"},a=e[t];if(!a)throw new Error("Unknown special expression.");return a},y.prototype.extractParts=function(t){if(!this.expression)throw new Error("cron expression is empty");for(var e=t.trim().split(/[ ]+/),a=0;a<e.length;a++)if(e[a].includes(",")){var r=e[a].split(",").map(function(u){return u.trim()}).filter(function(u){return u!==""}).map(function(u){return isNaN(Number(u))?u:Number(u)}).filter(function(u){return u!==null&&u!==""});r.length===0&&r.push("*"),r.sort(function(u,i){return u!==null&&i!==null?u-i:0}),e[a]=r.map(function(u){return u!==null?u.toString():""}).join(",")}if(e.length<5)throw new Error("Expression has only ".concat(e.length," part").concat(e.length==1?"":"s",". At least 5 parts are required."));if(e.length==5)e.unshift(""),e.push("");else if(e.length==6){var n=/\d{4}$/.test(e[5])||e[4]=="?"||e[2]=="?";n?e.unshift(""):e.push("")}else if(e.length>7)throw new Error("Expression has ".concat(e.length," parts; too many!"));return e},y.prototype.normalize=function(t){var e=this;if(t[3]=t[3].replace("?","*"),t[5]=t[5].replace("?","*"),t[2]=t[2].replace("?","*"),t[0].indexOf("0/")==0&&(t[0]=t[0].replace("0/","*/")),t[1].indexOf("0/")==0&&(t[1]=t[1].replace("0/","*/")),t[2].indexOf("0/")==0&&(t[2]=t[2].replace("0/","*/")),t[3].indexOf("1/")==0&&(t[3]=t[3].replace("1/","*/")),t[4].indexOf("1/")==0&&(t[4]=t[4].replace("1/","*/")),t[6].indexOf("1/")==0&&(t[6]=t[6].replace("1/","*/")),t[5]=t[5].replace(/(^\d)|([^#/\s]\d)/g,function(l){var h=l.replace(/\D/,""),d=h;return e.dayOfWeekStartIndexZero?h=="7"&&(d="0"):d=(parseInt(h)-1).toString(),l.replace(h,d)}),t[5]=="L"&&(t[5]="6"),t[3]=="?"&&(t[3]="*"),t[3].indexOf("W")>-1&&(t[3].indexOf(",")>-1||t[3].indexOf("-")>-1))throw new Error("The 'W' character can be specified only when the day-of-month is a single day, not a range or list of days.");var a={SUN:0,MON:1,TUE:2,WED:3,THU:4,FRI:5,SAT:6};for(var r in a)t[5]=t[5].replace(new RegExp(r,"gi"),a[r].toString());t[4]=t[4].replace(/(^\d{1,2})|([^#/\s]\d{1,2})/g,function(l){var h=l.replace(/\D/,""),d=h;return e.monthStartIndexZero&&(d=(parseInt(h)+1).toString()),l.replace(h,d)});var n={JAN:1,FEB:2,MAR:3,APR:4,MAY:5,JUN:6,JUL:7,AUG:8,SEP:9,OCT:10,NOV:11,DEC:12};for(var u in n)t[4]=t[4].replace(new RegExp(u,"gi"),n[u].toString());t[0]=="0"&&(t[0]=""),!/\*|\-|\,|\//.test(t[2])&&(/\*|\//.test(t[1])||/\*|\//.test(t[0]))&&(t[2]+="-".concat(t[2]));for(var i=0;i<t.length;i++)if(t[i].indexOf(",")!=-1&&(t[i]=t[i].split(",").filter(function(l){return l!==""}).join(",")||"*"),t[i]=="*/1"&&(t[i]="*"),t[i].indexOf("/")>-1&&!/^\*|\-|\,/.test(t[i])){var s=null;switch(i){case 4:s="12";break;case 5:s="6";break;case 6:s="9999";break;default:s=null;break}if(s!==null){var f=t[i].split("/");t[i]="".concat(f[0],"-").concat(s,"/").concat(f[1])}}},y.prototype.validate=function(t){var e="0-9,\\-*/";this.validateOnlyExpectedCharactersFound(t[0],e),this.validateOnlyExpectedCharactersFound(t[1],e),this.validateOnlyExpectedCharactersFound(t[2],e),this.validateOnlyExpectedCharactersFound(t[3],"0-9,\\-*/LW"),this.validateOnlyExpectedCharactersFound(t[4],e),this.validateOnlyExpectedCharactersFound(t[5],"0-9,\\-*/L#"),this.validateOnlyExpectedCharactersFound(t[6],e),this.validateAnyRanges(t)},y.prototype.validateAnyRanges=function(t){o.default.secondRange(t[0]),o.default.minuteRange(t[1]),o.default.hourRange(t[2]),o.default.dayOfMonthRange(t[3]),o.default.monthRange(t[4],this.monthStartIndexZero),o.default.dayOfWeekRange(t[5],this.dayOfWeekStartIndexZero)},y.prototype.validateOnlyExpectedCharactersFound=function(t,e){var a=t.match(new RegExp("[^".concat(e,"]+"),"gi"));if(a&&a.length)throw new Error("Expression contains invalid values: '".concat(a.toString(),"'"))},y}();p.CronParser=v},333:(b,p,_)=>{Object.defineProperty(p,"__esModule",{value:!0}),p.ExpressionDescriptor=void 0;var o=_(823),v=_(949),y=function(){function t(e,a){if(this.expression=e,this.options=a,this.expressionParts=new Array(5),!this.options.locale&&t.defaultLocale&&(this.options.locale=t.defaultLocale),!t.locales[this.options.locale]){var r=Object.keys(t.locales)[0];console.warn("Locale '".concat(this.options.locale,"' could not be found; falling back to '").concat(r,"'.")),this.options.locale=r}this.i18n=t.locales[this.options.locale],a.use24HourTimeFormat===void 0&&(a.use24HourTimeFormat=this.i18n.use24HourTimeFormatByDefault())}return t.toString=function(e,a){var r=a===void 0?{}:a,n=r.throwExceptionOnParseError,u=n===void 0?!0:n,i=r.verbose,s=i===void 0?!1:i,f=r.dayOfWeekStartIndexZero,l=f===void 0?!0:f,h=r.monthStartIndexZero,d=h===void 0?!1:h,$=r.use24HourTimeFormat,C=r.locale,W=C===void 0?null:C,V={throwExceptionOnParseError:u,verbose:s,dayOfWeekStartIndexZero:l,monthStartIndexZero:d,use24HourTimeFormat:$,locale:W};V.tzOffset&&console.warn("'tzOffset' option has been deprecated and is no longer supported.");var X=new t(e,V);return X.getFullDescription()},t.initialize=function(e,a){a===void 0&&(a="en"),t.specialCharacters=["/","-",",","*"],t.defaultLocale=a,e.load(t.locales)},t.prototype.getFullDescription=function(){var e,a,r="";try{var n=new v.CronParser(this.expression,this.options.dayOfWeekStartIndexZero,this.options.monthStartIndexZero);if(this.expressionParts=n.parse(),this.expressionParts[0]==="@reboot")return((a=(e=this.i18n).atReboot)===null||a===void 0?void 0:a.call(e))||"Run once, at startup";var u=this.getTimeOfDayDescription(),i=this.getDayOfMonthDescription(),s=this.getMonthDescription(),f=this.getDayOfWeekDescription(),l=this.getYearDescription();r+=u+i+f+s+l,r=this.transformVerbosity(r,!!this.options.verbose),r=r.charAt(0).toLocaleUpperCase()+r.substr(1)}catch(h){if(!this.options.throwExceptionOnParseError)r=this.i18n.anErrorOccuredWhenGeneratingTheExpressionD();else throw"".concat(h)}return r},t.prototype.getTimeOfDayDescription=function(){var e=this.expressionParts[0],a=this.expressionParts[1],r=this.expressionParts[2],n="";if(!o.StringUtilities.containsAny(a,t.specialCharacters)&&!o.StringUtilities.containsAny(r,t.specialCharacters)&&!o.StringUtilities.containsAny(e,t.specialCharacters))n+=this.i18n.atSpace()+this.formatTime(r,a,e);else if(!e&&a.indexOf("-")>-1&&!(a.indexOf(",")>-1)&&!(a.indexOf("/")>-1)&&!o.StringUtilities.containsAny(r,t.specialCharacters)){var u=a.split("-");n+=o.StringUtilities.format(this.i18n.everyMinuteBetweenX0AndX1(),this.formatTime(r,u[0],""),this.formatTime(r,u[1],""))}else if(!e&&r.indexOf(",")>-1&&r.indexOf("-")==-1&&r.indexOf("/")==-1&&!o.StringUtilities.containsAny(a,t.specialCharacters)){var i=r.split(",");n+=this.i18n.at();for(var s=0;s<i.length;s++)n+=" ",n+=this.formatTime(i[s],a,""),s<i.length-2&&(n+=","),s==i.length-2&&(n+=this.i18n.spaceAnd())}else{var f=this.getSecondsDescription(),l=this.getMinutesDescription(),h=this.getHoursDescription();if(n+=f,n&&l&&(n+=", "),n+=l,l===h)return n;n&&h&&(n+=", "),n+=h}return n},t.prototype.getSecondsDescription=function(){var e=this,a=this.getSegmentDescription(this.expressionParts[0],this.i18n.everySecond(),function(r){return r},function(r){return o.StringUtilities.format(e.i18n.everyX0Seconds(r),r)},function(r){return e.i18n.secondsX0ThroughX1PastTheMinute()},function(r){return r=="0"?"":parseInt(r)<20?e.i18n.atX0SecondsPastTheMinute(r):e.i18n.atX0SecondsPastTheMinuteGt20()||e.i18n.atX0SecondsPastTheMinute(r)});return a},t.prototype.getMinutesDescription=function(){var e=this,a=this.expressionParts[0],r=this.expressionParts[2],n=this.getSegmentDescription(this.expressionParts[1],this.i18n.everyMinute(),function(u){return u},function(u){return o.StringUtilities.format(e.i18n.everyX0Minutes(u),u)},function(u){return e.i18n.minutesX0ThroughX1PastTheHour()},function(u){try{return u=="0"&&r.indexOf("/")==-1&&a==""?e.i18n.everyHour():parseInt(u)<20?e.i18n.atX0MinutesPastTheHour(u):e.i18n.atX0MinutesPastTheHourGt20()||e.i18n.atX0MinutesPastTheHour(u)}catch{return e.i18n.atX0MinutesPastTheHour(u)}});return n},t.prototype.getHoursDescription=function(){var e=this,a=this.expressionParts[2],r=0,n=[];a.split("/")[0].split(",").forEach(function(s){var f=s.split("-");f.length===2&&n.push({value:f[1],index:r+1}),r+=f.length});var u=0,i=this.getSegmentDescription(a,this.i18n.everyHour(),function(s){var f=n.find(function(h){return h.value===s&&h.index===u}),l=f&&e.expressionParts[1]!=="0";return u++,l?e.formatTime(s,"59",""):e.formatTime(s,"0","")},function(s){return o.StringUtilities.format(e.i18n.everyX0Hours(s),s)},function(s){return e.i18n.betweenX0AndX1()},function(s){return e.i18n.atX0()});return i},t.prototype.getDayOfWeekDescription=function(){var e=this,a=this.i18n.daysOfTheWeek(),r=null;return this.expressionParts[5]=="*"?r="":r=this.getSegmentDescription(this.expressionParts[5],this.i18n.commaEveryDay(),function(n,u){var i=n;n.indexOf("#")>-1?i=n.substring(0,n.indexOf("#")):n.indexOf("L")>-1&&(i=i.replace("L",""));var s=parseInt(i),f=e.i18n.daysOfTheWeekInCase?e.i18n.daysOfTheWeekInCase(u)[s]:a[s];if(n.indexOf("#")>-1){var l=null,h=n.substring(n.indexOf("#")+1),d=n.substring(0,n.indexOf("#"));switch(h){case"1":l=e.i18n.first(d);break;case"2":l=e.i18n.second(d);break;case"3":l=e.i18n.third(d);break;case"4":l=e.i18n.fourth(d);break;case"5":l=e.i18n.fifth(d);break}f=l+" "+f}return f},function(n){return parseInt(n)==1?"":o.StringUtilities.format(e.i18n.commaEveryX0DaysOfTheWeek(n),n)},function(n){var u=n.substring(0,n.indexOf("-")),i=e.expressionParts[3]!="*";return i?e.i18n.commaAndX0ThroughX1(u):e.i18n.commaX0ThroughX1(u)},function(n){var u=null;if(n.indexOf("#")>-1){var i=n.substring(n.indexOf("#")+1),s=n.substring(0,n.indexOf("#"));u=e.i18n.commaOnThe(i,s).trim()+e.i18n.spaceX0OfTheMonth()}else if(n.indexOf("L")>-1)u=e.i18n.commaOnTheLastX0OfTheMonth(n.replace("L",""));else{var f=e.expressionParts[3]!="*";u=f?e.i18n.commaAndOnX0():e.i18n.commaOnlyOnX0(n)}return u}),r},t.prototype.getMonthDescription=function(){var e=this,a=this.i18n.monthsOfTheYear(),r=this.getSegmentDescription(this.expressionParts[4],"",function(n,u){return u&&e.i18n.monthsOfTheYearInCase?e.i18n.monthsOfTheYearInCase(u)[parseInt(n)-1]:a[parseInt(n)-1]},function(n){return parseInt(n)==1?"":o.StringUtilities.format(e.i18n.commaEveryX0Months(n),n)},function(n){return e.i18n.commaMonthX0ThroughMonthX1()||e.i18n.commaX0ThroughX1()},function(n){return e.i18n.commaOnlyInMonthX0?e.i18n.commaOnlyInMonthX0():e.i18n.commaOnlyInX0()});return r},t.prototype.getDayOfMonthDescription=function(){var e=this,a=null,r=this.expressionParts[3];switch(r){case"L":a=this.i18n.commaOnTheLastDayOfTheMonth();break;case"WL":case"LW":a=this.i18n.commaOnTheLastWeekdayOfTheMonth();break;default:var n=r.match(/(\d{1,2}W)|(W\d{1,2})/);if(n){var u=parseInt(n[0].replace("W","")),i=u==1?this.i18n.firstWeekday():o.StringUtilities.format(this.i18n.weekdayNearestDayX0(),u.toString());a=o.StringUtilities.format(this.i18n.commaOnTheX0OfTheMonth(),i);break}else{var s=r.match(/L-(\d{1,2})/);if(s){var f=s[1];a=o.StringUtilities.format(this.i18n.commaDaysBeforeTheLastDayOfTheMonth(f),f);break}else{if(r=="*"&&this.expressionParts[5]!="*")return"";a=this.getSegmentDescription(r,this.i18n.commaEveryDay(),function(l){return l=="L"?e.i18n.lastDay():e.i18n.dayX0?o.StringUtilities.format(e.i18n.dayX0(),l):l},function(l){return l=="1"?e.i18n.commaEveryDay():e.i18n.commaEveryX0Days(l)},function(l){return e.i18n.commaBetweenDayX0AndX1OfTheMonth(l)},function(l){return e.i18n.commaOnDayX0OfTheMonth(l)})}break}}return a},t.prototype.getYearDescription=function(){var e=this,a=this.getSegmentDescription(this.expressionParts[6],"",function(r){return/^\d+$/.test(r)?new Date(parseInt(r),1).getFullYear().toString():r},function(r){return o.StringUtilities.format(e.i18n.commaEveryX0Years(r),r)},function(r){return e.i18n.commaYearX0ThroughYearX1()||e.i18n.commaX0ThroughX1()},function(r){return e.i18n.commaOnlyInYearX0?e.i18n.commaOnlyInYearX0():e.i18n.commaOnlyInX0()});return a},t.prototype.getSegmentDescription=function(e,a,r,n,u,i){var s=null,f=e.indexOf("/")>-1,l=e.indexOf("-")>-1,h=e.indexOf(",")>-1;if(!e)s="";else if(e==="*")s=a;else if(!f&&!l&&!h)s=o.StringUtilities.format(i(e),r(e));else if(h){for(var d=e.split(","),$="",C=0;C<d.length;C++)if(C>0&&d.length>2&&($+=",",C<d.length-1&&($+=" ")),C>0&&d.length>1&&(C==d.length-1||d.length==2)&&($+="".concat(this.i18n.spaceAnd()," ")),d[C].indexOf("/")>-1||d[C].indexOf("-")>-1){var W=d[C].indexOf("-")>-1&&d[C].indexOf("/")==-1,V=this.getSegmentDescription(d[C],a,r,n,W?this.i18n.commaX0ThroughX1:u,i);W&&(V=V.replace(", ","")),$+=V}else f?$+=this.getSegmentDescription(d[C],a,r,n,u,i):$+=r(d[C]);f?s=$:s=o.StringUtilities.format(i(e),$)}else if(f){var d=e.split("/");if(s=o.StringUtilities.format(n(d[1]),d[1]),d[0].indexOf("-")>-1){var X=this.generateRangeSegmentDescription(d[0],u,r);X.indexOf(", ")!=0&&(s+=", "),s+=X}else if(d[0].indexOf("*")==-1){var Y=o.StringUtilities.format(i(d[0]),r(d[0]));Y=Y.replace(", ",""),s+=o.StringUtilities.format(this.i18n.commaStartingX0(),Y)}}else l&&(s=this.generateRangeSegmentDescription(e,u,r));return s},t.prototype.generateRangeSegmentDescription=function(e,a,r){var n="",u=e.split("-"),i=r(u[0],1),s=r(u[1],2),f=a(e);return n+=o.StringUtilities.format(f,i,s),n},t.prototype.formatTime=function(e,a,r){var n=0,u=0,i=parseInt(e)+n,s=parseInt(a)+u;s>=60?(s-=60,i+=1):s<0&&(s+=60,i-=1),i>=24?i=i-24:i<0&&(i=24+i);var f="",l=!1;this.options.use24HourTimeFormat||(l=!!(this.i18n.setPeriodBeforeTime&&this.i18n.setPeriodBeforeTime()),f=l?"".concat(this.getPeriod(i)," "):" ".concat(this.getPeriod(i)),i>12&&(i-=12),i===0&&(i=12));var h="";return r&&(h=":".concat(("00"+r).substring(r.length))),"".concat(l?f:"").concat(("00"+i.toString()).substring(i.toString().length),":").concat(("00"+s.toString()).substring(s.toString().length)).concat(h).concat(l?"":f)},t.prototype.transformVerbosity=function(e,a){if(!a&&(e=e.replace(new RegExp(", ".concat(this.i18n.everyMinute()),"g"),""),e=e.replace(new RegExp(", ".concat(this.i18n.everyHour()),"g"),""),e=e.replace(new RegExp(this.i18n.commaEveryDay(),"g"),""),e=e.replace(/\, ?$/,""),this.i18n.conciseVerbosityReplacements))for(var r=0,n=Object.entries(this.i18n.conciseVerbosityReplacements());r<n.length;r++){var u=n[r],i=u[0],s=u[1];e=e.replace(new RegExp(i,"g"),s)}return e},t.prototype.getPeriod=function(e){return e>=12?this.i18n.pm&&this.i18n.pm()||"PM":this.i18n.am&&this.i18n.am()||"AM"},t.locales={},t}();p.ExpressionDescriptor=y},747:(b,p,_)=>{Object.defineProperty(p,"__esModule",{value:!0}),p.enLocaleLoader=void 0;var o=_(486),v=function(){function y(){}return y.prototype.load=function(t){t.en=new o.en},y}();p.enLocaleLoader=v},486:(b,p)=>{Object.defineProperty(p,"__esModule",{value:!0}),p.en=void 0;var _=function(){function o(){}return o.prototype.atX0SecondsPastTheMinuteGt20=function(){return null},o.prototype.atX0MinutesPastTheHourGt20=function(){return null},o.prototype.commaMonthX0ThroughMonthX1=function(){return null},o.prototype.commaYearX0ThroughYearX1=function(){return null},o.prototype.use24HourTimeFormatByDefault=function(){return!1},o.prototype.anErrorOccuredWhenGeneratingTheExpressionD=function(){return"An error occurred when generating the expression description. Check the cron expression syntax."},o.prototype.everyMinute=function(){return"every minute"},o.prototype.everyHour=function(){return"every hour"},o.prototype.atSpace=function(){return"At "},o.prototype.everyMinuteBetweenX0AndX1=function(){return"Every minute between %s and %s"},o.prototype.at=function(){return"At"},o.prototype.spaceAnd=function(){return" and"},o.prototype.everySecond=function(){return"every second"},o.prototype.everyX0Seconds=function(){return"every %s seconds"},o.prototype.secondsX0ThroughX1PastTheMinute=function(){return"seconds %s through %s past the minute"},o.prototype.atX0SecondsPastTheMinute=function(){return"at %s seconds past the minute"},o.prototype.everyX0Minutes=function(){return"every %s minutes"},o.prototype.minutesX0ThroughX1PastTheHour=function(){return"minutes %s through %s past the hour"},o.prototype.atX0MinutesPastTheHour=function(){return"at %s minutes past the hour"},o.prototype.everyX0Hours=function(){return"every %s hours"},o.prototype.betweenX0AndX1=function(){return"between %s and %s"},o.prototype.atX0=function(){return"at %s"},o.prototype.commaEveryDay=function(){return", every day"},o.prototype.commaEveryX0DaysOfTheWeek=function(){return", every %s days of the week"},o.prototype.commaX0ThroughX1=function(){return", %s through %s"},o.prototype.commaAndX0ThroughX1=function(){return", %s through %s"},o.prototype.first=function(){return"first"},o.prototype.second=function(){return"second"},o.prototype.third=function(){return"third"},o.prototype.fourth=function(){return"fourth"},o.prototype.fifth=function(){return"fifth"},o.prototype.commaOnThe=function(){return", on the "},o.prototype.spaceX0OfTheMonth=function(){return" %s of the month"},o.prototype.lastDay=function(){return"the last day"},o.prototype.commaOnTheLastX0OfTheMonth=function(){return", on the last %s of the month"},o.prototype.commaOnlyOnX0=function(){return", only on %s"},o.prototype.commaAndOnX0=function(){return", and on %s"},o.prototype.commaEveryX0Months=function(){return", every %s months"},o.prototype.commaOnlyInX0=function(){return", only in %s"},o.prototype.commaOnTheLastDayOfTheMonth=function(){return", on the last day of the month"},o.prototype.commaOnTheLastWeekdayOfTheMonth=function(){return", on the last weekday of the month"},o.prototype.commaDaysBeforeTheLastDayOfTheMonth=function(){return", %s days before the last day of the month"},o.prototype.firstWeekday=function(){return"first weekday"},o.prototype.weekdayNearestDayX0=function(){return"weekday nearest day %s"},o.prototype.commaOnTheX0OfTheMonth=function(){return", on the %s of the month"},o.prototype.commaEveryX0Days=function(){return", every %s days"},o.prototype.commaBetweenDayX0AndX1OfTheMonth=function(){return", between day %s and %s of the month"},o.prototype.commaOnDayX0OfTheMonth=function(){return", on day %s of the month"},o.prototype.commaEveryHour=function(){return", every hour"},o.prototype.commaEveryX0Years=function(){return", every %s years"},o.prototype.commaStartingX0=function(){return", starting %s"},o.prototype.daysOfTheWeek=function(){return["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},o.prototype.monthsOfTheYear=function(){return["January","February","March","April","May","June","July","August","September","October","November","December"]},o.prototype.atReboot=function(){return"Run once, at startup"},o}();p.en=_},515:(b,p)=>{Object.defineProperty(p,"__esModule",{value:!0});function _(v,y){if(!v)throw new Error(y)}var o=function(){function v(){}return v.secondRange=function(y){for(var t=y.split(","),e=0;e<t.length;e++)if(!isNaN(parseInt(t[e],10))){var a=parseInt(t[e],10);_(a>=0&&a<=59,"seconds part must be >= 0 and <= 59")}},v.minuteRange=function(y){for(var t=y.split(","),e=0;e<t.length;e++)if(!isNaN(parseInt(t[e],10))){var a=parseInt(t[e],10);_(a>=0&&a<=59,"minutes part must be >= 0 and <= 59")}},v.hourRange=function(y){for(var t=y.split(","),e=0;e<t.length;e++)if(!isNaN(parseInt(t[e],10))){var a=parseInt(t[e],10);_(a>=0&&a<=23,"hours part must be >= 0 and <= 23")}},v.dayOfMonthRange=function(y){for(var t=y.split(","),e=0;e<t.length;e++)if(!isNaN(parseInt(t[e],10))){var a=parseInt(t[e],10);_(a>=1&&a<=31,"DOM part must be >= 1 and <= 31")}},v.monthRange=function(y,t){for(var e=y.split(","),a=0;a<e.length;a++)if(!isNaN(parseInt(e[a],10))){var r=parseInt(e[a],10);_(r>=1&&r<=12,t?"month part must be >= 0 and <= 11":"month part must be >= 1 and <= 12")}},v.dayOfWeekRange=function(y,t){for(var e=y.split(","),a=0;a<e.length;a++)if(!isNaN(parseInt(e[a],10))){var r=parseInt(e[a],10);_(r>=0&&r<=6,t?"DOW part must be >= 0 and <= 6":"DOW part must be >= 1 and <= 7")}},v}();p.default=o},823:(b,p)=>{Object.defineProperty(p,"__esModule",{value:!0}),p.StringUtilities=void 0;var _=function(){function o(){}return o.format=function(v){for(var y=[],t=1;t<arguments.length;t++)y[t-1]=arguments[t];return v.replace(/%s/g,function(e){return y.shift()})},o.containsAny=function(v,y){return y.some(function(t){return v.indexOf(t)>-1})},o}();p.StringUtilities=_}},O={};function S(b){var p=O[b];if(p!==void 0)return p.exports;var _=O[b]={exports:{}};return L[b](_,_.exports,S),_.exports}var w={};return(()=>{var b=w;Object.defineProperty(b,"__esModule",{value:!0}),b.toString=void 0;var p=S(333),_=S(747);p.ExpressionDescriptor.initialize(new _.enLocaleLoader),b.default=p.ExpressionDescriptor;var o=p.ExpressionDescriptor.toString;b.toString=o})(),w})())}(te)),te.exports}var ze=Ze();const Ge=Ae(ze),Je=["title"],Qe={"flex-none":"","text-left":""},Ke={flex:"~ gap-2","min-w-0":"","items-center":"","text-sm":""},et={class:"truncate"},tt={"text-xs":"",op50:""},nt={key:0},rt=["onClick"],ot=K({__name:"CronCollection",props:{collection:{}},setup(F){const U=F,L=ne(),O=B(!0),S=R(()=>Ge.toString(U.collection.cron));return(w,b)=>{const p=re,_=fe;return g(),I("div",null,[m("button",{flex:"~ gap-2","w-full":"","items-start":"","items-center":"",p2:"","hover-bg-active":"",title:S.value,onClick:b[0]||(b[0]=o=>O.value=!O.value)},[m("div",Qe,[x(p,{icon:"carbon:chevron-right","mb0.5":"","transform-rotate":O.value?90:0,transition:""},null,8,["transform-rotate"])]),m("span",Ke,[x(_,{class:"font-mono n-primary",textContent:N(w.collection.cron)},null,8,["textContent"]),m("span",et,N(S.value),1),m("span",tt,"("+N(w.collection.tasks.length)+")",1)])],8,Je),b[2]||(b[2]=m("div",{"x-divider":""},null,-1)),O.value?(g(),I("ul",nt,[(g(!0),I(H,null,G(w.collection.tasks,o=>(g(),I("li",{key:o},[m("button",{flex:"~ gap-2","w-full":"","items-start":"","items-center":"",px2:"",py1:"","pl-9":"","font-mono":"","hover-bg-active":"",class:Z([{"bg-active":E(L)===o}]),onClick:v=>L.value=o},N(o),11,rt),b[1]||(b[1]=m("div",{"x-divider":""},null,-1))]))),128))])):j("",!0)])}}}),at={"h-full":"","w-full":"",flex:"~ col"},it={flex:"~ col gap-2","n-navbar-glass":"","flex-none":"",p4:""},st={flex:"~ gap2 items-center"},ut={relative:"","w-full":""},lt={absolute:"","right-2":"","top-1.5":"",flex:"~ gap-1"},ct={flex:"~ gap2 wrap","w-full":"","items-center":"",px4:"",pb2:"","text-center":"","text-sm":"",border:"b base"},ft={class:"hidden md:block"},pt={flex:"~ gap2","mb--2":"","items-center":"",op50:""},dt={key:0,border:"b base",relative:"","n-code-block":""},vt={flex:"~ wrap","w-full":""},ht=["onClick"],mt={border:"b base",flex:"~ gap2","items-center":"",px4:"",py2:""},yt={key:1,"text-xs":"",op50:""},gt=K({__name:"ServerTaskDetails",props:{task:{}},emits:["openDefaultInput"],setup(F,{emit:U}){const L=F,O=U,S=B({payload:{}}),{inputDefaults:w}=ue("serverRoutes"),[b,p]=xe(),_=Oe(),o=Te(),v=ie({contentType:"text/plain",data:"",statusCode:200,error:void 0,fetchTime:0}),y=R(()=>JSON.stringify(v.data,null,2)),t=R(()=>v.contentType.includes("application/json")?"json":"text"),e=B(!1),a=B(!1),r=Se(),n=B(),u=["json"],i=B(u[0]),s=ie({query:[{active:!0,key:"",value:"",type:"string"}],body:[{active:!0,key:"",value:"",type:"string"}],headers:[]}),f=R({get:()=>s[n.value],set:k=>{s[n.value]=k}}),l=R(()=>({...X(w.value.query),...X(s.query)})),h=R(()=>({...X(w.value.headers)})),d=R(()=>Object.keys(S.value?.payload??{}).length?{...S.value}:void 0),$=R(()=>d.value?"POST":"GET"),C=R(()=>{let k=window?.location.origin;return k.charAt(k.length-1)==="/"&&(k=k.slice(0,-1)),k}),W=R(()=>{let k=new URLSearchParams(l.value).toString();k&&(k=`?${k}`);const c=`/_nitro/tasks/${L.task.name}${k}`;let D=o.value?.app.baseURL||"";return(D==="./"||D===".")&&(D=""),D.endsWith("/")&&(D=D.slice(0,-1)),D+c}),V=R(()=>C.value+W.value);function X(k){const c=Object.fromEntries(k.filter(({active:D,key:q,value:J})=>D&&q&&J!==void 0).map(({key:D,value:q})=>[D,q]));return Object.entries(c).length?c:void 0}async function Y(){a.value=!0,e.value=!0;const k=Date.now();Ie("server-tasks:run",{method:$.value});try{v.data=await $fetch(V.value,{method:"POST",headers:h.value,query:l.value,body:d.value,onResponse({response:c}){v.contentType=(c.headers.get("content-type")||"").toString().toLowerCase().trim(),v.statusCode=c.status,v.error=void 0},onResponseError(c){v.error=c.response._data,v.data=c.response._data}})}catch{}e.value=!1,v.fetchTime=Date.now()-k}const me=R(()=>{const k=[];return k.push({name:"Query",slug:"query",length:s.query.length}),k.push({name:"Body",slug:"body",length:s.body.length}),k});Ve(()=>{if(i.value==="json"&&typeof S.value=="string")try{S.value=JSON.parse(S.value)}catch{}});const oe=we("nuxt-devtools:server-tasks:inputs",()=>[],{window:window.parent});Xe([s,n],()=>{const k=oe.value?.find(c=>c.task===L.task.name);if(k){n.value||(n.value=k.tab),k.tab!==n.value&&(k.tab=n.value);const{body:c,query:D,headers:q}=k.inputs;Object.assign(s,{body:c,query:D,headers:q})}else{const c={task:L.task.name,tab:"query",inputs:s};oe.value.push(c),n.value||(n.value=c.tab)}},{immediate:!0,deep:!0,debounce:500});const ye=Ee();return(k,c)=>{const D=le,q=Ce,J=re,ae=de,ge=ce,_e=Me,ee=fe,be=qe,Q=pe("tooltip");return g(),I("div",at,[m("div",it,[m("div",st,[z((g(),I("div",null,[x(D,{class:Z(["n-badge-base n-sm",E(De)($.value)]),"pointer-events-none":"","font-mono":"",tabindex:"-1"},{default:M(()=>[P(N($.value),1)]),_:1},8,["class"])])),[[Q,`Method is ${$.value} as ${$.value==="GET"?"no":""} json body is sent`]]),m("div",ut,[x(q,{"model-value":W.value,readonly:"","flex-auto":"","font-mono":"",p:"x5 y2",n:"sm"},null,8,["model-value"]),m("div",lt,[z(x(D,{title:"Copy URL",n:"xs blue",icon:"carbon:copy",border:!1,onClick:c[0]||(c[0]=T=>E(ye)(V.value,"server-task-url"))},null,512),[[Q,"Copy URL"]]),z(x(D,{title:"Open in Editor",icon:"carbon-launch",n:"xs blue",border:!1,onClick:c[1]||(c[1]=T=>E(r)(k.task.handler))},null,512),[[Q,"Open in Editor"]])])]),x(D,{"h-full":"",n:"primary solid",onClick:Y},{default:M(()=>[x(J,{icon:"carbon:send"})]),_:1})])]),m("div",ct,[(g(!0),I(H,null,G(me.value,T=>z((g(),A(D,{key:T.slug,class:Z(n.value===T.slug?"text-primary n-primary":"border-transparent shadow-none"),onClick:ke=>n.value=T.slug},{default:M(()=>[x(J,{icon:E(ve)[T.slug]},null,8,["icon"]),m("div",ft,[P(N(T.name)+" "+N(T?.length?`(${T.length})`:"")+" ",1),m("span",null,N(E(w)[T.slug]?.length?`(${E(w)[T.slug].length})`:""),1)])]),_:2},1032,["class","onClick"])),[[Q,T.name]])),128))]),x(E(b),null,{default:M(()=>[x(ae,{modelValue:f.value,"onUpdate:modelValue":c[4]||(c[4]=T=>f.value=T),default:{active:!0,type:"string"},"max-h-xs":"","of-auto":""},{default:M(()=>[E(w)[n.value]?.length?(g(),I(H,{key:0},[m("div",pt,[c[6]||(c[6]=m("div",{"w-5":"","x-divider":""},null,-1)),c[7]||(c[7]=m("div",{"flex-none":""}," Default Inputs ",-1)),x(D,{icon:"i-carbon-edit",border:!1,onClick:c[2]||(c[2]=T=>O("openDefaultInput"))}),c[8]||(c[8]=m("div",{"x-divider":""},null,-1))]),x(ae,{modelValue:E(w)[n.value],"onUpdate:modelValue":c[3]||(c[3]=T=>E(w)[n.value]=T),disabled:"",p0:""},null,8,["modelValue"])],64)):j("",!0)]),_:1},8,["modelValue"])]),_:1}),f.value?(g(),I("div",dt,[n.value==="body"?(g(),I(H,{key:0},[m("div",vt,[(g(),I(H,null,G(u,T=>m("button",{key:T,px4:"",py2:"",border:"r base",hover:"bg-active",class:Z({"border-b":T!==i.value}),onClick:ke=>i.value=T},[m("div",{class:Z({op30:T!==i.value}),"font-mono":""},N(T),3)],10,ht)),64)),c[9]||(c[9]=m("div",{border:"b base","flex-auto":""},null,-1))]),i.value==="input"?(g(),A(E(p),{key:0})):i.value==="json"?(g(),A(E(He),je({key:1,modelValue:S.value,"onUpdate:modelValue":c[5]||(c[5]=T=>S.value=T),class:[E(_)==="dark"?"jse-theme-dark":"light","json-editor-vue of-auto text-sm outline-none"]},k.$attrs,{mode:"text","navigation-bar":!1,indentation:2,"tab-size":2}),null,16,["modelValue","class"])):j("",!0)],64)):(g(),A(E(p),{key:1}))])):j("",!0),a.value?e.value?(g(),A(_e,{key:2,"z-10":"","flex-auto":"","backdrop-blur":""},{default:M(()=>c[11]||(c[11]=[P(" Running... ")])),_:1,__:[11]})):(g(),I(H,{key:3},[m("div",mt,[c[13]||(c[13]=m("div",null,"Result",-1)),v.error?(g(),A(ee,{key:0,n:"red"},{default:M(()=>c[12]||(c[12]=[P(" Error ")])),_:1,__:[12]})):j("",!0),x(ee,{n:v.error?"orange":"green",textContent:N(v.statusCode)},null,8,["n","textContent"]),v.contentType?(g(),I("code",yt,N(v.contentType),1)):j("",!0),c[14]||(c[14]=m("div",{"flex-auto":""},null,-1)),c[15]||(c[15]=m("div",{op50:""}," Tasks finished in ",-1)),x(ee,{n:"green"},{default:M(()=>[P(N(v.fetchTime)+" ms ",1)]),_:1})]),x(be,{"flex-auto":"","overflow-auto":"","py-2":"",code:y.value,lang:t.value},null,8,["code","lang"])],64)):(g(),A(ge,{key:1},{default:M(()=>[x(D,{n:"primary",onClick:Y},{default:M(()=>[x(J,{icon:"carbon:send"}),c[10]||(c[10]=P(" Run task "))]),_:1,__:[10]})]),_:1}))])}}}),_t={"flex-none":"","text-left":""},bt={flex:"","items-center":"","text-sm":"","font-mono":""},he=K({__name:"ServerTaskListItem",props:{item:{},index:{default:0}},setup(F){const U=B(!0),L=ne();return(O,S)=>{const w=re,b=he;return g(),I("div",null,[m("button",{flex:"~ gap-2","w-full":"","items-start":"","items-center":"",px2:"",py1:"","hover-bg-active":"",class:Z([{"bg-active":E(L)===O.item.name}]),style:Be({paddingLeft:`calc(0.5rem + ${O.index*1.5}em)`}),onClick:S[0]||(S[0]=p=>{U.value=!U.value,L.value=O.item.name})},[m("div",_t,[O.item.type==="collection"?(g(),A(w,{key:0,icon:"carbon:chevron-right","mb0.5":"","transform-rotate":U.value?90:0,transition:""},null,8,["transform-rotate"])):j("",!0)]),m("span",bt,[O.item.type==="collection"?(g(),A(w,{key:0,title:`${O.item.tasks?.length} tasks`,icon:"carbon:folder",mr1:""},null,8,["title"])):(g(),A(w,{key:1,icon:"carbon:play",ml3:"",mr1:""})),P(" "+N(O.item.name),1)])],6),S[1]||(S[1]=m("div",{"x-divider":""},null,-1)),U.value?We(O.$slots,"default",{key:0},()=>[(g(!0),I(H,null,G(O.item.tasks,p=>(g(),A(b,{key:p.name,item:p,index:O.index+1},null,8,["item","index"]))),128))]):j("",!0)])}}}),kt={flex:"~ gap1","text-sm":""},xt={key:0,op50:""},Ot={op50:""},Vt=K({__name:"server-tasks",setup(F){const U=B(!1),L=$e(),O=R(()=>Object.keys(L.value?.tasks??{}).map(r=>({name:r,...L.value.tasks[r],type:"task"}))),S=R(()=>Object.entries(L.value?.scheduledTasks??{}).map(([r,n])=>({cron:r,tasks:n}))),w=ne(),{view:b,selectedTask:p,inputDefaults:_}=ue("serverTasks"),o=R(()=>{!w.value&&p.value&&(w.value=p.value.name);const r=O.value.find(n=>n.name===w.value);return w.value!==p.value?.name&&r&&(p.value=r),r}),v=B(""),y=R(()=>new Re(O.value,{keys:["key","description"],shouldSort:!0})),t=R(()=>v.value?y.value.search(v.value).map(n=>n.item):O.value),e=R(()=>{const r=[],n=(i,s)=>{i.tasks||=[],i.tasks.push(s)},u=(i,s)=>{const f=s?s.tasks?.find(h=>h.name===i):r.find(h=>h.name===i);if(f)return{...f,type:"collection"};const l={name:i,handler:i,description:"",type:"collection",tasks:[]};return s?n(s,l):r.push(l),l};return t.value.forEach(i=>{let s;const f={...i,type:"task"},h=i.name.split(":").concat();h.length>0&&h[h.length-1].includes(".")&&h.pop(),h.forEach(d=>{s=u(d,s)}),s?n(s,f):r.push(f)}),r});function a(){b.value=b.value==="tree"?"list":"tree"}return(r,n)=>{const u=le,i=Ne,s=ot,f=Le,l=gt,h=Ue,d=ce,$=de,C=Pe,W=Ye,V=pe("tooltip");return g(),I(H,null,[x(f,{"storage-key":"tab-server-tasks"},{left:M(()=>[x(f,{horizontal:"","storage-key":"tab-server-tasks-split"},{left:M(()=>[x(i,{search:v.value,"onUpdate:search":n[1]||(n[1]=X=>v.value=X),pb2:""},{actions:M(()=>[z(x(u,{"text-lg":"",icon:E(b)==="list"?"i-carbon-list":"i-carbon-tree-view-alt",title:"Toggle view",border:!1,onClick:a},null,8,["icon"]),[[V,"Toggle View"]]),z(x(u,{"text-lg":"",icon:"i-carbon-cics-sit-overrides",title:"Default Inputs",border:!1,onClick:n[0]||(n[0]=X=>U.value=!U.value)},null,512),[[V,"Default Inputs"]])]),default:M(()=>[m("div",kt,[v.value?(g(),I("span",xt,N(t.value.length)+" matched · ",1)):j("",!0),m("span",Ot,N(O.value?.length)+" tasks in total",1)])]),_:1},8,["search"]),(g(!0),I(H,null,G(E(b)==="tree"?e.value:t.value,X=>(g(),A(he,{key:X.name,item:X},null,8,["item"]))),128))]),right:M(()=>[n[6]||(n[6]=m("div",{px4:"",py2:"",border:"b base"}," Cron groups ",-1)),m("div",null,[m("ul",null,[(g(!0),I(H,null,G(S.value,X=>(g(),I("li",{key:X.cron},[x(s,{collection:X},null,8,["collection"])]))),128))])])]),_:1})]),right:M(()=>[(g(),A(Fe,{max:10},[o.value?(g(),A(l,{key:o.value.name,task:o.value,onOpenDefaultInput:n[2]||(n[2]=X=>U.value=!0)},null,8,["task"])):j("",!0)],1024)),o.value?j("",!0):(g(),A(d,{key:0},{default:M(()=>[x(h,{px6:"",py2:""},{default:M(()=>n[7]||(n[7]=[m("span",{op75:""},"Select a task to start",-1)])),_:1,__:[7]})]),_:1}))]),_:1}),x(W,{modelValue:U.value,"onUpdate:modelValue":n[4]||(n[4]=X=>U.value=X),"auto-close":"","max-w-xl":"","min-w-xl":"",onClose:n[5]||(n[5]=X=>U.value=!1)},{default:M(()=>[m("div",null,[n[8]||(n[8]=m("div",{p4:"",border:"b base"},[m("span",{"text-lg":""},"Default Inputs"),m("br"),m("span",{"text-white":"",op50:""},"Merged as default for every task in DevTools")],-1)),x(C,{text:`Query ${E(_).query.length?`(${E(_).query.length})`:""}`,padding:!1,icon:E(ve).query},{default:M(()=>[x($,{modelValue:E(_).query,"onUpdate:modelValue":n[3]||(n[3]=X=>E(_).query=X),py0:"",default:{active:!0,type:"string"}},null,8,["modelValue"])]),_:1},8,["text","icon"])])]),_:1},8,["modelValue"])],64)}}});export{Vt as default};
