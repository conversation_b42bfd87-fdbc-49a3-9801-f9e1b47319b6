import{be as $e,I as De,b4 as se,bf as Ke,b2 as Qe,a4 as Xe,A as Ye,f as Ze,w as et,g as Ee,ay as tt,b3 as ot,a as nt,_ as Re,v as at,i as Ve,F as lt,M as st,bg as rt,S as it,n as ut,a8 as dt}from"./czf9xkmw.js";import{_ as ct}from"./nnavbar.vue-ma0lzxrm.js";import{_ as je}from"./nbadge-bu0b8pjx.js";import{p as ye,k as P,J as k,a3 as r,S as u,W as pt,U as N,Z as M,V as D,aa as O,u as b,ab as K,X as ft,F as L,ag as H,D as me,x as Ie,b as Ue,C as vt,z as mt,r as ve,q as V,w as yt,ai as Oe,a5 as v,a4 as T,ac as Z,a6 as ht,av as gt,K as bt}from"./vendor/json-editor-vue-m9gzt21j.js";import{_ as _t}from"./nselect.vue-heccp04i.js";import{_ as Ne}from"./server-route-inputs.vue-miharh1u.js";import{_ as kt}from"./code-snippets.vue-k04b5ejb.js";import{_ as wt}from"./data-schema-button.vue-i6u1wh6f.js";import{_ as xt}from"./ncode-block.vue-ctkw4rc7.js";import{d as Ct}from"./vendor/unocss-oyl7opas.js";import{C as xe,S as Le}from"./constants-b32h69zq.js";import{_ as St}from"./nsection-block-m4vpsvnn.js";import{_ as Tt}from"./ndrawer.vue-dr3o1y9k.js";import"./vendor/shiki-imfwxqoq.js";import"./ncheckbox.vue-nc9ppn5r.js";import"./client-oeqdl4pb.js";import"./nicon-title.vue-ejocqf9t.js";const Pe=ye({__name:"ServerRouteListItem",props:{item:{},index:{default:0}},setup(e){const s=P(!0),n=$e();return(o,m)=>{const f=De,d=je,_=Pe;return r(),k("div",null,[u("button",{flex:"~ gap-2","w-full":"","items-start":"","items-center":"",px2:"",py1:"","hover-bg-active":"",class:M([{"bg-active":b(n)===o.item.filepath}]),style:ft({paddingLeft:`calc(0.5rem + ${o.index*1.5}em)`}),onClick:m[0]||(m[0]=h=>{s.value=!s.value,n.value=o.item.filepath})},[u("div",{class:M({"w-12":!o.item.routes}),"flex-none":"","text-left":""},[o.item.type==="collection"?(r(),D(f,{key:0,icon:"carbon:chevron-right","mb0.5":"","transform-rotate":s.value?90:0,transition:""},null,8,["transform-rotate"])):(r(),D(d,{key:1,class:M(b(se)(o.item.method||"*")),textContent:O((o.item.method||"*").toUpperCase())},null,8,["class","textContent"]))],2),u("span",{class:M({"flex items-center":o.item.routes}),"text-sm":"","font-mono":""},[o.item.type==="collection"?(r(),D(f,{key:0,title:`${o.item.routes?.length} routes`,icon:"carbon:folder",mr1:""},null,8,["title"])):N("",!0),K(" "+O(o.item.route),1)],2)],6),m[1]||(m[1]=u("div",{"x-divider":""},null,-1)),s.value?pt(o.$slots,"default",{key:0},()=>[(r(!0),k(L,null,H(o.item.routes,h=>(r(),D(_,{key:h.filepath,item:h,index:o.index+1},null,8,["item","index"]))),128))]):N("",!0)])}}});function $t(e,s){if(typeof e!="string")throw new TypeError("argument str must be a string");const n={},o=s||{},m=o.decode||Dt;let f=0;for(;f<e.length;){const d=e.indexOf("=",f);if(d===-1)break;let _=e.indexOf(";",f);if(_===-1)_=e.length;else if(_<d){f=e.lastIndexOf(";",d-1)+1;continue}const h=e.slice(f,d).trim();if(o?.filter&&!o?.filter(h)){f=_+1;continue}if(n[h]===void 0){let i=e.slice(d+1,_).trim();i.codePointAt(0)===34&&(i=i.slice(1,-1)),n[h]=Et(i,m)}f=_+1}return n}function Dt(e){return e.includes("%")?decodeURIComponent(e):e}function Et(e,s){try{return s(e)}catch{return e}}const ae=/^[\u0009\u0020-\u007E\u0080-\u00FF]+$/;function Ce(e,s,n){const o=n||{},m=o.encode||encodeURIComponent;if(typeof m!="function")throw new TypeError("option encode is invalid");if(!ae.test(e))throw new TypeError("argument name is invalid");const f=m(s);if(f&&!ae.test(f))throw new TypeError("argument val is invalid");let d=e+"="+f;if(o.maxAge!==void 0&&o.maxAge!==null){const _=o.maxAge-0;if(Number.isNaN(_)||!Number.isFinite(_))throw new TypeError("option maxAge is invalid");d+="; Max-Age="+Math.floor(_)}if(o.domain){if(!ae.test(o.domain))throw new TypeError("option domain is invalid");d+="; Domain="+o.domain}if(o.path){if(!ae.test(o.path))throw new TypeError("option path is invalid");d+="; Path="+o.path}if(o.expires){if(!Rt(o.expires)||Number.isNaN(o.expires.valueOf()))throw new TypeError("option expires is invalid");d+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(d+="; HttpOnly"),o.secure&&(d+="; Secure"),o.priority)switch(typeof o.priority=="string"?o.priority.toLowerCase():o.priority){case"low":{d+="; Priority=Low";break}case"medium":{d+="; Priority=Medium";break}case"high":{d+="; Priority=High";break}default:throw new TypeError("option priority is invalid")}if(o.sameSite)switch(typeof o.sameSite=="string"?o.sameSite.toLowerCase():o.sameSite){case!0:{d+="; SameSite=Strict";break}case"lax":{d+="; SameSite=Lax";break}case"strict":{d+="; SameSite=Strict";break}case"none":{d+="; SameSite=None";break}default:throw new TypeError("option sameSite is invalid")}return o.partitioned&&(d+="; Partitioned"),d}function Rt(e){return Object.prototype.toString.call(e)==="[object Date]"||e instanceof Date}function A(e){if(typeof e!="object")return e;var s,n,o=Object.prototype.toString.call(e);if(o==="[object Object]"){if(e.constructor!==Object&&typeof e.constructor=="function"){n=new e.constructor;for(s in e)e.hasOwnProperty(s)&&n[s]!==e[s]&&(n[s]=A(e[s]))}else{n={};for(s in e)s==="__proto__"?Object.defineProperty(n,s,{value:A(e[s]),configurable:!0,enumerable:!0,writable:!0}):n[s]=A(e[s])}return n}if(o==="[object Array]"){for(s=e.length,n=Array(s);s--;)n[s]=A(e[s]);return n}return o==="[object Set]"?(n=new Set,e.forEach(function(m){n.add(A(m))}),n):o==="[object Map]"?(n=new Map,e.forEach(function(m,f){n.set(A(f),A(m))}),n):o==="[object Date]"?new Date(+e):o==="[object RegExp]"?(n=new RegExp(e.source,e.flags),n.lastIndex=e.lastIndex,n):o==="[object DataView]"?new e.constructor(A(e.buffer)):o==="[object ArrayBuffer]"?e.slice(0):o.slice(-6)==="Array]"?new e.constructor(e):e}const Vt={path:"/",watch:!0,decode:e=>Ct(decodeURIComponent(e)),encode:e=>encodeURIComponent(typeof e=="string"?e:JSON.stringify(e))},le=window.cookieStore;function jt(e,s){const n={...Vt,...s};n.filter??=i=>i===e;const o=Se(n)||{};let m;n.maxAge!==void 0?m=n.maxAge*1e3:n.expires&&(m=n.expires.getTime()-Date.now());const f=m!==void 0&&m<=0,d=f||o[e]===void 0||o[e]===null,_=A(f?void 0:o[e]??n.default?.()),h=m&&!f?Ot(_,m,n.watch&&n.watch!=="shallow"):P(_);{let i=null;try{!le&&typeof BroadcastChannel<"u"&&(i=new BroadcastChannel(`nuxt:cookies:${e}`))}catch{}const E=(w=!1)=>{!w&&(n.readonly||Ke(h.value,o[e]))||(Ut(e,h.value,n),o[e]=A(h.value),i?.postMessage({value:n.encode(h.value)}))},I=w=>{const y=w.refresh?Se(n)?.[e]:n.decode(w.value);B=!0,h.value=y,o[e]=A(y),mt(()=>{B=!1})};let B=!1;const J=!!Ue();if(J&&me(()=>{B=!0,E(),i?.close()}),le){const w=y=>{const U=y.changed.find(C=>C.name===e),g=y.deleted.find(C=>C.name===e);U&&I({value:U.value}),g&&I({value:null})};le.addEventListener("change",w),J&&me(()=>le.removeEventListener("change",w))}else i&&(i.onmessage=({data:w})=>I(w));n.watch&&Ie(h,()=>{B||E()},{deep:n.watch!=="shallow"}),d&&E(d)}return h}function Se(e={}){return $t(document.cookie,e)}function It(e,s,n={}){return s==null?Ce(e,s,{...n,maxAge:-1}):Ce(e,s,n)}function Ut(e,s,n={}){document.cookie=It(e,s,n)}const Te=2147483647;function Ot(e,s,n){let o,m,f=0;const d=n?P(e):{value:e};return Ue()&&me(()=>{m?.(),clearTimeout(o)}),vt((_,h)=>{n&&(m=Ie(d,h));function i(){f=0,clearTimeout(o);const E=s-f,I=E<Te?E:Te;o=setTimeout(()=>{if(f+=I,f<s)return i();d.value=void 0,h()},I)}return{get(){return _(),d.value},set(E){i(),d.value=E,h()}}})}const Nt={"h-full":"","w-full":"",flex:"~ col"},Lt={flex:"~ col gap-2","n-navbar-glass":"","flex-none":"",p4:""},Pt={flex:"~ gap2 items-center"},At={relative:"","w-full":""},Bt={absolute:"","right-2":"","top-1.5":"",flex:"~ gap-1"},qt={flex:"~ gap2 wrap","w-full":"","items-center":"",px4:"",pb2:"","text-center":"","text-sm":"",border:"b base"},Mt={class:"hidden md:block"},Ft={key:0,border:"b base","items-center":"",px4:"",py2:"",grid:"~ cols-[max-content_1fr] gap-2"},zt={"text-right":"","font-mono":""},Ht={key:1,border:"b base",p4:"",flex:"~ col gap-4","font-mono":""},Jt={flex:"~ gap-4"},Wt={flex:"~ gap2","mb--2":"","items-center":"",op50:""},Gt={key:2},Kt={key:3,border:"b base",relative:"","n-code-block":""},Qt={flex:"~ wrap","w-full":""},Xt=["onClick"],Yt={border:"b base",flex:"~ gap2","items-center":"",px4:"",py2:""},Zt={key:1,"text-xs":"",op50:""},eo={key:0,"flex-auto":"","overflow-auto":""},to={border:"~ base","h-full":"","w-full":"",rounded:""},oo=["data"],no={key:2,"flex-auto":"","overflow-auto":"",p4:""},ao={border:"~ base",rounded:""},lo=["src"],so={key:1,controls:"",rounded:""},ro=["src"],io=ye({__name:"ServerRouteDetails",props:{route:{}},emits:["openDefaultInput"],setup(e,{emit:s}){const n=e,o=s,[m,f]=Qe(),d=Xe(),_=Ye(),h=Ze(),i=ve({contentType:"text/plain",data:"",statusCode:200,error:void 0,fetchTime:0}),E=V(()=>i.contentType.includes("application/json")?"json":i.contentType.includes("text/html")?"html":i.contentType.includes("text/css")?"css":i.contentType.includes("text/javascript")?"javascript":i.contentType.includes("image")||i.contentType.includes("video")?"media":i.contentType.includes("text/xml")||i.contentType.includes("application/xml")?"xml":i.contentType.includes("application/pdf")?"pdf":"text"),I=V(()=>{if(E.value==="json")return JSON.stringify(i.data,null,2);if(E.value==="media"||E.value==="pdf"){const a=new Blob([i.data],{type:i.contentType});return URL.createObjectURL(a)}return i.data}),B=P(!1),J=P(!1),w=et(),y=V(()=>n.route.route?.split(/((?:\*\*)?:\w+)/g)),U=V(()=>y.value?.filter(a=>a.startsWith(":")||a.startsWith("**:"))||[]),g=P(n.route.method||"GET"),C=P({}),x=ve({query:[{active:!0,key:"",value:"",type:"string"}],body:[{active:!0,key:"",value:"",type:"string"}],headers:[{active:!0,key:"Content-Type",value:"application/json",type:"string"}]}),j=P({}),{inputDefaults:S,sendFrom:F}=Ee("serverRoutes"),Q=V(()=>h?.value?.app?.$fetch?F.value:"devtools"),re=["GET","POST","PUT","PATCH","DELETE","HEAD"],ie=["PATCH","POST","PUT","DELETE"],ee=V(()=>ie.includes(g.value.toUpperCase())),c=P(),te=["input","json"],W=P(te[0]),ue=V({get:()=>x[c.value],set:a=>{x[c.value]=a}}),he=V(()=>({...G(S.value.query),...G(x.query)})),ge=V(()=>({...G(S.value.headers),...G(x.headers)})),de=V(()=>ee.value?W.value==="json"?{...G(S.value.body),...j.value}:{...G(S.value.body),...G(x.body)}:void 0),Ae=V(()=>{let a=window?.location.origin;return a.charAt(a.length-1)==="/"&&(a=a.slice(0,-1)),a}),oe=V(()=>{let a=new URLSearchParams(he.value).toString();a&&(a=`?${a}`);const t=(y.value?.map(R=>(R.startsWith(":")||R.startsWith("**:"))&&C.value[R]||R).join("")||"")+a;let p=_.value?.app.baseURL||"";return(p==="./"||p===".")&&(p=""),p.endsWith("/")&&(p=p.slice(0,-1)),p+t}),be=V(()=>Ae.value+oe.value);function G(a){const t=Object.fromEntries(a.filter(({active:p,key:R,value:$})=>p&&R&&$!==void 0).map(({key:p,value:R})=>[p,R]));return Object.entries(t).length?t:void 0}async function _e(){J.value=!0,B.value=!0;const a=Date.now(),t=Q.value==="app"?h.value.app.$fetch:$fetch;st("server-routes:fetch",{method:g.value,sendFrom:Q.value});try{i.data=await t(be.value,{method:g.value.toUpperCase(),headers:ge.value,query:he.value,body:de.value,onResponse({response:p}){i.contentType=(p.headers.get("content-type")||"").toString().toLowerCase().trim(),i.statusCode=p.status,i.error=void 0},onResponseError(p){i.error=p.response._data,i.data=p.response._data}})}catch{}B.value=!1,i.fetchTime=Date.now()-a}const ke=V(()=>{const a=[],t=[],p=Object.entries(ge.value).filter(([$,q])=>$&&q&&!($==="Content-Type"&&q==="application/json")).map(([$,q])=>`  '${$}': '${q}'`).join(`,
`);g.value.toUpperCase()!=="GET"&&t.push(`method: '${g.value.toUpperCase()}'`),p&&t.push(`headers: {
${p}
}`),de.value&&t.push(`body: ${JSON.stringify(de.value,null,2)}`);const R=t.length?`, {
${t.join(`,
`).split(`
`).map($=>`  ${$}`).join(`
`)}
}`:"";return a.push({name:"useFetch",lang:"javascript",docs:xe.nuxt.useFetch,code:`const { data, pending, error, refresh } = useFetch('${oe.value}'${R})`}),a.push({name:"$fetch",lang:"javascript",docs:xe.nuxt.$fetch,code:`await $fetch('${oe.value}'${R})`}),a}),X=P(qe()),z=ve({key:"",value:""}),Be=V(()=>{const a=[];return U.value.length&&a.push({name:"Params",slug:"params",length:U.value.length}),a.push({name:"Query",slug:"query",length:x.query.length}),ee.value&&a.push({name:"Body",slug:"body",length:x.body.length}),a.push({name:"Headers",slug:"headers",length:x.headers.length}),a.push({name:"Cookies",slug:"cookies",length:X.value.length}),a.push({name:"Snippets",slug:"snippet"}),a});function qe(){return document.cookie.split("; ").map(a=>{const[t,p]=a.split("=");return{key:t,value:p}}).filter(a=>a.key)}function ce(a,t){if(!a)return;const p=X.value.find($=>$.key===a),R=jt(a);p!==void 0?t===void 0&&(X.value=X.value.filter($=>$.key!==a)):(X.value.push({key:a,value:t}),z.key="",z.value=""),R.value=t}yt(()=>{W.value==="json"&&typeof j.value=="string"&&(j.value=JSON.parse(j.value))});const pe=tt("nuxt-devtools:server-routes:inputs",()=>[],{window:window.parent});ot([x,c],()=>{const a=pe.value?.find(t=>t.path===n.route.filepath);if(a){c.value||(c.value=a.tab),a.tab!==c.value&&(a.tab=c.value);const{body:t,query:p,headers:R,params:$}=a.inputs;Object.assign(x,{body:t,query:p,headers:R}),C.value=$}else{const t={path:n.route.filepath,tab:U.value.length?"params":"query",inputs:{...x,...U.value.length?{params:C.value}:{}}};pe.value.push(t),c.value||(c.value=t.tab)}},{immediate:!0,deep:!0,debounce:500});function Me(){pe.value=[],x.body=[],x.query=[],x.headers=[],C.value={},c.value=U.value.length?"params":"query"}const Fe=nt();return(a,t)=>{const p=Re,R=_t,$=at,q=De,we=Ne,ze=kt,He=Ve,Je=lt,fe=je,We=wt,Ge=xt,ne=Oe("tooltip");return r(),k("div",Nt,[u("div",Lt,[u("div",Pt,[a.route.method?(r(),D(p,{key:0,class:M(["n-badge-base n-sm",b(se)(g.value)]),"pointer-events-none":"","font-mono":"",tabindex:"-1"},{default:T(()=>[K(O(g.value.toUpperCase()),1)]),_:1},8,["class"])):(r(),D(R,{key:1,modelValue:g.value,"onUpdate:modelValue":t[0]||(t[0]=l=>g.value=l),class:M(["n-badge-base n-sm",b(se)(g.value)])},{default:T(()=>[(r(),k(L,null,H(re,l=>u("option",{key:l,class:M(b(se)(l))},O(l.toUpperCase()),3)),64))]),_:1},8,["modelValue","class"])),u("div",At,[v($,{"model-value":oe.value,readonly:"","flex-auto":"","font-mono":"",p:"x5 y2",n:"sm"},null,8,["model-value"]),u("div",Bt,[Z(v(p,{title:"Copy URL",n:"xs blue",icon:"carbon:copy",border:!1,onClick:t[1]||(t[1]=l=>b(Fe)(be.value,"server-route-url"))},null,512),[[ne,"Copy URL"]]),Z(v(p,{title:"Open in Editor",icon:"carbon-launch",n:"xs blue",border:!1,onClick:t[2]||(t[2]=l=>b(w)(a.route.filepath))},null,512),[[ne,"Open in Editor"]])])]),v(p,{"h-full":"",n:"primary solid",onClick:_e},{default:T(()=>[v(q,{icon:"carbon:send"})]),_:1})])]),u("div",qt,[(r(!0),k(L,null,H(Be.value,l=>Z((r(),D(p,{key:l.slug,class:M(c.value===l.slug?"text-primary n-primary":"border-transparent shadow-none"),onClick:Y=>c.value=l.slug},{default:T(()=>[v(q,{icon:b(Le)[l.slug]},null,8,["icon"]),u("div",Mt,[K(O(l.name)+" "+O(l?.length?`(${l.length})`:"")+" ",1),u("span",null,O(b(S)[l.slug]?.length?`(${b(S)[l.slug].length})`:""),1)])]),_:2},1032,["class","onClick"])),[[ne,l.name]])),128)),t[12]||(t[12]=u("div",{"flex-auto":""},null,-1)),t[13]||(t[13]=u("div",{"text-xs":"",op50:""}," Send from ",-1)),v(R,{modelValue:Q.value,"onUpdate:modelValue":t[3]||(t[3]=l=>Q.value=l),class:"n-xs",disabled:!b(h)?.app?.$fetch},{default:T(()=>t[11]||(t[11]=[u("option",{value:"app"}," App ",-1),u("option",{value:"devtools"}," DevTools ",-1)])),_:1,__:[11]},8,["modelValue","disabled"]),Z(v(p,{n:"orange",class:"p-3",icon:"i-carbon-clean",onClick:Me},null,512),[[ne,"Clear Inputs Saved Cache"]])]),c.value==="params"?(r(),k("div",Ft,[(r(!0),k(L,null,H(U.value,l=>(r(),k(L,{key:l},[u("div",zt,O(l),1),v($,{modelValue:C.value[l],"onUpdate:modelValue":Y=>C.value[l]=Y,placeholder:l,"flex-1":""},null,8,["modelValue","onUpdate:modelValue","placeholder"])],64))),128))])):N("",!0),c.value==="cookies"?(r(),k("div",Ht,[(r(!0),k(L,null,H(X.value,l=>(r(),k("div",{key:l.key,flex:"~ gap-4 items-center"},[v($,{placeholder:"Key...","model-value":l.key,disabled:"","op-70":""},null,8,["model-value"]),v($,{placeholder:"Value...","model-value":l.value,"flex-1":"",n:"primary",onInput:Y=>ce(l.key,Y.target?.value)},null,8,["model-value","onInput"]),v(p,{title:"Delete",n:"red",onClick:Y=>ce(l.key,void 0)},{default:T(()=>[v(q,{icon:"i-carbon-trash-can"})]),_:2},1032,["onClick"])]))),128)),u("div",Jt,[v($,{modelValue:z.key,"onUpdate:modelValue":t[4]||(t[4]=l=>z.key=l),placeholder:"Key",n:"primary","flex-1":""},null,8,["modelValue"]),v($,{modelValue:z.value,"onUpdate:modelValue":t[5]||(t[5]=l=>z.value=l),placeholder:"Value",n:"primary","flex-1":""},null,8,["modelValue"]),v(p,{title:"Add",n:"primary",onClick:t[6]||(t[6]=l=>ce(z.key,z.value))},{default:T(()=>[v(q,{icon:"i-carbon-save"})]),_:1})])])):N("",!0),v(b(m),null,{default:T(()=>[v(we,{modelValue:ue.value,"onUpdate:modelValue":t[9]||(t[9]=l=>ue.value=l),default:{active:!0,type:"string"},"max-h-xs":"","of-auto":""},{default:T(()=>[b(S)[c.value]?.length?(r(),k(L,{key:0},[u("div",Wt,[t[14]||(t[14]=u("div",{"w-5":"","x-divider":""},null,-1)),t[15]||(t[15]=u("div",{"flex-none":""}," Default Inputs ",-1)),v(p,{icon:"i-carbon-edit",border:!1,onClick:t[7]||(t[7]=l=>o("openDefaultInput"))}),t[16]||(t[16]=u("div",{"x-divider":""},null,-1))]),v(we,{modelValue:b(S)[c.value],"onUpdate:modelValue":t[8]||(t[8]=l=>b(S)[c.value]=l),disabled:"",p0:""},null,8,["modelValue"])],64)):N("",!0)]),_:1},8,["modelValue"])]),_:1}),c.value==="snippet"?(r(),k("div",Gt,[ke.value.length?(r(),D(ze,{key:0,"code-snippets":ke.value},null,8,["code-snippets"])):N("",!0)])):ue.value?(r(),k("div",Kt,[c.value==="body"?(r(),k(L,{key:0},[u("div",Qt,[(r(),k(L,null,H(te,l=>u("button",{key:l,px4:"",py2:"",border:"r base",hover:"bg-active",class:M({"border-b":l!==W.value}),onClick:Y=>W.value=l},[u("div",{class:M({op30:l!==W.value}),"font-mono":""},O(l),3)],10,Xt)),64)),t[17]||(t[17]=u("div",{border:"b base","flex-auto":""},null,-1))]),W.value==="input"?(r(),D(b(f),{key:0})):W.value==="json"?(r(),D(b(gt),ht({key:1,modelValue:j.value,"onUpdate:modelValue":t[10]||(t[10]=l=>j.value=l),class:[b(d)==="dark"?"jse-theme-dark":"light","json-editor-vue of-auto text-sm outline-none"]},a.$attrs,{mode:"text","navigation-bar":!1,indentation:2,"tab-size":2}),null,16,["modelValue","class"])):N("",!0)],64)):(r(),D(b(f),{key:1}))])):N("",!0),J.value?B.value?(r(),D(Je,{key:5,"z-10":"","flex-auto":"","backdrop-blur":""},{default:T(()=>t[19]||(t[19]=[K(" Fetching... ")])),_:1,__:[19]})):(r(),k(L,{key:6},[u("div",Yt,[t[21]||(t[21]=u("div",null,"Response",-1)),i.error?(r(),D(fe,{key:0,n:"red"},{default:T(()=>t[20]||(t[20]=[K(" Error ")])),_:1,__:[20]})):N("",!0),v(fe,{n:i.error?"orange":"green",textContent:O(i.statusCode)},null,8,["n","textContent"]),i.contentType?(r(),k("code",Zt,O(i.contentType),1)):N("",!0),i.contentType==="application/json"?(r(),D(We,{key:2,getter:()=>({input:I.value})},null,8,["getter"])):N("",!0),t[22]||(t[22]=u("div",{"flex-auto":""},null,-1)),t[23]||(t[23]=u("div",{op50:""}," Request finished in ",-1)),v(fe,{n:"green"},{default:T(()=>[K(O(i.fetchTime)+" ms ",1)]),_:1})]),E.value==="pdf"?(r(),k("div",eo,[u("div",to,[u("object",{data:I.value,type:"application/pdf","flex-auto":"",width:"100%",height:"100%",rounded:""},null,8,oo)])])):E.value!=="media"?(r(),D(Ge,{key:1,"flex-auto":"","overflow-auto":"","py-2":"",code:I.value,lang:E.value},null,8,["code","lang"])):(r(),k("div",no,[u("div",ao,[i.contentType.includes("image")?(r(),k("img",{key:0,rounded:"",src:I.value},null,8,lo)):(r(),k("video",so,[u("source",{src:I.value,type:"video/mp4"},null,8,ro)]))])]))],64)):(r(),D(He,{key:4},{default:T(()=>[v(p,{n:"primary",onClick:_e},{default:T(()=>[v(q,{icon:"carbon:send"}),t[18]||(t[18]=K(" Send request "))]),_:1,__:[18]})]),_:1}))])}}}),uo={flex:"~ gap1","text-sm":""},co={key:0,op50:""},po={op50:""},Ro=ye({__name:"server-routes",setup(e){const s=P(!1),n=rt(),o=$e(),{selectedRoute:m,view:f,inputDefaults:d}=Ee("serverRoutes"),_=V(()=>{!o.value&&m.value&&(o.value=m.value.filepath);const w=n.value?.find(y=>y.filepath===o.value);return o.value!==m.value?.filepath&&w&&(m.value=w),w}),h=P(""),i=V(()=>new it(n.value||[],{keys:["method","route"],shouldSort:!0})),E=V(()=>n.value?h.value?i.value.search(h.value).map(y=>y.item):n.value:[]),I=V(()=>{const w=[],y=(g,C)=>{g.routes=g.routes||[],g.routes.push(C)},U=(g,C)=>{const x=C?C.routes?.find(S=>S.route===g):w.find(S=>S.route===g);if(x)return x;const j={route:g,filepath:g.replace(/\W/g,"-").toLowerCase(),type:"collection",routes:[]};return C?y(C,j):w.push(j),j};return E.value.forEach(g=>{let C,x;const j=g.filepath.split("/"),S=j.slice(j.indexOf("server")+1);if(g.type==="runtime"){S[0]="runtime";const F=j.indexOf("dist");F!==-1&&(C=j[F-1],C&&S.splice(1,0,C))}S.length>0&&S[S.length-1].includes(".")&&S.pop(),S.forEach(F=>{x=U(F,x)}),x?y(x,g):w.push(g)}),w});function B(){f.value=f.value==="tree"?"list":"tree"}function J(w){return w.charAt(0).toUpperCase()+w.slice(1)}return(w,y)=>{const U=Re,g=ct,C=Pe,x=io,j=ut,S=Ve,F=dt,Q=Ne,re=St,ie=Tt,ee=Oe("tooltip");return r(),k(L,null,[v(F,{"storage-key":"tab-server-routes"},{left:T(()=>[v(g,{search:h.value,"onUpdate:search":y[1]||(y[1]=c=>h.value=c),pb2:""},{actions:T(()=>[Z(v(U,{"text-lg":"",icon:b(f)==="list"?"i-carbon-list":"i-carbon-tree-view-alt",title:"Toggle view",border:!1,onClick:B},null,8,["icon"]),[[ee,"Toggle View"]]),Z(v(U,{"text-lg":"",icon:"i-carbon-cics-sit-overrides",title:"Default Inputs",border:!1,onClick:y[0]||(y[0]=c=>s.value=!s.value)},null,512),[[ee,"Default Inputs"]])]),default:T(()=>[u("div",uo,[h.value?(r(),k("span",co,O(E.value.length)+" matched · ",1)):N("",!0),u("span",po,O(b(n)?.length)+" routes in total",1)])]),_:1},8,["search"]),(r(!0),k(L,null,H(b(f)==="tree"?I.value:E.value,c=>(r(),D(C,{key:c.filepath,item:c},null,8,["item"]))),128))]),right:T(()=>[(r(),D(bt,{max:10},[_.value?(r(),D(x,{key:_.value.filepath,route:_.value,onOpenDefaultInput:y[2]||(y[2]=c=>s.value=!0)},null,8,["route"])):N("",!0)],1024)),_.value?N("",!0):(r(),D(S,{key:0},{default:T(()=>[v(j,{px6:"",py2:""},{default:T(()=>y[5]||(y[5]=[u("span",{op75:""},"Select a route to start",-1)])),_:1,__:[5]})]),_:1}))]),_:1}),v(ie,{modelValue:s.value,"onUpdate:modelValue":y[3]||(y[3]=c=>s.value=c),"auto-close":"","max-w-xl":"","min-w-xl":"",onClose:y[4]||(y[4]=c=>s.value=!1)},{default:T(()=>[u("div",null,[y[6]||(y[6]=u("div",{p4:"",border:"b base"},[u("span",{"text-lg":""},"Default Inputs"),u("br"),u("span",{"text-white":"",op50:""},"Merged as default for every request in DevTools")],-1)),(r(!0),k(L,null,H(Object.keys(b(d)),c=>(r(),D(re,{key:c,text:`${J(c)} ${b(d)[c].length?`(${b(d)[c].length})`:""}`,padding:!1,icon:b(Le)[c]},{default:T(()=>[v(Q,{modelValue:b(d)[c],"onUpdate:modelValue":te=>b(d)[c]=te,py0:"",default:{active:!0,type:"string"}},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["text","icon"]))),128))])]),_:1},8,["modelValue"])],64)}}});export{Ro as default};
