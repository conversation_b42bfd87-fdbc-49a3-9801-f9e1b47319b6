import{i as w,n as y,o as C,_ as B,I as V,M as $}from"./czf9xkmw.js";import{_ as v}from"./nmarkdown.vue-mk2gi3ky.js";import{p as I,V as a,a3 as t,a4 as o,a5 as c,S as i,U as m,aa as _,J as P,F,ag as L,a6 as M,ab as S}from"./vendor/json-editor-vue-m9gzt21j.js";const T={"text-xl":""},D={flex:"~ gap2 wrap"},j=I({__name:"LaunchPage",props:{name:{},icon:{},title:{},description:{},actions:{}},emits:["action"],setup(l,{emit:p}){const d=l,u=p;function f(e,s){$("launch-page",{pageName:d.name}),e.handle?.(),u("action",s)}return(e,s)=>{const g=C,k=v,b=V,h=B,N=y,x=w;return t(),a(x,null,{default:o(()=>[c(N,{flex:"~ col gap2",mxa:"","min-w-100":"","items-center":"",p6:""},{default:o(()=>[c(g,{mb2:"","text-5xl":"",icon:e.icon||e.icon,title:e.title},null,8,["icon","title"]),i("h1",T,_(e.title),1),e.description?(t(),a(k,{key:0,mb2:"","mt--1":"","text-center":"","text-base":"",op50:"",markdown:e.description},null,8,["markdown"])):m("",!0),i("div",D,[(t(!0),P(F,null,L(e.actions,(n,r)=>(t(),a(h,M({key:r,n:"solid primary",disabled:n.pending,to:n.src,target:n.src?"_blank":void 0},{ref_for:!0},n.attrs,{onClick:E=>f(n,r)}),{default:o(()=>[n.pending?(t(),a(b,{key:0,icon:"carbon-circle-dash","animate-spin":""})):m("",!0),S(" "+_(n.label),1)]),_:2},1040,["disabled","to","target","onClick"]))),128))])]),_:1})]),_:1})}}});export{j as _};
