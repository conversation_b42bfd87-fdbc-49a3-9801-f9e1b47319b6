import{F as r}from"./czf9xkmw.js";import{u as m,m as _}from"./vue-devtools-d89ywfb1.js";import{p as c,J as p,a3 as e,V as o,u as n,a4 as i,ab as u}from"./vendor/json-editor-vue-m9gzt21j.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";import"./vue-virtual-scroller.esm-k5helfir.js";const l={class:"h-full w-full"},h=c({__name:"pinia",setup(f){const{connected:a}=m();return(d,t)=>{const s=r;return e(),p("div",l,[n(a)?(e(),o(n(_),{key:0})):(e(),o(s,{key:1},{default:i(()=>t[0]||(t[0]=[u(" Connecting.... ")])),_:1,__:[0]}))])}}});export{h as default};
