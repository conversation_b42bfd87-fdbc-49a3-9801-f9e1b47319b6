import{p as m,J as l,a3 as t,F as c,ag as v,Z as r,u as o,S as d,ac as f,aa as _,aw as g,j as y}from"./vendor/json-editor-vue-m9gzt21j.js";import{G as V}from"./czf9xkmw.js";const h={class:"n-select-tabs flex flex-inline flex-wrap items-center border n-border-base rounded n-bg-base"},x=["disabled","title"],B=["disabled","value","title"],D=m({__name:"NSelectTabs",props:{modelValue:{default:void 0},disabled:{type:Boolean,default:!1},options:{}},setup(i,{emit:p}){const a=V(i,"modelValue",p,{passive:!0});return(s,n)=>(t(),l("fieldset",h,[(t(!0),l(c,null,v(s.options,(e,b)=>(t(),l("label",{key:e.label,disabled:s.disabled,class:r(["relative n-border-base px-0.5em py-0.1em hover:n-bg-active",[b?"border-l n-border-base ml--1px":"",e.value===o(a)?"n-bg-active":""]]),title:e.label},[d("div",{class:r([e.value===o(a)?"":"op35"])},_(e.label),3),f(d("input",{"onUpdate:modelValue":n[0]||(n[0]=u=>y(a)?a.value=u:null),type:"radio",disabled:s.disabled,value:e.value,title:e.label,class:"absolute inset-0 op-0.1"},null,8,B),[[g,o(a)]])],10,x))),128))]))}});export{D as _};
