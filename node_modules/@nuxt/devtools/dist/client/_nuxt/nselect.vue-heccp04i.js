import{G as p,I as m}from"./czf9xkmw.js";import{p as f,J as l,a3 as s,Z as n,W as t,ac as b,V as h,U as d,aC as v,u as V,S as y,j as k,aa as B}from"./vendor/json-editor-vue-m9gzt21j.js";const S=["disabled"],g={key:0,value:"",disabled:"",hidden:""},$=f({__name:"NSelect",props:{modelValue:{default:void 0},placeholder:{default:""},icon:{default:""},disabled:{type:Boolean,default:!1}},setup(i,{emit:r}){const o=p(i,"modelValue",r,{passive:!0});return(e,a)=>{const c=m;return s(),l("div",{class:n(["n-select flex flex items-center border rounded n-bg-base px-2 py-1 focus-within:border-context focus-within:n-focus-base",e.disabled?"border-gray:10":"n-border-base"])},[t(e.$slots,"icon",{},()=>[e.icon?(s(),h(c,{key:0,icon:e.icon,class:"mr-0.4em text-1.1em op50"},null,8,["icon"])):d("",!0)]),b(y("select",{"onUpdate:modelValue":a[0]||(a[0]=u=>k(o)?o.value=u:null),disabled:e.disabled,class:n(["w-full flex-auto n-bg-base !outline-none",e.disabled?"appearance-none":""])},[e.placeholder?(s(),l("option",g,B(e.placeholder),1)):d("",!0),t(e.$slots,"default")],10,S),[[v,V(o)]])],2)}}});export{$ as _};
