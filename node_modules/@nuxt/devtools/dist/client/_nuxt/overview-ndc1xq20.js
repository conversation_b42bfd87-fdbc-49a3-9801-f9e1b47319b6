import{f as M,A as z,V as E,ab as O,ac as P,a6 as F,e as $,h as q,ad as g,ae as _,af as G,q as H,ag as K,_ as U,i as J,j as W,E as Q}from"./czf9xkmw.js";import{u as X,_ as Y}from"./state-modules-i9ej6ssc.js";import{u as Z}from"./state-components-amzv37n9.js";import{p as h,q as A,V as v,a3 as s,a4 as n,J as p,u as o,S as e,a5 as i,aa as a,U as u,F as f,ab as l}from"./vendor/json-editor-vue-m9gzt21j.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";import"./nbadge-bu0b8pjx.js";import"./ncode-block.vue-ctkw4rc7.js";import"./client-oeqdl4pb.js";import"./ncheckbox.vue-nc9ppn5r.js";const c="2.6.2",tt={key:0},et={key:1,flex:"~ col gap2",ma:"","h-full":"","max-w-300":"","w-full":"",p5:"",px5:"","md:px20":""},ot={flex:"~ col","mt-5":"","items-center":"","md:mt-20":""},nt={op40:""},st={flex:"~ gap2 wrap"},lt={key:5,"pointer-events-none":"","min-w-40":"","theme-card-lime":"",p4:"",flex:"~ auto gap-6"},it={grid:"~ cols-[auto_auto] gap-x-5 items-center"},ut={"text-right":""},rt={"text-right":""},at={"text-right":""},dt={"text-right":""},pt={flex:"~ col gap2"},mt={flex:"~ gap-6 wrap","mt-5":"","items-center":"","justify-center":""},xt={flex:"col gap-2",mxa:"",hidden:"","w-100":"","text-sm":"",op50:"","md:flex":""},vt={flex:"~ gap-1","items-center":""},ft={flex:"~ gap-1","items-center":""},Vt=h({__name:"overview",setup(gt){const k=M(),m=z(),D=Z(),b=E(),V=X(),j=O(),y=P(),B=F();function I(){W.value=!0,B.push("/")}const w=A(()=>k.value?.nuxt.vueApp.version),r=A(()=>k.value?.metrics.loading());function L(){Q()}return(S,t)=>{const R=q,x=$,C=Y,N=H,d=U,T=J;return s(),v(T,{"h-screen":"","w-full":"",flex:""},{default:n(()=>[o(m)?(s(),p("div",et,[t[40]||(t[40]=e("div",{"flex-auto":""},null,-1)),e("div",ot,[i(x,{flex:"~","mt--10":"","items-center":"","justify-center":"",to:"https://devtools.nuxt.com/",target:"_blank"},{default:n(()=>[i(R,{"h-10":""})]),_:1}),e("button",{mb6:"",mt3:"","text-center":"","text-sm":"",flex:"~ gap-1 wrap",onClick:I},[t[0]||(t[0]=e("span",{op40:""}," Nuxt DevTools ",-1)),e("code",nt,"v"+a(o(c)),1),i(C,{"package-name":"@nuxt/devtools",options:{dev:!0},"show-version":!1})])]),e("div",st,[i(x,{to:"https://nuxt.com",target:"_blank","theme-card-green":"",p4:"",flex:"~ col auto"},{default:n(()=>[t[1]||(t[1]=e("div",{"logos-nuxt-icon":"","text-3xl":""},null,-1)),i(C,{"package-name":"nuxt",options:{dev:!0}})]),_:1,__:[1]}),w.value?(s(),v(x,{key:0,to:"https://vuejs.org",target:"_blank","theme-card-green":"",p4:"",flex:"~ col auto"},{default:n(()=>[t[2]||(t[2]=e("div",{"logos-vue":"","text-3xl":""},null,-1)),e("code",null,"v"+a(w.value),1)]),_:1,__:[2]})):u("",!0),o(m)?(s(),p(f,{key:1},[o(m)&&o(m).pages&&o(k)?(s(),v(x,{key:0,"min-w-40":"","theme-card-lime":"",p4:"",flex:"~ col auto",to:"/modules/pages"},{default:n(()=>[t[3]||(t[3]=e("div",{"carbon-tree-view-alt":"","text-3xl":""},null,-1)),e("div",null,a(o(g)(o(j).length,"page")),1)]),_:1,__:[3]})):u("",!0),o(m)?(s(),v(x,{key:1,"min-w-40":"","theme-card-lime":"",p4:"",flex:"~ col auto",to:"/modules/components"},{default:n(()=>[t[4]||(t[4]=e("div",{"i-carbon-assembly-cluster":"","text-3xl":""},null,-1)),e("div",null,a(o(g)(o(D).length,"component")),1)]),_:1,__:[4]})):u("",!0),o(m)&&o(b)?(s(),v(x,{key:2,"min-w-40":"","theme-card-yellow":"",p4:"",flex:"~ col auto",to:"/modules/imports"},{default:n(()=>[t[5]||(t[5]=e("div",{"carbon-function":"","text-3xl":""},null,-1)),e("div",null,a(o(g)(o(b).imports.length,"import")),1)]),_:1,__:[5]})):u("",!0),o(m)?(s(),v(x,{key:3,"min-w-40":"","theme-card-purple":"",p4:"",flex:"~ col auto",to:"/modules/modules"},{default:n(()=>[t[6]||(t[6]=e("div",{"carbon-3d-mpr-toggle":"","text-3xl":""},null,-1)),e("div",null,a(o(g)(o(V).length,"module")),1)]),_:1,__:[6]})):u("",!0),o(m)?(s(),v(x,{key:4,"min-w-40":"","theme-card-teal":"",p4:"",flex:"~ col auto",to:"/modules/plugins"},{default:n(()=>[t[7]||(t[7]=e("div",{"carbon-plug":"","text-3xl":""},null,-1)),e("div",null,a(o(g)(o(m).plugins.length,"plugin")),1)]),_:1,__:[7]})):u("",!0),r.value?(s(),p("div",lt,[t[12]||(t[12]=e("div",{"i-carbon-time-plot":"","flex-none":"","text-3xl":""},null,-1)),e("div",it,[r.value.ssrStart?(s(),p(f,{key:0},[t[8]||(t[8]=e("div",{"text-sm":""}," SSR to full load ",-1)),e("div",ut,a(o(_)(r.value.appLoad-r.value.ssrStart)),1)],64)):u("",!0),t[10]||(t[10]=e("div",{"text-sm":""}," Page load ",-1)),e("div",rt,a(o(_)(r.value.appLoad-r.value.appInit)),1),t[11]||(t[11]=e("div",{"text-sm":""}," Navigation ",-1)),e("div",at,a(o(_)(r.value.pageEnd-r.value.pageStart)),1),r.value.hmrStart?(s(),p(f,{key:1},[t[9]||(t[9]=e("div",{"text-sm":""}," HMR ",-1)),e("div",dt,a(o(_)(r.value.hmrEnd-r.value.hmrStart)),1)],64)):u("",!0)])])):u("",!0)],64)):u("",!0)]),e("div",pt,[o(G)?(s(),v(N,{key:0,n:"yellow5",icon:"carbon-unlink","justify-center":""},{default:n(()=>t[13]||(t[13]=[l(" Not connected to the client app, showing server-side data only. Use the embedded mode for full features. ")])),_:1,__:[13]})):u("",!0),e("button",{title:"Authorize",onClick:L},[("isDevAuthed"in S?S.isDevAuthed:o(K))?u("",!0):(s(),v(N,{key:0,n:"orange5",icon:"i-carbon-locked","justify-center":""},{default:n(()=>t[14]||(t[14]=[l(" Access from an untrusted browser, some features are limited. Click to authorize now. ")])),_:1,__:[14]}))])]),e("div",mt,[t[16]||(t[16]=e("a",{href:"https://github.com/nuxt/devtools",target:"_blank",flex:"~ gap1","items-center":"",op50:"",hover:"op100 text-blue",transition:""},[e("div",{"i-carbon-star":""}),l(" Star on GitHub ")],-1)),t[17]||(t[17]=e("a",{href:"https://github.com/nuxt/devtools/discussions/29",target:"_blank",flex:"~ gap1","items-center":"",op50:"",hover:"op100 text-yellow",transition:""},[e("div",{"i-carbon-data-enrichment":""}),l(" Ideas & Suggestions ")],-1)),t[18]||(t[18]=e("a",{href:"https://github.com/nuxt/devtools/discussions/31",target:"_blank",flex:"~ gap1","items-center":"",op50:"",hover:"op100 text-lime",transition:""},[e("div",{"i-carbon-plan":""}),l(" Project Roadmap ")],-1)),t[19]||(t[19]=e("a",{href:"https://github.com/nuxt/devtools/issues",target:"_blank",flex:"~ gap1","items-center":"",op50:"",hover:"op100 text-rose",transition:""},[e("div",{"i-carbon-debug":""}),l(" Bug Reports ")],-1)),i(x,{to:"/settings",flex:"~ gap1","inline-block":"","items-center":"",op50:"","hover:op80":""},{default:n(()=>t[15]||(t[15]=[e("div",{"i-carbon-settings-adjust":""},null,-1),l(" Settings ")])),_:1,__:[15]})]),t[41]||(t[41]=e("div",{"flex-auto":""},null,-1)),e("div",xt,[e("div",vt,[o(y)?(s(),p(f,{key:0},[i(d,{n:"xs",class:"px2"},{default:n(()=>t[20]||(t[20]=[l(" ⌘ Cmd ")])),_:1,__:[20]}),t[22]||(t[22]=e("span",null,"+",-1)),i(d,{n:"xs",class:"px2"},{default:n(()=>t[21]||(t[21]=[l(" K ")])),_:1,__:[21]})],64)):(s(),p(f,{key:1},[i(d,{n:"xs",class:"px2"},{default:n(()=>t[23]||(t[23]=[l(" Ctrl ")])),_:1,__:[23]}),t[25]||(t[25]=e("span",null,"+",-1)),i(d,{n:"xs",class:"px2"},{default:n(()=>t[24]||(t[24]=[l(" K ")])),_:1,__:[24]})],64)),t[26]||(t[26]=e("div",{"flex-auto":""},null,-1)),t[27]||(t[27]=l(" Open Command Palette "))]),e("div",ft,[o(y)?(s(),p(f,{key:0},[i(d,{n:"xs",class:"px2"},{default:n(()=>t[28]||(t[28]=[l(" ⇧ Shift ")])),_:1,__:[28]}),t[31]||(t[31]=e("span",null,"+",-1)),i(d,{n:"xs",class:"px2"},{default:n(()=>t[29]||(t[29]=[l(" ⌥ Option ")])),_:1,__:[29]}),t[32]||(t[32]=e("span",null,"+",-1)),i(d,{n:"xs",class:"px2"},{default:n(()=>t[30]||(t[30]=[l(" D ")])),_:1,__:[30]})],64)):(s(),p(f,{key:1},[i(d,{n:"xs",class:"px2"},{default:n(()=>t[33]||(t[33]=[l(" Shift ")])),_:1,__:[33]}),t[36]||(t[36]=e("span",null,"+",-1)),i(d,{n:"xs",class:"px2"},{default:n(()=>t[34]||(t[34]=[l(" Alt ")])),_:1,__:[34]}),t[37]||(t[37]=e("span",null,"+",-1)),i(d,{n:"xs",class:"px2"},{default:n(()=>t[35]||(t[35]=[l(" D ")])),_:1,__:[35]})],64)),t[38]||(t[38]=e("div",{"flex-auto":""},null,-1)),t[39]||(t[39]=e("div",null,"Toggle DevTools",-1))])])])):(s(),p("div",tt," Loading... "))]),_:1})}}});export{Vt as default};
