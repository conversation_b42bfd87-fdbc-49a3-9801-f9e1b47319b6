import{_ as oe}from"./filepath-item.vue-dx8apiq4.js";import{w as Y,n as Z,e as L,I as G,Z as J,_ as F,r as q,E,M as ne,$ as Q,a0 as W,S as se,A as le,Q as ae,q as ie,a1 as re}from"./czf9xkmw.js";import{_ as ue,u as O,a as de}from"./state-modules-i9ej6ssc.js";import{_ as me}from"./duration-display.vue-mr4ha0rw.js";import{p as D,q as I,V as C,a3 as t,a4 as u,S as e,W as T,J as m,U as g,ab as k,aa as b,u as j,Z as S,a5 as s,F as B,ag as A,an as pe,k as P,H}from"./vendor/json-editor-vue-m9gzt21j.js";import{_ as ce}from"./nsection-block-m4vpsvnn.js";import{_ as _e}from"./nicon-title.vue-ejocqf9t.js";import{_ as K}from"./ndropdown.vue-0o486e0j.js";import{_ as fe}from"./ncheckbox.vue-nc9ppn5r.js";import{_ as ve}from"./nnavbar.vue-ma0lzxrm.js";import{_ as ge}from"./nbadge-bu0b8pjx.js";import{s as xe}from"./vue-virtual-scroller.esm-k5helfir.js";import{_ as be}from"./ncode-block.vue-ctkw4rc7.js";import{_ as ke}from"./nlink.vue-d4id0o0s.js";import{_ as he}from"./code-diff.vue-m3dd16hs.js";import{_ as ye}from"./help-fab.vue-b6h1gmzk.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";import"./client-oeqdl4pb.js";const $e={flex:"~ col gap2","flex-auto":"","of-hidden":"",px1:""},we={"gap-1t":"",flex:"","items-center":"","text-ellipsis":"","ws-nowrap":"","text-lg":""},Ce={key:2},Ie={key:1,flex:"~ gap-2",title:"Documentation"},Me={key:2,flex:"~ gap-2"},Ne={key:0,flex:"~ gap-4 items-center"},Be={key:0,flex:"~ gap-2 items-center",op50:""},je={key:1,flex:"~ gap-2 items-center",op50:""},De={key:1},Pe={flex:"~ col","items-end":""},Ae={key:0,"h-20":"","w-20":"",flex:"","flex-none":"",rounded:"","bg-gray:3":"",p4:""},Te=["src","alt"],Se={key:1,"i-carbon-cube":"",ma:"","flex-none":"","text-4xl":"",op30:""},Fe={key:1,flex:"~",mt2:"","flex-auto":"","items-end":"","justify-end":""},Ue=["src"],ze={flex:"","justify-end":""},Le="https://api.nuxtjs.org/api/ipx/s_80,f_webp/gh/nuxt/modules/main/icons/",Oe="https://api.nuxtjs.org/api/ipx/s_44,f_webp/gh_avatar/",Ve="https://github.com/",qe="https://www.npmjs.com/package/",V=D({__name:"ModuleItemBase",props:{mod:{},info:{},compact:{type:Boolean},maintainers:{type:Boolean,default:!0}},setup(N){const v=N,a=I(()=>({...v.mod?.meta,...v.mod,...v.info})),{format:x}=Intl.NumberFormat(navigator.language||"en",{notation:"compact",maximumFractionDigits:1}),y=I(()=>x(a.value.stats?.stars||0)),l=I(()=>x(a.value.stats?.downloads||0)),r=Y();return(o,c)=>{const h=L,_=G,d=me,$=Z;return t(),C($,{p4:"",flex:"~ gap2"},{default:u(()=>[e("div",$e,[T(o.$slots,"main",{},()=>[e("div",we,[o.mod.isPackageModule?(t(),C(h,{key:0,to:qe+(a.value.npm||a.value.name),target:"_blank",hover:"underline text-primary"},{default:u(()=>[k(b(a.value.name),1)]),_:1},8,["to"])):o.mod.entryPath?(t(),m("button",{key:1,role:"button",hover:"underline text-primary",onClick:c[0]||(c[0]=f=>j(r)(o.mod.entryPath))},b(a.value.name),1)):(t(),m("span",Ce,b(a.value.name),1)),T(o.$slots,"badge")]),a.value.description?(t(),m("div",{key:0,class:S(o.compact?"ws-nowrap of-hidden truncate":"line-clamp-2"),"mt--1":"","text-sm":"",op50:""},b(a.value.description),3)):g("",!0),c[3]||(c[3]=e("div",{"flex-auto":""},null,-1)),a.value.website?(t(),m("div",Ie,[c[1]||(c[1]=e("span",{"i-carbon-link":"","flex-none":"","text-lg":"",op50:""},null,-1)),s(h,{to:a.value.website,target:"_blank","of-hidden":"",truncate:"","ws-nowrap":"","text-sm":"",op50:"",hover:"op100 underline text-primary"},{default:u(()=>[k(b(a.value.website.replace(/^https?:\/\//,"")),1)]),_:1},8,["to"])])):g("",!0),a.value.github?(t(),m("div",Me,[c[2]||(c[2]=e("span",{"i-carbon-logo-github":"","flex-none":"","text-lg":"",op50:""},null,-1)),s(h,{to:a.value.github,target:"_blank","of-hidden":"",truncate:"","ws-nowrap":"","text-sm":"",op50:"",hover:"op100 underline text-primary"},{default:u(()=>[k(b(a.value.github.replace(/^https?:\/\/github.com\//,"")),1)]),_:1},8,["to"])])):g("",!0)]),T(o.$slots,"items"),a.value.stats?(t(),m("div",Ne,[a.value.stats.stars?(t(),m("div",Be,[s(_,{icon:"carbon-star","flex-none":"","text-lg":""}),e("span",null,b(y.value),1)])):g("",!0),a.value.stats.downloads?(t(),m("div",je,[s(_,{icon:"carbon-download","flex-none":"","text-lg":""}),e("span",null,b(l.value),1)])):g("",!0)])):g("",!0),o.mod.timings?.setup?(t(),m("div",De,[s(d,{title:"Module Setup Time",flex:"~ items-center",duration:o.mod.timings.setup,factor:.5},{before:u(()=>[s(_,{icon:"carbon-time",mr2:"","flex-none":"","text-lg":"",op50:""})]),_:1},8,["duration"])])):g("",!0)]),e("div",Pe,[a.value.icon||o.mod.isPackageModule?(t(),m("div",Ae,[a.value.icon?(t(),m("img",{key:0,src:Le+a.value.icon,alt:o.mod.name,ma:""},null,8,Te)):(t(),m("div",Se))])):g("",!0),a.value.maintainers?.length&&o.maintainers?(t(),m("div",Fe,[(t(!0),m(B,null,A(a.value.maintainers,f=>(t(),C(h,{key:f.name,target:"_blank",to:Ve+f.github,title:f.name},{default:u(()=>[e("img",{src:Oe+f.github,"h-6":"","w-6":"","rounded-full":""},null,8,Ue)]),_:2},1032,["to","title"]))),128))])):g("",!0),o.$slots.actions?(t(),m(B,{key:2},[c[4]||(c[4]=e("div",{"flex-auto":""},null,-1)),e("div",ze,[T(o.$slots,"actions")])],64)):g("",!0)])]),_:3})}}}),Ee={key:0,flex:"~ gap-2",title:"Open on filesystem"},He={key:1,"mx--2":""},Ye=["onClick"],Ze={key:2,"mx--2":""},Ge=["onClick"],Je={op50:""},Qe={"text-green":""},We={key:3,flex:"~ gap-2","items-center":"",title:"NPM"},Ke={"text-sm":"",op50:""},Re=D({__name:"ModuleItem",props:{mod:{}},setup(N){const v=N,a=I(()=>v.mod.info),x=I(()=>({...v.mod?.meta,...v.mod,...a.value})),y=J();return(l,r)=>{const o=oe,c=L,h=ue,_=V;return t(),C(_,{mod:l.mod,info:a.value},{items:u(()=>[l.mod.entryPath?(t(),m("div",Ee,[r[0]||(r[0]=e("span",{"i-carbon-folder-move-to":"","flex-none":"","text-lg":"",op50:""},null,-1)),s(o,{filepath:l.mod.entryPath,"text-sm":"",op50:"",hover:"text-primary op100"},null,8,["filepath"])])):g("",!0),x.value.npm?(t(),C(h,{key:x.value.npm,"package-name":x.value.npm,options:{dev:!0}},{default:u(({info:d,update:$,state:f,id:i,restart:n})=>[f==="running"?(t(),C(c,{key:0,flex:"~ gap-2","animate-pulse":"","items-center":"",to:i?"/modules/terminals":void 0,onClick:w=>i?y.value=i:void 0},{default:u(()=>r[1]||(r[1]=[e("span",{"i-carbon-circle-dash":"","flex-none":"","animate-spin":"","text-lg":"",op50:""},null,-1),e("code",{"text-sm":"",op50:""},"Upgrading...",-1)])),_:2,__:[1]},1032,["to","onClick"])):f==="updated"?(t(),m("div",He,[e("button",{flex:"~ gap-2",hover:"bg-primary/20","items-center":"",rounded:"","bg-primary:10":"",px2:"","text-sm":"","text-primary":"",onClick:n},r[2]||(r[2]=[e("span",{"i-carbon-intent-request-active":"","flex-none":"","text-lg":"","text-primary":""},null,-1),e("code",{"text-xs":""},"Update installed, click to restart",-1)]),8,Ye)])):d?.needsUpdate?(t(),m("div",Ze,[e("button",{flex:"~ gap-2",title:"Click to upgrade","items-center":"",rounded:"",px2:"","text-sm":"",hover:"bg-active",onClick:w=>$()},[r[3]||(r[3]=e("span",{"i-carbon-intent-request-upgrade":"","flex-none":"","text-lg":"",op50:""},null,-1)),e("code",Je,"v"+b(d.current),1),r[4]||(r[4]=e("div",{"i-carbon-arrow-right":"",op50:""},null,-1)),e("code",Qe,"v"+b(d.latest),1)],8,Ge)])):d?.latest?(t(),m("div",We,[r[5]||(r[5]=e("span",{"i-carbon-cube":"","flex-none":"","text-lg":"",op50:""},null,-1)),e("code",Ke,"v"+b(d.current),1)])):g("",!0)]),_:1},8,["package-name"])):g("",!0)]),_:1},8,["mod","info"])}}}),Xe=D({__name:"ModuleItemInstall",props:{item:{}},emits:["start"],setup(N,{emit:v}){const a=N,x=v,y=O(),l=I(()=>y.value.find(_=>_.name===a.item.npm)),r=I(()=>l.value&&l.value.isPackageModule),o=I(()=>l.value&&l.value.isPackageModule&&l.value.isUninstallable);async function c(_,d){const $=d==="install"?q.installNuxtModule:q.uninstallNuxtModule,f=await $(await E(),_.npm,!0);ne(`modules:${d}`,{moduleName:_.npm}),f.commands&&await Q.start(_,f,d)&&(W.value.push({name:_.npm,info:_,processId:f.processId}),x("start"),await $(await E(),_.npm,!1))}const h={};return(_,d)=>{const $=ge,f=F,i=K,n=V;return t(),C(n,{mod:h,role:r.value?"":"button",info:_.item,mb2:"","h-full":"",class:S(r.value?"border-dashed op75":"hover:bg-active!"),compact:!0,onClick:d[1]||(d[1]=w=>r.value?null:c(_.item,"install"))},pe({_:2},[r.value?{name:"badge",fn:u(()=>[s($,{n:"green",textContent:"Installed"}),o.value?(t(),C(i,{key:0,n:"sm green"},{trigger:u(({click:w})=>[s(f,{icon:"carbon-overflow-menu-vertical",border:!1,onClick:p=>w()},null,8,["onClick"])]),default:u(()=>[s(f,{icon:"carbon-trash-can",n:"red",onClick:d[0]||(d[0]=w=>c(_.item,"uninstall"))},{default:u(()=>d[2]||(d[2]=[k(" Uninstall ")])),_:1,__:[2]})]),_:1})):g("",!0)]),key:"0"}:void 0]),1032,["role","info","class"])}}}),et={"h-full":"",flex:"~ col gap-4"},tt={flex:"~  items-center"},ot={flex:"~ col","w-30":"","of-auto":""},nt={flex:"~ justify-between","w-full":"","text-xs":"",capitalize:"",op75:""},st={flex:"~ items-center gap-2"},lt={flex:"~ gap1","text-sm":"",op50:""},at={key:0},it={"flex-auto":"","of-auto":"",flex:"~ col gap-2",pl6:"",pr4:""},rt=D({__name:"ModuleInstallList",emits:["close"],setup(N,{emit:v}){const a=v,x=de(),y=O(),l=["downloads","stars","updated","created"],r=P(!1),o=P(l[0]),c=P(!0),h={downloads:(i,n)=>i.stats.downloads-n.stats.downloads,stars:(i,n)=>i.stats.stars-n.stats.stars,created:(i,n)=>i.stats.createdAt-n.stats.createdAt,updated:(i,n)=>i.stats.publishedAt-n.stats.publishedAt},_=I(()=>x.value?.toSorted((i,n)=>h[o.value](i,n)*(r.value?1:-1))),d=P(""),$=I(()=>new se(x.value||[],{keys:["name","description","npm","category"],sortFn:(i,n)=>{const w=x.value?.[i.idx],p=x.value?.[n.idx];return w&&p?h[o.value](w,p)*(r.value?1:-1):i.score-n.score},threshold:.2})),f=I(()=>{let i=_.value;return c.value&&(i=(i||[]).filter(n=>!y.value.some(w=>w.name===n.name))),d.value?$.value.search(d.value).map(n=>n.item).filter(n=>i?.includes(n)):i});return(i,n)=>{const w=_e,p=F,U=G,R=K,X=fe,ee=ve,te=Xe;return t(),m("div",et,[s(w,{mx6:"",mt6:"","text-xl":"",op75:"",icon:"i-carbon-intent-request-create",text:"Install Module"}),s(ee,{search:d.value,"onUpdate:search":n[2]||(n[2]=M=>d.value=M),"no-padding":"","px-6":"","pb-3":"","pt-2":""},{actions:u(()=>[s(R,{direction:"end",n:"sm primary"},{trigger:u(({click:M})=>[e("div",tt,[s(p,{icon:r.value?"tabler:sort-ascending":"tabler:sort-descending","h-full":"","rounded-r-none":"",onClick:n[0]||(n[0]=z=>r.value=!r.value)},null,8,["icon"]),s(p,{flex:"~ justify-between","min-w-30":"","border-l-0":"","rounded-l-none":"","px-2":"",capitalize:"",hover:"border-l-1",onClick:z=>M()},{default:u(()=>[k(b(o.value)+" ",1),s(U,{icon:"carbon:chevron-down"})]),_:2},1032,["onClick"])])]),default:u(()=>[e("div",ot,[(t(),m(B,null,A(l,M=>s(p,{key:M,border:!1,p2:"",hover:"n-checkbox-hover text-green",onClick:z=>o.value=M},{default:u(()=>[e("span",nt,[k(b(M)+" ",1),o.value===M?(t(),C(U,{key:0,icon:"carbon:checkmark"})):g("",!0)])]),_:2},1032,["onClick"])),64))])]),_:1})]),default:u(()=>[e("div",st,[s(X,{modelValue:c.value,"onUpdate:modelValue":n[1]||(n[1]=M=>c.value=M),n:"primary md"},{default:u(()=>n[4]||(n[4]=[e("span",{op75:""},"Exclude installed modules",-1)])),_:1,__:[4]},8,["modelValue"]),e("div",lt,[d.value||c.value?(t(),m("span",at,b(f.value?.length)+" matched · ",1)):g("",!0),e("span",null,b(j(x)?.length)+" modules in total",1)])])]),_:1},8,["search"]),e("div",it,[s(j(xe),{class:"scroller",items:f.value,"item-size":200,"key-field":"name"},{default:u(({item:M})=>[s(te,{item:M,onStart:n[3]||(n[3]=z=>a("close"))},null,8,["item"])]),_:1},8,["items"])])])}}}),ut={flex:"~ col gap-2","w-150":"",p4:"",border:"t base"},dt={capitalize:""},mt={op50:""},pt={flex:"~ gap-3",mt2:"","justify-end":""},ct=D({__name:"ModuleActionDialog",setup(N){const v=le(),a=Y(),x={};return(y,l)=>{const r=V,o=be,c=ke,h=he,_=ie,d=F,$=ae;return t(),C(j(Q),null,{default:u(({resolve:f,args:i})=>[s($,{"model-value":!0,onClose:n=>f(!1)},{default:u(()=>[s(r,{mod:x,info:i[0],border:"none","w-150":"","n-panel-grids":""},null,8,["info"]),e("div",ut,[e("h2",{"text-xl":"",class:S(i[2]==="install"?"text-primary":"text-red")},[e("span",dt,b(i[2]),1),l[1]||(l[1]=k()),e("code",null,b(i[0].name),1),l[2]||(l[2]=k("? "))],2),l[7]||(l[7]=e("p",{op50:""}," The following command will be executed in your terminal: ",-1)),s(o,{code:i[1].commands.join(" "),lang:"bash",px4:"",py2:"",border:"~ base rounded",lines:!1},null,8,["code"]),e("p",mt,[l[3]||(l[3]=k(" Then your ")),s(c,{role:"button",n:"primary",underline:"",onClick:l[0]||(l[0]=n=>j(a)(j(v)?._nuxtConfigFile)),textContent:"Nuxt config"}),l[4]||(l[4]=k(" will be updated as: "))]),s(h,{from:i[1].configOriginal,to:i[1].configGenerated,"max-h-80":"","of-auto":"",py2:"",border:"~ base rounded",lang:"ts"},null,8,["from","to"]),l[8]||(l[8]=e("p",null,[e("span",{op50:""},"After that, Nuxt will "),e("span",{"text-orange":""},"restart automatically"),k(". ")],-1)),e("div",pt,[s(_,{n:"sm amber","flex-auto":"",icon:"i-carbon-data-backup"},{default:u(()=>l[5]||(l[5]=[k(" Please make sure to backup your project first. ")])),_:1,__:[5]}),s(d,{onClick:n=>f(!1)},{default:u(()=>l[6]||(l[6]=[k(" Cancel ")])),_:2,__:[6]},1032,["onClick"]),s(d,{n:"solid",capitalize:"",class:S(i[2]==="install"?"n-primary":"n-red"),onClick:n=>f(!0)},{default:u(()=>[k(b(i[2]),1)]),_:2},1032,["class","onClick"])])])]),_:2},1032,["onClose"])]),_:1})}}}),_t={class:"markdown-body"},ft={__name:"modules",setup(N,{expose:v}){return v({frontmatter:{}}),(x,y)=>(t(),m("div",_t,y[0]||(y[0]=[e("h1",null,"Modules",-1),e("p",null,[k("Nuxt provides a module system to extend the framework core and simplify integrations. You don’t need to develop everything from scratch or maintain boilerplate if there is already a Nuxt module for it. Adding Nuxt modules is possible using "),e("code",null,"nuxt.config"),k(".")],-1),e("p",null,[k("You can explore the list of modules on "),e("a",{href:"https://nuxt.com/modules",target:"_blank",rel:"noopener"},"nuxt.com/modules"),k(" or install them directly inside the DevTools.")],-1),e("p",null,[e("a",{href:"https://nuxt.com/docs/guide/concepts/modules",target:"_blank",rel:"noopener"},"Learn more in the documentation")],-1)])))}},vt={"h-full":"","w-full":"","of-auto":""},gt={relative:"","h-20":"","w-20":"",flex:"","flex-none":"",rounded:"","bg-gray:3":"",p3:""},xt=["src","alt"],bt={"text-lg":"","group-hover":"text-primary",transition:"",flex:"~ gap-2 items-center"},kt={op75:""},ht={key:0,border:"l base",pos:"fixed bottom-0 right-0 top-0","z-200":"","w-150":"","bg-base":""},yt="https://api.nuxtjs.org/api/ipx/s_80,f_webp/gh/nuxt/modules/main/icons/",qt=D({__name:"modules",setup(N){const v=P(!1),a=O(),x=J(),y=I(()=>a.value.filter(r=>r.isPackageModule)),l=I(()=>a.value.filter(r=>!r.isPackageModule));return re(()=>[{id:"action:modules:install",title:"Install a new module",icon:"i-carbon-intent-request-create ",action:()=>{v.value=!0}}]),(r,o)=>{const c=Re,h=Z,_=L,d=ce,$=F,f=rt,i=ct,n=ft,w=ye;return t(),m(B,null,[e("div",vt,[s(d,{icon:"carbon-3d-mpr-toggle",text:"Installed Modules","container-class":"grid grid-cols-minmax-400px gap3 px4",padding:!1,description:`Total modules: ${y.value.length}`},{default:u(()=>[(t(!0),m(B,null,A(y.value,p=>(t(),C(c,{key:p.name,mod:p},null,8,["mod"]))),128)),(t(!0),m(B,null,A("processInstallingModules"in r?r.processInstallingModules:j(W),p=>(t(),C(_,{key:p.processId,block:"","min-h-30":"",to:"/modules/terminals",onClick:U=>x.value=p.processId},{default:u(()=>[s(h,{border:"1.5 dashed","h-full":"","animate-pulse":"",p4:"",transition:"",hover:"border-primary",flex:"~ col gap-1 items-center justify-center",role:"button",class:"group"},{default:u(()=>[e("div",gt,[e("img",{src:yt+p.info.icon,alt:p.info.name,ma:""},null,8,xt),o[4]||(o[4]=e("div",{"i-carbon-cube":"",ma:"","text-4xl":"",op30:""},null,-1))]),e("div",bt,[o[5]||(o[5]=e("div",{"i-carbon-circle-dash":"","animate-spin":"","text-xl":"",op75:""},null,-1)),e("span",kt,"Installing "+b(p.name)+"...",1)])]),_:2},1024)]),_:2},1032,["onClick"]))),128)),s(h,{border:"1.5 dashed","min-h-30":"",p4:"",transition:"",hover:"border-primary",flex:"~ col gap-2 items-center justify-center",role:"button",class:"group",onClick:o[0]||(o[0]=p=>v.value=!0)},{default:u(()=>o[6]||(o[6]=[e("div",{"i-carbon-intent-request-create":"","text-4xl":"",op40:"","group-hover":"op75 text-primary",transition:""},null,-1),e("div",{"text-lg":"",op40:"","group-hover":"op75 text-primary",transition:""}," Install New Module ",-1)])),_:1,__:[6]})]),_:1},8,["description"]),l.value.length?(t(),C(d,{key:0,icon:"carbon-3d-mpr-toggle",text:"User Modules","container-class":"grid grid-cols-minmax-400px gap3 px4",padding:!1,description:`Total modules: ${l.value.length}`},{default:u(()=>[(t(!0),m(B,null,A(l.value,p=>(t(),m(B,{key:p.meta?.name||p.entryPath},[p.meta?.name||p.entryPath?(t(),C(c,{key:0,mod:p},null,8,["mod"])):g("",!0)],64))),128))]),_:1},8,["description"])):g("",!0),s(H,{name:"fade-in"},{default:u(()=>[v.value?(t(),m("div",{key:0,class:"fixed bottom-0 left-0 right-0 top-0 z-100","bg-black:20":"","backdrop-blur-2":"",onClick:o[1]||(o[1]=p=>v.value=!1)})):g("",!0)]),_:1}),s(H,{name:"slide-in"},{default:u(()=>[v.value?(t(),m("div",ht,[s($,{icon:"carbon-close",pos:"absolute top-3 right-3 z-10","rounded-full":"","text-xl":"",border:!1,onClick:o[2]||(o[2]=p=>v.value=!1)}),s(f,{onClose:o[3]||(o[3]=p=>v.value=!1)})])):g("",!0)]),_:1}),s(i)]),s(w,null,{default:u(()=>[s(n)]),_:1})],64)}}});export{qt as default};
