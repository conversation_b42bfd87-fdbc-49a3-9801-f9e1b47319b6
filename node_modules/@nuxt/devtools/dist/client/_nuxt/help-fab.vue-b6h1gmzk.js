import{g as f,_ as m}from"./czf9xkmw.js";import{p as v,k as c,J as o,U as s,u as k,a3 as a,F as g,S as r,a5 as l,H as u,a4 as i,W as _}from"./vendor/json-editor-vue-m9gzt21j.js";const x={key:0,border:"l base",class:"prose",pos:"fixed bottom-0 right-0 top-0","z-200":"","h-full":"","w-150":"","overflow-auto":"","bg-base":"",px8:"",py4:""},y=v({__name:"HelpFab",setup(w){const t=c(!1),{showHelpButtons:p}=f("ui");return(d,e)=>{const b=m;return k(p)?(a(),o(g,{key:0},[r("button",{pos:"absolute bottom-5 right-5",border:"~ base rounded-full ",flex:"~ items-center justify-center","z-110":"","h-11":"","w-11":"","backdrop-blur-8":"",bg:"bg-base op50!","light:shadow":"",hover:"bg-active",title:"Help",onClick:e[0]||(e[0]=n=>t.value=!t.value)},e[3]||(e[3]=[r("div",{"i-ri:question-mark":""},null,-1)])),l(u,{name:"fade-in"},{default:i(()=>[t.value?(a(),o("div",{key:0,class:"fixed bottom-0 left-0 right-0 top-0 z-100","bg-black:20":"","backdrop-blur-2":"",onClick:e[1]||(e[1]=n=>t.value=!1)})):s("",!0)]),_:1}),l(u,{name:"slide-in"},{default:i(()=>[t.value?(a(),o("div",x,[_(d.$slots,"default"),l(b,{icon:"carbon-close",pos:"absolute top-3 right-3","rounded-full":"","text-xl":"",border:!1,onClick:e[2]||(e[2]=n=>t.value=!1)})])):s("",!0)]),_:3})],64)):s("",!0)}}});export{y as _};
