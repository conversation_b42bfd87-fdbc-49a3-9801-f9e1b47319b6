import{G as i,a2 as p,_ as u}from"./czf9xkmw.js";import{p as m,k as c,J as f,a3 as _,W as n,S as b,a5 as v,u as t,a4 as k,ab as V,Z as B}from"./vendor/json-editor-vue-m9gzt21j.js";const $=m({__name:"NDropdown",props:{modelValue:{type:Boolean},direction:{default:"start"}},setup(r,{emit:l}){const e=i(r,"modelValue",l,{passive:!0}),s=c();return p(s,()=>{e.value=!1}),(a,o)=>{const d=u;return _(),f("div",{ref_key:"el",ref:s,class:"relative"},[n(a.$slots,"trigger",{enabled:t(e),click:()=>e.value=!t(e)},()=>[v(d,{onClick:o[0]||(o[0]=g=>e.value=!t(e))},{default:k(()=>o[1]||(o[1]=[V(" Dropdown ")])),_:1,__:[1]})]),b("div",{class:B(["absolute z-10 border n-border-base rounded n-bg-base shadow n-transition",[t(e)?"op-100":"op0 pointer-events-none -translate-y-1",a.direction==="end"?"right-0":"left-0"]])},[n(a.$slots,"default")],2)],512)}}});export{$ as _};
