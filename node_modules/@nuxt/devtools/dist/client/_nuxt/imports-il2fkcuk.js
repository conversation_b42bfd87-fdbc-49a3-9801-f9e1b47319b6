import{V as A,W as S,X as T,A as j,S as P,Y as X,I as Y}from"./czf9xkmw.js";import{_ as q}from"./nselect-tabs.vue-r7y0s9z2.js";import{_ as J}from"./nnavbar.vue-ma0lzxrm.js";import{_ as U}from"./filepath-item.vue-dx8apiq4.js";import{_ as W}from"./composable-item.vue-olv2xw90.js";import{p as C,J as u,a3 as a,F as k,ag as M,a5 as c,S as i,V as w,U as x,u as f,q as _,aa as G,k as V,a4 as $}from"./vendor/json-editor-vue-m9gzt21j.js";import{_ as K}from"./nsection-block-m4vpsvnn.js";import{_ as O}from"./help-fab.vue-b6h1gmzk.js";import{D as B}from"./constants-b32h69zq.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";import"./nmarkdown.vue-mk2gi3ky.js";import"./client-oeqdl4pb.js";import"./index-jc4yj4to.js";import"./nicon-title.vue-ejocqf9t.js";const Q={flex:"~ wrap gap2",p2:"",pl4:""},R=C({__name:"ComposableTree",props:{map:{},root:{},metadata:{}},setup(D){return(r,v)=>{const o=U,e=W;return a(),u("div",null,[(a(!0),u(k,null,M(r.map.entries(),([l,m])=>(a(),u("div",{key:l},[c(o,{filepath:l,op50:"","hover:underline":""},null,8,["filepath"]),i("div",Q,[(a(!0),u(k,null,M(m,g=>(a(),w(e,{key:g.as,item:g,metadata:r.metadata,"is-directive":g.meta?.vueDirective===!0,filepath:l.match(/^[\w@]/)?void 0:l},null,8,["item","metadata","is-directive","filepath"]))),128))])]))),128))])}}}),Z={key:0,flex:"~ col gap-2 items-start"},ee=C({__name:"HelpImportsDirs",setup(D){const r=A();return(v,o)=>{const e=U;return f(r)?.dirs?(a(),u("div",Z,[(a(!0),u(k,null,M(f(r).dirs,l=>(a(),w(e,{key:l,filepath:l,"text-primary":""},null,8,["filepath"]))),128))])):x("",!0)}}}),te={flex:"~ gap-2 wrap",mb6:""},oe=C({__name:"HelpImportsModules",setup(D){const r=A(),v=_(()=>[...new Set(r.value?.imports.map(o=>S(o.from)).filter(o=>!!o&&!T(o)))]);return(o,e)=>(a(),u("div",te,[(a(!0),u(k,null,M(v.value,l=>(a(),u("code",{key:l,rounded:"","bg-primary:5":"",p:"x2 y0.5","text-primary":""},G(l),1))),128))]))}}),se={class:"markdown-body"},ae={__name:"imports",setup(D,{expose:r}){return r({frontmatter:{}}),(o,e)=>{const l=ee,m=oe;return a(),u("div",se,[e[0]||(e[0]=i("h1",null,"Auto imports",-1)),e[1]||(e[1]=i("p",null,"Nuxt auto-imports helper functions, composables and Vue APIs to be used across your application without explicitly importing them. Based on the directory structure, every Nuxt application can also use auto-imports for its own components, composables and plugins. Components, composables or plugins can use these functions.",-1)),e[2]||(e[2]=i("hr",null,null,-1)),e[3]||(e[3]=i("p",null,"According to your config, exports of files under the following folders will be registed as auto-imports entry:",-1)),c(l),e[4]||(e[4]=i("p",null,"Meanwhile, modules could also provide auto-imports for their own components. You have auto-imports from the following modules as well:",-1)),c(m),e[5]||(e[5]=i("hr",null,null,-1)),e[6]||(e[6]=i("p",null,[i("a",{href:"https://nuxt.com/docs/guide/concepts/auto-imports",target:"_blank",rel:"noopener"},"Learn more in the documentation")],-1)),e[7]||(e[7]=i("h2",null,"Directives",-1)),e[8]||(e[8]=i("p",null,"Directives placed in the directives/ directory are automatically registered by Nuxt. They can be used in your templates without importing them.",-1)),e[9]||(e[9]=i("p",null,[i("a",{href:"https://nuxt.com/docs/guide/directory-structure/directives",target:"_blank",rel:"noopener"},"Learn more in the documentation")],-1))])}}},le={key:0,relative:"","h-full":"","of-auto":""},re={key:0,flex:"~ gap-2 items-center lt-sm:col lt-sm:items-start"},$e=C({__name:"imports",setup(D){const r=j(),v=V("all"),o=V("all"),e=V(""),l=A(),m=_(()=>l.value?.metadata),g=_(()=>l.value?.imports.filter(d=>d.as||d.name).sort((d,n)=>(d.as||d.name).localeCompare(n.as||n.name))||[]),z=_(()=>new P(g.value,{keys:["from","as","name"]})),s=_(()=>{const d=new Map,n=new Map,I=new Map;let p=e.value?z.value.search(e.value).map(t=>t.item):g.value;const N=o.value;N==="composables"?p=p.filter(t=>t.meta?.vueDirective!==!0):N==="directives"&&(p=p.filter(t=>t.meta?.vueDirective===!0)),v.value==="using"&&m.value?p=p.filter(t=>(t.as||t.name)in m.value.injectionUsage):v.value==="not-used"&&m.value&&(p=p.filter(t=>!((t.as||t.name)in m.value.injectionUsage)));const h={user:0,lib:0,builtin:0};return p.forEach(t=>{const b=X(t.from)?T(S(t.from))?I:n:d;b.has(t.from)||b.set(t.from,[]),b.get(t.from).push(t),h[b===d?"user":b===n?"lib":"builtin"]++}),{user:d,lib:n,builtin:I,count:h}}),F=_(()=>o.value==="directives"?`${s.value.count.user} directives from ${s.value.user.size} modules`:`${s.value.count.user} composables from ${s.value.user.size} modules`),E=_(()=>o.value==="directives"?`${s.value.count.builtin} directives`:`${s.value.count.builtin} composables`),H=_(()=>o.value==="directives"?`${s.value.count.lib} directives from ${s.value.lib.size} packages`:`${s.value.count.lib} composables from ${s.value.lib.size} packages`);return(d,n)=>{const I=Y,p=q,N=J,h=R,t=K,b=ae,L=O;return a(),u(k,null,[f(r)?(a(),u("div",le,[c(N,{search:e.value,"onUpdate:search":n[2]||(n[2]=y=>e.value=y),pb3:""},{default:$(()=>[m.value?(a(),u("div",re,[c(I,{icon:"carbon-filter",op50:""}),c(p,{modelValue:v.value,"onUpdate:modelValue":n[0]||(n[0]=y=>v.value=y),n:"primary sm",options:[{label:"All",value:"all"},{label:"Using",value:"using"},{label:"Not used",value:"not-used"}]},null,8,["modelValue"]),c(p,{modelValue:o.value,"onUpdate:modelValue":n[1]||(n[1]=y=>o.value=y),n:"primary sm",options:[{label:"All",value:"all"},{label:"Composables",value:"composables"},{label:"Directives",value:"directives"}]},null,8,["modelValue"])])):x("",!0)]),_:1},8,["search"]),s.value.user.size?(a(),w(t,{key:0,open:s.value.count.user<=f(B),icon:o.value==="directives"?"tabler:hexagon-letter-d":"carbon-function",text:`User ${o.value==="directives"?"directives":"composables"}`,description:F.value},{default:$(()=>[c(h,{map:s.value.user,root:f(r).rootDir,metadata:m.value},null,8,["map","root","metadata"])]),_:1},8,["open","icon","text","description"])):x("",!0),s.value.builtin.size?(a(),w(t,{key:1,open:s.value.count.builtin<=f(B),icon:"simple-icons-nuxtdotjs",text:`Built-in ${o.value==="directives"?"directives":"composables"}`,description:E.value},{default:$(()=>[c(h,{map:s.value.builtin,root:f(r).rootDir,metadata:m.value},null,8,["map","root","metadata"])]),_:1},8,["open","text","description"])):x("",!0),s.value.lib.size?(a(),w(t,{key:2,open:s.value.count.lib<=f(B),icon:"carbon-3d-mpr-toggle",text:`${o.value==="directives"?"Directives":"Composables"} from libraries`,description:H.value},{default:$(()=>[c(h,{map:s.value.lib,root:f(r).rootDir,metadata:m.value},null,8,["map","root","metadata"])]),_:1},8,["open","text","description"])):x("",!0)])):x("",!0),c(L,null,{default:$(()=>[c(b)]),_:1})],64)}}});export{$e as default};
