import{a5 as E,H as V,r as f,E as p,_ as $,ap as B,Z as F,I,aa as D}from"./czf9xkmw.js";import{x as M,a as P}from"./vendor/xterm-dbpzgj7s.js";import{p as y,k as g,E as R,J as d,a3 as t,F as A,S as c,a5 as N,V as m,U as v,aa as _,q,w as z,u as w,ag as L,ab as C,Z as h,al as S,a4 as Z}from"./vendor/json-editor-vue-m9gzt21j.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";const j={border:"t base",flex:"~ gap-2","items-center":"",p2:""},H={"text-sm":"",op50:""},J=y({__name:"TerminalView",props:{id:{}},setup(b){const n=b,r=g(),i=E(),o=g();let a;R(async()=>{a=new M.Terminal({convertEol:!0,cols:80,screenReaderMode:!0});const u=new P.FitAddon;a.loadAddon(u),a.open(r.value),u.fit(),V(window,"resize",()=>{u.fit()}),o.value=await f.getTerminalDetail(await p(),n.id),o.value?.buffer&&a.write(o.value.buffer),i.hook("devtools:terminal:data",({id:e,data:l})=>{e===n.id&&a.write(l)})});async function s(){f.runTerminalAction(await p(),n.id,"clear"),a?.clear()}async function k(){f.runTerminalAction(await p(),n.id,"restart")}async function x(){f.runTerminalAction(await p(),n.id,"terminate")}return(u,e)=>{const l=$;return t(),d(A,null,[c("div",{ref_key:"container",ref:r,"h-full":"","w-full":"","of-auto":"","bg-black":""},null,512),c("div",j,[N(l,{title:"Clear",icon:"i-carbon-clean",border:!1,onClick:e[0]||(e[0]=T=>s())}),o.value?.restartable?(t(),m(l,{key:0,title:"Restart",icon:"carbon-renew",border:!1,onClick:e[1]||(e[1]=T=>k())})):v("",!0),o.value?.terminatable?(t(),m(l,{key:1,title:"Terminate",icon:"carbon-delete",border:!1,onClick:e[2]||(e[2]=T=>x())})):v("",!0),c("span",H,_(o.value?.description),1)])],64)}}}),U={key:0,"h-full":"","w-full":"","of-hidden":"",grid:"~ rows-[max-content_1fr_max-content]"},G={flex:"~",border:"b base","n-navbar-glass":"","flex-1":"","items-center":""},K=["onClick"],O={key:1,p10:""},Q={key:1,"h-full":"",flex:"","items-center":"","justify-center":""},W=y({__name:"TerminalPage",setup(b){const n=B(),r=F(),i=q(()=>n.value?.find(a=>a.id===r.value));async function o(a){f.runTerminalAction(await p(),a,"remove")}return z(()=>{!r.value&&n.value?.length&&(r.value=n.value[0].id)}),(a,s)=>{const k=I,x=$,u=J;return w(n)?.length?(t(),d("div",U,[c("div",G,[(t(!0),d(A,null,L(w(n),e=>(t(),d("button",{key:e.id,border:"r base",flex:"~ gap-2","items-center":"",px3:"",py2:"",class:h(e.id===i.value?.id?"bg-active":""),onClick:l=>r.value=e.id},[e.icon?(t(),m(k,{key:0,icon:e.icon},null,8,["icon"])):v("",!0),c("span",{class:h(e.id===i.value?.id?"":"op50")},_(e.name)+_(e.isTerminated?" (terminated)":""),3),e.isTerminated?(t(),m(x,{key:1,icon:"carbon-close","mx--2":"",border:!1,onClick:S(l=>o(e.id),["stop"])},null,8,["onClick"])):v("",!0)],10,K))),128))]),i.value?(t(),m(u,{id:i.value.id,key:i.value.id},null,8,["id"])):(t(),d("div",O,[s[0]||(s[0]=C(" Terminal ")),c("code",null,_(w(r)),1),s[1]||(s[1]=C(" not found "))]))])):(t(),d("div",Q,s[2]||(s[2]=[c("em",{op50:""},"No terminal attached",-1)])))}}}),ae=y({__name:"terminals",setup(b){return(n,r)=>{const i=W,o=D;return t(),m(o,null,{default:Z(()=>[N(i)]),_:1})}}});export{ae as default};
