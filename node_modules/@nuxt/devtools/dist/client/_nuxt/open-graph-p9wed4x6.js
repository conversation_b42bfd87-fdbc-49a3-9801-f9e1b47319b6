import{at as oe,a as ae,w as H,n as J,_ as U,au as G,av as ne,aw as se,ax as re,b as ie,ay as le,az as ce,c as de,y as pe,z as ue,C as me,aA as E,v as ge}from"./czf9xkmw.js";import{_ as he}from"./nnavbar.vue-ma0lzxrm.js";import{_ as P}from"./nlink.vue-d4id0o0s.js";import{_ as q}from"./nsection-block-m4vpsvnn.js";import{_ as _e}from"./ncode-block.vue-ctkw4rc7.js";import{p as N,q as C,k as I,V as A,U as y,a3 as t,a4 as h,S as e,J as n,F as S,ag as B,Z as O,aa as u,a5 as p,ab as $,u as g,X as L,am as fe,z as ve,ai as be,ac as D,Y as we}from"./vendor/json-editor-vue-m9gzt21j.js";import{_ as xe}from"./help-fab.vue-b6h1gmzk.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";import"./nicon-title.vue-ejocqf9t.js";import"./client-oeqdl4pb.js";const K=[{name:"title",suggestion:"required",head:{title:"[title]"},seoMeta:{title:"[title]"},docs:"https://developer.mozilla.org/en-US/docs/Web/HTML/Element/title",description:"A concise and descriptive title for the browser that accurately summarizes the content of the page."},{name:"description",suggestion:"required",head:{meta:[{name:"description",content:"[description]"}]},seoMeta:{description:"[description]"},description:"A one to two sentence summary for search engines that includes relevant keywords to improve visibility in search results."},{name:"icon",suggestion:"recommended",head:{link:[{rel:"icon",type:"image/png",href:"/favicon.png"}]},description:"A small image that appears in the browser tab and bookmark menu to help users easily identify the page."},{name:"lang",suggestion:"recommended",head:{htmlAttrs:{lang:"en"}},description:"The primary language of the page to help search engines and browsers understand the content."},{name:"og:title",suggestion:"recommended",head:{meta:[{property:"og:title",content:"[og:title]"}]},seoMeta:{ogTitle:"[og:title]"},docs:"https://ogp.me/#metadata",description:"A title for the link preview used by social media platforms."},{name:"og:description",suggestion:"recommended",head:{meta:[{property:"og:description",content:"[og:description]"}]},seoMeta:{ogDescription:"[og:description]"},docs:"https://ogp.me/#metadata",description:"A description for the link preview used by social media platforms."},{name:"og:image",suggestion:"recommended",head:{meta:[{property:"og:image",content:"[og:image]"}]},seoMeta:{ogImage:"[og:image]"},docs:"https://ogp.me/#metadata",description:"An image for the link preview used by social media platforms."},{name:"og:url",suggestion:"recommended",head:{meta:[{property:"og:url",content:"[og:url]"}]},seoMeta:{ogUrl:"[og:url]"},docs:"https://ogp.me/#metadata",description:"A canonical URL for the link preview used to specify the preferred URL to display in search engine results and social media previews when multiple URLs may point to the same page."},{name:"twitter:title",suggestion:"optional",head:{meta:[{name:"twitter:title",content:"[twitter:title]"}]},seoMeta:{twitterTitle:"[twitter:title]"},docs:"https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/abouts-cards",description:"A title for the Twitter card used to provide a preview of the content shared on the page."},{name:"twitter:description",suggestion:"optional",head:{meta:[{name:"twitter:description",content:"[twitter:description]"}]},seoMeta:{twitterDescription:"[twitter:description]"},docs:"https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/abouts-cards",description:"A description for the Twitter card used to provide a preview of the content shared on the page."},{name:"twitter:image",suggestion:"optional",head:{meta:[{name:"twitter:image",content:"[twitter:image]"}]},seoMeta:{twitterImage:"[twitter:image]"},docs:"https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/abouts-cards",description:"An image for the Twitter card used to provide a preview of the content shared on the page."},{name:"twitter:card",suggestion:"optional",head:{meta:[{name:"twitter:card",content:"summary"}]},seoMeta:{twitterCard:"summary"},docs:"https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/abouts-cards",description:"The type of Twitter card to use, which determines the type of card to display in link previews on Twitter."}],ye={flex:"~ wrap","mt--2":"","w-full":"","flex-none":""},ke=["onClick"],$e={key:0,"x-divider":""},Te={key:1,"x-divider":""},Se={flex:"~ gap-1 items-center",class:"px2 pt2",lg:"px4 py2"},Ce={"w-full":"",p2:"",op75:""},Ne={key:1,m4:"",flex:"~ col gap-2"},Me={flex:"~ gap-1 wrap items-center"},Oe={key:1},Ae={flex:"~ gap-2",n:"sm primary",absolute:"","right-2":"","top-2":""},Ie=N({__name:"OpenGraphMissingTabs",props:{tags:{},matchedRouteFilepath:{}},setup(v){const s=v,l=C(()=>K.filter(d=>!s.tags?.some(o=>o.name===d.name))),a=C(()=>{let d={};const o={};l.value.forEach(m=>{m.seoMeta?Object.assign(o,m.seoMeta):d=oe(d,m.head)});const w=[];if(Object.keys(o).length){const m=JSON.stringify(o,null,2).replace(/"([^"]+)":/g,"$1:").replace(/"/g,"'");w.push(`useSeoMeta(${m})`)}if(Object.keys(d).length){const m=JSON.stringify(d,null,2).replace(/"([^"]+)":/g,"$1:").replace(/"/g,"'");w.push(`useHead(${m})`)}return w.join(`

`)}),c=ae(),k=H(),b=["Missing Tags","Code Snippet"],_=I(b[0]);return(d,o)=>{const w=P,m=J,T=U,M=_e,R=q;return l.value.length?(t(),A(R,{key:0,text:"Missing Tags",description:`${l.value.length} missing tags`,icon:"carbon:warning-other","header-class":"text-orange op100! [[open]_&]:text-inherit",padding:!1},{default:h(()=>[e("div",ye,[(t(),n(S,null,B(b,(r,i)=>e("button",{key:i,px4:"",py2:"",border:"r t base",hover:"bg-active",class:O(r===_.value?"":"border-b"),onClick:f=>_.value=r},[e("div",{class:O(r===_.value?"":"op30"),capitalize:""},u(r),3)],10,ke)),64)),o[3]||(o[3]=e("div",{border:"b base","flex-auto":""},null,-1))]),_.value===b[0]?(t(),A(m,{key:0,grid:"~ cols-[1fr] lg:cols-[max-content_1fr]",m4:"","items-center":"","justify-between":"","of-hidden":""},{default:h(()=>[(t(!0),n(S,null,B(l.value,(r,i)=>(t(),n(S,{key:i},[i?(t(),n("div",$e)):y("",!0),i?(t(),n("div",Te)):y("",!0),e("div",Se,[o[4]||(o[4]=e("div",{"i-carbon-warning":"","text-orange":""},null,-1)),p(w,{"op-50":"",href:r.docs,target:"_blank",n:"orange"},{default:h(()=>[$(u(r.name),1)]),_:2},1032,["href"])]),e("div",Ce,u(r.description),1)],64))),128))]),_:1})):(t(),n("div",Ne,[e("p",Me,[p(T,{icon:"carbon-copy",n:"xs","px-2":"",onClick:o[0]||(o[0]=r=>g(c)(a.value,"open-graph-suggestion"))},{default:h(()=>o[5]||(o[5]=[$(" Copy ")])),_:1,__:[5]}),o[7]||(o[7]=$(" the following code snippet and paste it into your ")),d.matchedRouteFilepath?(t(),A(T,{key:0,icon:"carbon-launch",n:"xs","px-2":"",onClick:o[1]||(o[1]=r=>g(k)(d.matchedRouteFilepath))},{default:h(()=>o[6]||(o[6]=[$(" page component ")])),_:1,__:[6]})):(t(),n("span",Oe,"page component")),o[8]||(o[8]=$(" to full fill the missing tags. "))]),p(m,{relative:"","n-code-block":""},{default:h(()=>[p(M,{code:a.value,lang:"ts",lines:!1,"w-full":"","of-auto":"",p3:""},null,8,["code"]),e("div",Ae,[p(T,{icon:"carbon-copy",onClick:o[2]||(o[2]=r=>g(c)(a.value,"open-graph-suggestion"))},{default:h(()=>o[9]||(o[9]=[$(" Copy ")])),_:1,__:[9]})])]),_:1})]))]),_:1},8,["description"])):y("",!0)}}}),Be={class:"max-w-[524px] min-w-[524px] cursor-pointer bg-base"},Le={class:"break-words border border-base px-[12px] py-[10px] antialiased"},Re={class:"overflow-hidden truncate whitespace-nowrap text-[12px] leading-[11px] uppercase op50"},ze={class:"block h-[46px] max-h-[46px] border-separate select-none overflow-hidden break-words text-left",style:{"border-spacing":"0px"}},Fe={class:"mt-[3px] truncate pt-[2px] text-[16px] font-semibold leading-[20px]"},je={class:"mt-[3px] block h-[18px] max-h-[80px] border-separate select-none overflow-hidden truncate whitespace-nowrap break-words text-left text-[14px] leading-[20px] op50",style:{"-webkit-line-clamp":"1","border-spacing":"0px","-webkit-box-orient":"vertical"}},De=N({__name:"SocialFacebook",props:{card:{}},setup(v){return(s,l)=>(t(),n("div",Be,[e("div",{class:"h-[274px] border border-b-0 border-base bg-cover bg-center bg-no-repeat",style:L({backgroundImage:`url(${JSON.stringify(s.card.image)})`})},null,4),e("div",Le,[e("div",Re,u(s.card.url),1),e("div",ze,[e("div",Fe,u(s.card.title),1),e("div",je,u(s.card.description),1)])])]))}}),Ve={class:"max-w-[438px] min-w-[438px] of-hidden border border-base rounded-[16px] bg-base -outline-offset-1",style:{"font-family":"TwitterChirp, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif"}},Ee={class:"break-words border-base p-[0.75em] antialiased",flex:"~ col justify-center gap-[2px]"},He={class:"overflow-hidden truncate whitespace-nowrap text-[15px] leading-[20px] lowercase op50"},Je={class:"m-0 truncate text-[15px] font-semibold leading-[20px]"},Ue={class:"line-clamp-2 select-none overflow-hidden break-words text-left text-[15px] leading-[20px] op50"},Ge=N({__name:"SocialTwitter",props:{tags:{}},setup(v){const s=v,l=C(()=>G(s.tags,{title:[{tag:"title"}],image:[{tag:"meta",name:"twitter:image"},{tag:"meta",name:"og:image"}],imageAlt:[{tag:"meta",name:"twitter:image:alt"}],description:[{tag:"meta",name:"twitter:description"},{tag:"meta",name:"description"}],favicon:[{tag:"link",name:"icon"}]})),a=C(()=>l.value.image?s.tags.find(c=>c.tag==="meta"&&c.name==="twitter:card")?.value||"summary_large_image":"summary");return(c,k)=>(t(),n("div",Ve,[e("div",{class:O(["cursor-pointer overflow-hidden leading-[1.3em]",a.value==="summary_large_image"?"":"flex"]),hover:"bg-[#88888805]"},[a.value==="summary_large_image"?(t(),n("div",{key:0,class:"h-[220px] border-b border-base bg-cover bg-center bg-no-repeat",style:L({backgroundImage:`url(${JSON.stringify(l.value.image)})`})},null,4)):(t(),n("div",{key:1,class:"h-[129px] w-[129px] flex-none border-r border-base bg-cover bg-center bg-no-repeat",style:L({backgroundImage:`url(${JSON.stringify(l.value.image)})`})},null,4)),e("div",Ee,[e("div",He,u(l.value.url),1),e("div",Je,u(l.value.title),1),e("div",Ue,u(l.value.description),1)])],2)]))}}),Pe={class:"max-w-[520px] min-w-[520px] cursor-pointer overflow-hidden border border-base rounded-[2px] bg-base shadow-md"},qe={class:"break-words p-[10px] antialiased"},Ke={class:"block h-auto max-h-[50px] border-separate select-none break-words text-left",style:{"border-spacing":"0px"}},Ye={class:"pb-[2px] text-[16px] font-semibold leading-[24px]"},We={class:"overflow-hidden truncate whitespace-nowrap text-xs font-normal uppercase op85"},Xe=N({__name:"SocialLinkedin",props:{card:{}},setup(v){return(s,l)=>(t(),n("div",Pe,[e("div",{class:"h-[270px] border-b border-base bg-cover bg-center bg-no-repeat",style:L({backgroundImage:`url(${JSON.stringify(s.card.image)})`})},null,4),e("div",qe,[e("div",Ke,[e("div",Ye,u(s.card.title),1),e("div",We,u(s.card.url),1)])])]))}}),Ze={class:"relative max-w-[420px] min-w-[420px] rounded-[16px] rounded-bl-0 bg-base py-[6px] leading-[18px] drop-shadow-sm drop-shadow-color-[#10232f26]"},Qe={class:"pl-[10px] pr-[8px]"},et={class:"text-[#3390ec] leading-normal underline"},tt={class:"quote w-full flex"},ot={class:"flex flex-col gap-1"},at={key:0,class:"my-[3px] overflow-hidden rounded"},nt=["src"],st={class:"cursor-pointer text-sm text-[#3390ec]"},rt={class:"cursor-pointer text-sm"},it={class:"cursor-pointer text-sm"},lt={class:"flex justify-end text-xs text-[#707579]"},ct=N({__name:"SocialTelegram",props:{card:{}},setup(v){const s=ne(),l=se(re(),"HH:mm");return(a,c)=>(t(),n("div",Ze,[e("div",Qe,[e("div",et,u(a.card.url),1),e("div",tt,[e("div",ot,[a.card.image?(t(),n("div",at,[e("img",{class:"h-full max-w-full w-full rounded object-cover",src:a.card.image},null,8,nt)])):y("",!0),e("div",st,[e("strong",null,u(g(s).hostname),1)]),e("div",rt,[e("strong",null,u(a.card.title),1)]),e("div",it,u(a.card.description),1)])]),e("div",lt,[e("span",null,u(g(l)),1)])]),c[0]||(c[0]=e("svg",{width:"11",height:"20",viewBox:"0 0 11 20",xmlns:"http://www.w3.org/2000/svg",class:"absolute bottom-0 ml-[-8.4px] translate-y-px text-white"},[e("g",{transform:"translate(9 -14)","fill-rule":"evenodd"},[e("path",{id:"corner-fill",d:"M-6 16h6v17c-.193-2.84-.876-5.767-2.05-8.782-.904-2.325-2.446-4.485-4.625-6.48A1 1 0 01-6 16z",transform:"matrix(1 0 0 -1 0 49)",fill:"currentColor"})])],-1))]))}}),dt=ie(ct,[["__scopeId","data-v-4566448e"]]),pt={flex:"~ col","w-full":""},ut={flex:"~ wrap","w-full":"","flex-none":""},mt=["onClick"],gt={flex:"~ items-center justify-center","flex-auto":"",p4:"","n-panel-grids":""},ht={key:0},_t={key:1},ft={key:2},vt={key:3},bt=N({__name:"SocialPreviewGroup",props:{tags:{}},setup(v){const s=v,l=["twitter","facebook","linkedin","telegram"],a=le("nuxt-devtools-social-preview-tab",l[0]),c=C(()=>G(s.tags,{title:[{tag:"title"}],image:[{tag:"meta",name:"og:image"}],imageAlt:[{tag:"meta",name:"og:image:alt"}],description:[{tag:"meta",name:"og:description"},{tag:"meta",name:"description"}],favicon:[{tag:"link",name:"icon"}]}));return(k,b)=>{const _=De,d=Ge,o=Xe,w=dt;return t(),n("div",pt,[e("div",ut,[(t(),n(S,null,B(l,(m,T)=>e("button",{key:T,px4:"",py2:"",border:"r base",hover:"bg-active",class:O(m===g(a)?"":"border-b"),onClick:M=>a.value=m},[e("div",{class:O(m===g(a)?"":"op30"),capitalize:""},u(m),3)],10,mt)),64)),b[0]||(b[0]=e("div",{border:"b base","flex-auto":""},null,-1))]),e("div",gt,[g(a)==="facebook"?(t(),n("div",ht,[p(_,{card:c.value},null,8,["card"])])):g(a)==="twitter"?(t(),n("div",_t,[p(d,{tags:k.tags},null,8,["tags"])])):g(a)==="linkedin"?(t(),n("div",ft,[p(o,{card:c.value},null,8,["card"])])):g(a)==="telegram"?(t(),n("div",vt,[p(w,{card:c.value},null,8,["card"])])):y("",!0)])])}}}),wt={class:"markdown-body"},xt={__name:"open-graph",setup(v,{expose:s}){return s({frontmatter:{}}),(a,c)=>(t(),n("div",wt,c[0]||(c[0]=[fe('<h1>Open Graph</h1><p>Nuxt provides several different ways to manage your meta tags using <a href="https://unhead.harlanzw.com/" target="_blank" rel="noopener"><code>unhead</code></a>. Improve your Nuxt app’s SEO with powerful head config, composables and components.</p><p><a href="https://nuxt.com/docs/getting-started/seo-meta" target="_blank" rel="noopener">Learn more on the documentation</a></p><hr><p>You can also find how open graph specs are defined in:</p><ul><li><a href="https://ogp.me/" target="_blank" rel="noopener">The Open Graph protocol</a></li><li><a href="https://developer.twitter.com/en/docs/twitter-for-websites/cards/guides/getting-started" target="_blank" rel="noopener">Twitter Cards</a></li></ul>',6)])))}},yt={grid:"~ lg:cols-2","h-full":"","w-full":"","of-hidden":""},kt={"flex-auto":"","of-auto":""},$t={"flex-none":"",flex:"~ gap2 items-center"},Tt={flex:"~ col"},St={key:0,"x-divider":""},Ct={key:1,"x-divider":""},Nt={mr2:"",px4:"",py2:""},Vt=N({__name:"open-graph",setup(v){const s=I(0),l=ce(),a=de(async()=>(s.value,(await l.value?.resolveTags()).map(i=>{const f=i.props||{};return i.tag==="htmlAttrs"&&f.lang?{tag:"html",name:"lang",value:f.lang}:f.charset?{tag:"meta",name:"charset",value:f.charset}:{tag:i.tag,name:f.property??f.name??f.rel??i.tag,value:f.content??f.href??i.textContent??JSON.stringify(f)}})),[]),c=I(!0);function k(){s.value+=1}const b=pe(),_=ue(),d=I(""),o=me(),w=H();async function m(){d.value!==_.value.path&&b.value.push(d.value||"/")}const T=C(()=>d.value===_.value.path?[]:b.value.resolve(d.value||"/").matched),M=C(()=>{const r=o.value.find(i=>i.path===_.value?.matched?.[0]?.path);return r?.file||r?.meta?.file});function R(r){return K.find(i=>i.name===r.name)?.docs}return E(_).toBeTruthy().then(r=>{d.value=r.path}),E(b).toBeTruthy().then(r=>{r.afterEach(()=>{ve(()=>{d.value=_.value.path,setTimeout(k,200),setTimeout(k,800)})})}),(r,i)=>{const f=ge,z=U,Y=he,V=P,W=J,X=q,Z=Ie,Q=bt,ee=xt,te=xe,F=be("tooltip");return t(),n(S,null,[e("div",yt,[e("div",kt,[p(Y,null,{search:h(()=>[p(f,{modelValue:d.value,"onUpdate:modelValue":i[0]||(i[0]=x=>d.value=x),placeholder:"Route",icon:"carbon-direction-right-01 scale-y--100",n:"primary","flex-auto":"","font-mono":"",class:O(["px-5 py-2",g(_)?.path===d.value?"":T.value.length?"text-green":"text-orange"]),onKeydown:we(m,["enter"])},null,8,["modelValue","class"])]),actions:h(()=>[e("div",$t,[M.value?D((t(),A(z,{key:0,"text-lg":"",border:!1,icon:"carbon:launch",title:"Open file in editor",onClick:i[1]||(i[1]=x=>g(w)(M.value))},null,512)),[[F,"Open file in editor"]]):y("",!0),D(p(z,{"text-lg":"",border:!1,icon:"carbon:reset",title:"Refresh Data",onClick:k},null,512),[[F,"Refresh Data"]]),D(p(z,{"text-lg":"",border:!1,icon:c.value?"carbon:side-panel-open":"carbon:open-panel-right",title:"Toggle Preview",onClick:i[2]||(i[2]=x=>c.value=!c.value)},null,8,["icon"]),[[F,"Toggle Preview"]])])]),_:1}),e("div",Tt,[p(X,{text:"Tags",icon:"carbon:tag-group"},{default:h(()=>[p(W,{grid:"~ cols-[max-content_1fr]","items-center":"","justify-between":"","of-hidden":""},{default:h(()=>[(t(!0),n(S,null,B(g(a),(x,j)=>(t(),n(S,{key:j},[j?(t(),n("div",St)):y("",!0),j?(t(),n("div",Ct)):y("",!0),e("div",Nt,[p(V,{op50:"",href:R(x),target:"_blank",n:"primary"},{default:h(()=>[$(u(x.name),1)]),_:2},1032,["href"])]),p(V,{href:String(x.value).match(/^https?:\/\//)?x.value:void 0,target:"_blank","w-full":"",p2:"","font-mono":"",n:"primary"},{default:h(()=>[$(u(x.value),1)]),_:2},1032,["href"])],64))),128))]),_:1})]),_:1}),p(Z,{tags:g(a),"matched-route-filepath":M.value},null,8,["tags","matched-route-filepath"])])]),c.value&&g(a)?.length?(t(),A(Q,{key:0,tags:g(a),"flex-none":"","border-base":"","lt-lg":"border-t",lg:"h-full w-140 border-l"},null,8,["tags"])):y("",!0)]),p(te,null,{default:h(()=>[p(ee)]),_:1})],64)}}});export{Vt as default};
