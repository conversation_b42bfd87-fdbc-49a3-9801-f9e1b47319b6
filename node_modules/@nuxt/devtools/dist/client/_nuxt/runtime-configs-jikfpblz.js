import{_ as x}from"./state-editor.vue-dxli1s6t.js";import{_ as b}from"./nsection-block-m4vpsvnn.js";import{J as u,a3 as l,S as o,ab as d,p as y,q as c,U as C,a5 as e,u as f,a4 as a,F as k}from"./vendor/json-editor-vue-m9gzt21j.js";import{_ as w}from"./help-fab.vue-b6h1gmzk.js";import{f as N,bk as R}from"./czf9xkmw.js";import"./data-schema-button.vue-i6u1wh6f.js";import"./nicon-title.vue-ejocqf9t.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";const T={class:"markdown-body"},B={__name:"runtime-configs",setup(_,{expose:t}){return t({frontmatter:{}}),(m,n)=>(l(),u("div",T,n[0]||(n[0]=[o("h1",null,"Runtime Configs",-1),o("p",null,"Nuxt provides a runtime config API to expose configuration within your application and server routes, with the ability to update it at runtime by setting environment variables.",-1),o("p",null,[d("In Nuxt DevTools, the editor allows you to edit the runtime config temporarily to see how the changes affect your application. To update them, you might want to update your "),o("code",null,"nuxt.config.js"),d(" file, or check more details in the documentation.")],-1),o("p",null,[o("a",{href:"https://nuxt.com/docs/guide/going-further/runtime-config",target:"_blank",rel:"noopener"},"Learn more in the documentation")],-1)])))}},S={key:0},H=y({__name:"runtime-configs",setup(_){const t=N(),p=R(),m=c(()=>t.value?.nuxt.payload),n=c(()=>t.value?.revision.value),g=c(()=>{const i={...p.value};return delete i.public,delete i.app,i});return(i,V)=>{const s=x,r=b,v=B,h=w;return l(),u(k,null,[f(t)?(l(),u("div",S,[e(r,{icon:"carbon-settings-services",text:"App Config",padding:!1},{default:a(()=>[e(s,{state:f(t).app.appConfig,revision:n.value},null,8,["state","revision"])]),_:1}),e(r,{icon:"carbon-settings",text:"Public Runtime Config",padding:!1},{default:a(()=>[e(s,{state:m.value.config?.public,revision:n.value},null,8,["state","revision"])]),_:1}),e(r,{icon:"i-carbon-code-signing-service",text:"Private Runtime Config",open:!1,padding:!1,description:"These values are not exposed to the client. Readonly in the DevTools."},{default:a(()=>[e(s,{state:g.value,revision:n.value,readonly:""},null,8,["state","revision"])]),_:1})])):C("",!0),e(h,null,{default:a(()=>[e(v)]),_:1})],64)}}});export{H as default};
