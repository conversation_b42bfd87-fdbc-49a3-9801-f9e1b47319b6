import{v as S,_ as H,w as J,x as F,a as Y,y as Z,z as G,A as Q,B as X,C as ee,D as te,r as oe,E as ne}from"./czf9xkmw.js";import{_ as j}from"./nbadge-bu0b8pjx.js";import{p as L,k as A,q as x,$ as ae,J as a,V as k,a3 as t,S as e,aa as C,a4 as $,F as g,ag as I,Z as M,al as se,U as N,a5 as _,ab as E,u as c,E as re,Y as le}from"./vendor/json-editor-vue-m9gzt21j.js";import{_ as z}from"./filepath-item.vue-dx8apiq4.js";import{_ as ue}from"./nsection-block-m4vpsvnn.js";import{_ as ie}from"./launch-page.vue-k0b466z9.js";import{_ as pe}from"./help-fab.vue-b6h1gmzk.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";import"./nicon-title.vue-ejocqf9t.js";import"./nmarkdown.vue-mk2gi3ky.js";import"./client-oeqdl4pb.js";const de={block:"","cursor-pointer":""},ce={p2:""},me=["onSubmit"],ve={flex:"~","items-center":"",p2:"","text-sm":"","font-mono":""},he={key:1},_e=L({__name:"RoutePathItem",props:{route:{}},emits:["navigate"],setup(P,{emit:i}){const v=P,R=i,m=A([]),f=x(()=>{const r=w(v.route.path);return m.value=Array.from({length:r.length},()=>""),r});function w(r){return r.split(/(:\w+[?*]?(?:\(\))?)/).filter(Boolean).map(d=>d[0]===":"?d.replace(/\(\)$/,"?"):d)}const y=x(()=>f.value.map((r,d)=>r[0]===":"?m.value[d]:r).join("").replace(/\/+/g,"/")),o=x(()=>v.route.path.includes(":"));function s(){R("navigate",y.value)}return(r,d)=>{const h=S,n=H,l=ae("VDropdown");return o.value?(t(),k(l,{key:1},{popper:$(({hide:b})=>[e("div",ce,[e("form",{flex:"~ col",onSubmit:se(()=>{s(),b()},["prevent"])},[o.value?(t(),a(g,{key:0},[d[0]||(d[0]=e("div",{px2:"","text-sm":"",op50:""}," Fill params and navigate: ",-1)),e("div",ve,[(t(!0),a(g,null,I(f.value,(u,p)=>(t(),a(g,{key:p},[u[0]===":"?(t(),k(h,{key:0,modelValue:m.value[p],"onUpdate:modelValue":B=>m.value[p]=B,"w-20":"","n-sm":"",placeholder:u.slice(1)},null,8,["modelValue","onUpdate:modelValue","placeholder"])):(t(),a("span",he,C(u),1))],64))),128))])],64)):N("",!0),_(n,{type:"submit",block:"",n:"primary"},{default:$(()=>d[1]||(d[1]=[E(" Navigate ")])),_:1,__:[1]})],40,me)])]),default:$(()=>[e("code",de,[(t(!0),a(g,null,I(f.value,(b,u)=>(t(),a("span",{key:u,class:M(b[0]===":"?"text-gray border border-dashed rounded border-gray:50 px1":"")},C(b[0]===":"?b.slice(1):b),3))),128))])]),_:1})):(t(),a("button",{key:0,onClick:s},[e("code",null,C(r.route.path),1)]))}}}),fe={"max-w-full":"","of-auto":""},ge={"w-full":""},ye={"w-20":"","pr-1":""},be={flex:"","items-center":"","justify-end":""},xe={"text-sm":""},ke={flex:"inline gap3","items-center":""},we={flex:"~ gap1",pr2:"",op0:"","group-hover:op100":""},$e=["onClick"],Ce=["onClick"],Ne={"w-0":"","ws-nowrap":"","pr-1":"","text-left":"","text-sm":"","font-mono":"",op50:""},Re={"w-0":"","ws-nowrap":"","pr-1":"","text-center":"","text-sm":"","font-mono":"",op50:""},Ve={"w-0":"","ws-nowrap":"","text-center":"","text-sm":"","font-mono":""},Pe={key:0},Ee=["onClick"],Ie=L({__name:"RoutesTable",props:{pages:{},layouts:{},matched:{},matchedPending:{}},emits:["navigate"],setup(P){const i=P,v=J(),R=F(),m=Y(),f=x(()=>[...i.pages].sort((o,s)=>o.path.localeCompare(s.path)));function w(o){const s=i.layouts.find(r=>r.name===o);s&&v(s.file)}function y(o){if(typeof o=="string")return R.value?.middleware.find(s=>s.name===o)?.path}return(o,s)=>{const r=j,d=_e,h=z;return t(),a("div",fe,[e("table",ge,[s[4]||(s[4]=e("thead",{border:"b base"},[e("tr",null,[e("th",{"text-left":""}),e("th",{"text-left":""}," Route Path "),e("th",{"text-left":""}," Name "),e("th",{"text-left":""}," Middleware "),e("th",null," Layout ")])],-1)),e("tbody",null,[(t(!0),a(g,null,I(f.value,n=>(t(),a("tr",{key:n.name,class:"group","h-7":"",border:"b dashed transparent hover:base"},[e("td",ye,[e("div",be,[o.matched.find(l=>l.name===n.name)?(t(),k(r,{key:0,n:"green",title:"active",textContent:"active"})):o.matchedPending.find(l=>l.name===n.name)?(t(),k(r,{key:1,n:"teal",title:"next",textContent:"next"})):N("",!0)])]),e("td",xe,[e("div",ke,[_(d,{route:n,class:M(o.matched.find(l=>l.name===n.name)?"text-primary":o.matchedPending.find(l=>l.name===n.name)?"text-teal":""),"ws-nowrap":"",onNavigate:s[0]||(s[0]=l=>o.$emit("navigate",l))},null,8,["route","class"]),e("div",we,[n.file||n.meta?.file?(t(),a("button",{key:0,"text-sm":"",op40:"",hover:"op100 text-primary",title:"Open in editor",onClick:l=>c(v)(n.file||n.meta?.file)},s[2]||(s[2]=[e("div",{"i-carbon-script-reference":""},null,-1)]),8,$e)):N("",!0),n.file||n.meta?.file?(t(),a("button",{key:1,"text-sm":"",op40:"",hover:"op100 text-primary",title:"Copy path",onClick:l=>c(m)(n.file||n.meta?.file)},s[3]||(s[3]=[e("div",{"i-carbon-copy":""},null,-1)]),8,Ce)):N("",!0)])])]),e("td",Ne,C(n.name),1),e("td",Re,[_(h,{filepath:y(n.meta.middleware),override:`${n.meta.middleware||"-"}`},null,8,["filepath","override"])]),e("td",Ve,[n.meta.layout===!1?(t(),a("span",Pe,"-")):n.meta.layout?(t(),a("button",{key:1,onClick:l=>w(n.meta.layout)},C(n.meta.layout),9,Ee)):(t(),a("button",{key:2,"text-sm":"",op15:"",onClick:s[1]||(s[1]=l=>w("default"))}," (default) "))])]))),128))])])])}}}),Be={class:"markdown-body"},Te={__name:"pages",setup(P,{expose:i}){return i({frontmatter:{}}),(R,m)=>(t(),a("div",Be,m[0]||(m[0]=[e("h1",null,"Pages",-1),e("p",null,[E("Nuxt provides a file-based routing to create routes within your web application using Vue Router under the hood. Pages are Vue components and can have any valid extension that Nuxt supports (by default .vue, .js, .jsx, .mjs, .ts or .tsx). Nuxt will automatically create a route for every page in your "),e("code",null,"~/pages/"),E(" directory.")],-1),e("p",null,[e("a",{href:"https://nuxt.com/docs/getting-started/routing",target:"_blank",rel:"noopener"},"Learn more in the documentation")],-1),e("hr",null,null,-1),e("h1",null,"Middlewares",-1),e("p",null,"Nuxt provides a customizable route middleware framework that can be used throughout the application. This is ideal for extracting code that needs to run before navigating to a particular route.",-1),e("p",null,[e("a",{href:"https://nuxt.com/docs/guide/directory-structure/middleware",target:"_blank",rel:"noopener"},"Learn more in the documentation")],-1)])))}},Ae={key:0,"h-full":"","of-auto":""},Le={border:"b base",flex:"~ col gap1","n-navbar-glass":"",px4:"",py3:""},Me={"font-mono":""},De={key:1,op50:""},Se={key:0,"text-orange":"",op75:""},Fe={key:1,op50:""},je={"min-h-14":""},ze={key:1,class:"py-4 text-center"},Ue={"w-full":""},Ke={mr1:""},ot=L({__name:"pages",setup(P){const i=Z(),v=G(),R=Q(),m=F(),f=X(),w=ee(),y=x(()=>m.value?.middleware||[]),o=A(""),s=A(0),r=x(()=>(y.value,o.value,f.value,s.value,i.value?.currentRoute?.value?.path));re(()=>{v.value&&(o.value=i.value?.currentRoute?.value?.path),i.value?.beforeEach(u=>{o.value=u.fullPath}),i.value?.afterEach(u=>{o.value=u.fullPath})});async function d(){o.value!==i.value?.currentRoute?.value?.path&&i.value.push(o.value||"/")}const h=x(()=>i.value.resolve(o.value||"/").matched);function n(u){i.value.push(u),o.value=u}const l=te(),b=x(()=>`./${l===4?"app/":""}pages/index.vue`);return(u,p)=>{const B=S,D=Ie,T=ue,U=j,K=z,O=ie,W=Te,q=pe;return t(),a(g,null,[c(R)?.pages&&c(i)?(t(),a("div",Ae,[e("div",Le,[e("div",null,[r.value!==o.value?(t(),a(g,{key:0},[p[1]||(p[1]=e("span",{op50:""},"Navigate from ",-1)),e("span",Me,C(r.value),1),p[2]||(p[2]=e("span",{op50:""}," to ",-1))],64)):(t(),a("span",De,"Current route"))]),_(B,{modelValue:o.value,"onUpdate:modelValue":p[0]||(p[0]=V=>o.value=V),"font-mono":"",icon:"carbon-direction-right-01 scale-y--100",class:M(r.value===o.value?"":h.value.length?"text-green":"text-orange"),onKeydown:le(d,["enter"])},null,8,["modelValue","class"]),e("div",null,[r.value!==o.value?(t(),a(g,{key:0},[p[3]||(p[3]=e("span",null,[E("Press "),e("b",{"font-bold":""},"Enter"),E(" to navigate")],-1)),h.value.length?N("",!0):(t(),a("span",Se," (no match)"))],64)):(t(),a("span",Fe,"Edit path above to navigate"))])]),_(T,{icon:"carbon-tree-view",text:"Matched Routes",padding:!1},{default:$(()=>[e("div",je,[h.value.length?(t(),k(D,{key:0,pages:h.value,layouts:c(f)||[],matched:c(v).matched,"matched-pending":h.value,onNavigate:n},null,8,["pages","layouts","matched","matched-pending"])):(t(),a("div",ze,p[4]||(p[4]=[e("span",{op50:""},"No routes matched",-1)])))])]),_:1}),_(T,{icon:"carbon-tree-view-alt",text:"All Routes",description:`${c(w).length} routes registered in your application`,padding:"pr5"},{default:$(()=>[_(D,{pages:c(w),layouts:c(f)||[],matched:c(v).matched,"matched-pending":h.value,onNavigate:n},null,8,["pages","layouts","matched","matched-pending"])]),_:1},8,["description"]),y.value.length?(t(),k(T,{key:0,icon:"carbon:ibm-watson-studio",text:"Middleware",description:`${y.value.length} middleware registered in your application`,padding:"px13"},{default:$(()=>[e("table",Ue,[p[5]||(p[5]=e("thead",{border:"b base","h-7":""},[e("tr",null,[e("th",{"text-left":""}," Name "),e("th",{"text-left":""}," Path ")])],-1)),(t(!0),a(g,null,I(y.value,V=>(t(),a("tr",{key:V.path,"h-7":""},[e("td",null,[e("span",Ke,C(V.name),1),V.global?(t(),k(U,{key:0,n:"green",title:"Registered at runtime as a global component",textContent:"global"})):N("",!0)]),e("td",null,[_(K,{filepath:V.path},null,8,["filepath"])])]))),128))])]),_:1},8,["description"])):N("",!0)])):(t(),k(O,{key:1,icon:"carbon-tree-view-alt",name:"wizard-pages",title:"Nuxt Routing",description:`Create ${b.value} to enable routing`,actions:[{label:"Learn more",src:"https://nuxt.com/docs/getting-started/routing",attrs:{n:"primary"}},{label:"Enable Routing",async handle(){return("rpc"in u?u.rpc:c(oe)).runWizard(await("ensureDevAuthToken"in u?u.ensureDevAuthToken:c(ne))(),"enablePages")}}]},null,8,["description","actions"])),_(q,null,{default:$(()=>[_(W)]),_:1})],64)}}});export{ot as default};
