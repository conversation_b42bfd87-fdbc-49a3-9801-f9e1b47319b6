import{_ as j}from"./ncheckbox.vue-nc9ppn5r.js";import{G as O,_ as R,v as T,I as A}from"./czf9xkmw.js";import{_ as E}from"./nselect.vue-heccp04i.js";import{p as L,q as N,x as q,J as u,a3 as s,U as b,W as m,F as c,ag as h,u as r,a5 as y,S as C,V as p,a4 as V,ab as S,Z as v,aa as z}from"./vendor/json-editor-vue-m9gzt21j.js";const G={p4:"",flex:"~ col gap-4"},J={key:1,ml2:"",flex:""},K=["value"],M={key:0,flex:"","gap-4":""},X=L({__name:"ServerRouteInputs",props:{modelValue:{},keys:{default:()=>[]},default:{default:()=>({})},disabled:{type:Boolean,default:!1}},setup(U,{emit:w}){const g=U,d=O(g,"modelValue",w,{passive:!0}),$=N(()=>[...g.keys,"active","key","value","type"]),B=N(()=>{const l={};for(const e of $.value)l[e]=g.default[e]||"";return l}),I=["string","number","boolean","file","date","time","datetime-local"];function D(l,e){const t=e.target;if(t.files&&t.files[0]){const i=t.files[0],f=new FileReader;f.readAsDataURL(i),f.onload=()=>{d.value[l].value=f.result}}}return q(()=>d,l=>{l.value.forEach(e=>{if(e.type==="number"&&typeof e.value!="number"){const t=Number.parseFloat(e.value);e.value=Number.isNaN(t)?0:t}else e.type==="boolean"&&typeof e.value!="boolean"?e.value=!0:e.type==="file"&&typeof e.value!="object"?e.value="":e.type==="date"&&typeof e.value=="string"&&!e.value.match(/^\d{4}-\d{2}-\d{2}$/)?e.value=new Date().toISOString().slice(0,10):e.type==="time"&&typeof e.value=="string"&&!e.value.match(/^\d{2}:\d{2}$/)?e.value=new Date().toISOString().slice(11,16):e.type==="datetime-local"&&typeof e.value=="string"&&!e.value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/)?e.value=new Date().toISOString().slice(0,16):e.type==="string"&&(e.value=e.value.toString())})},{deep:!0,immediate:!0,flush:"sync"}),(l,e)=>{const t=j,i=T,f=E,F=A,_=R;return s(),u("div",G,[(s(!0),u(c,null,h(r(d),(a,k)=>(s(),u("div",{key:k,flex:"~ gap-2","justify-around":""},[m(l.$slots,"input",{item:a}),(s(!0),u(c,null,h($.value,n=>(s(),u(c,{key:n},[a.type!==null&&n==="active"?(s(),p(t,{key:0,modelValue:a[n],"onUpdate:modelValue":o=>a[n]=o,n:"sm primary",disabled:l.disabled},null,8,["modelValue","onUpdate:modelValue","disabled"])):b("",!0),a.type!==null&&n==="key"?(s(),p(i,{key:1,modelValue:a[n],"onUpdate:modelValue":o=>a[n]=o,placeholder:n,"flex-1":"","font-mono":"",n:"sm primary",disabled:l.disabled,class:v(l.disabled?"op50":"")},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","class"])):n==="value"?(s(),u(c,{key:2},[a.type==="file"?(s(),p(i,{key:0,type:"file",disabled:l.disabled,class:v(l.disabled?"op75":""),onChange:o=>D(k,o)},null,8,["disabled","class","onChange"])):a.type==="boolean"?(s(),u("div",J,[y(t,{modelValue:a.value,"onUpdate:modelValue":o=>a.value=o,placeholder:"Value",n:"green lg",disabled:l.disabled},null,8,["modelValue","onUpdate:modelValue","disabled"])])):(s(),p(i,{key:2,modelValue:a.value,"onUpdate:modelValue":o=>a.value=o,type:a.type,placeholder:"Value","flex-1":"","font-mono":"",n:"sm primary",disabled:l.disabled,class:v(l.disabled?"op75":"")},null,8,["modelValue","onUpdate:modelValue","type","disabled","class"]))],64)):n==="type"?(s(),p(f,{key:3,modelValue:a.type,"onUpdate:modelValue":o=>a.type=o,n:"sm green",class:v(l.disabled?"op75":""),disabled:l.disabled},{default:V(()=>[(s(),u(c,null,h(I,o=>C("option",{key:o,value:o},z(o),9,K)),64))]),_:2},1032,["modelValue","onUpdate:modelValue","class","disabled"])):b("",!0)],64))),128)),m(l.$slots,"input-actions",{},()=>[y(_,{n:"red",disabled:l.disabled,class:v(l.disabled?"op0!":""),onClick:n=>r(d).splice(k,1)},{default:V(()=>[y(F,{icon:"carbon:trash-can"})]),_:2},1032,["disabled","class","onClick"])])]))),128)),l.disabled?b("",!0):(s(),u("div",M,[m(l.$slots,"actions",{params:r(d)},()=>[y(_,{icon:"carbon-add",n:"sm primary",my1:"","px-3":"",onClick:e[0]||(e[0]=a=>r(d).push({...B.value}))},{default:V(()=>e[2]||(e[2]=[S(" Add ")])),_:1,__:[2]}),e[4]||(e[4]=C("div",{"flex-auto":""},null,-1)),r(d).length?(s(),p(_,{key:0,icon:"carbon-trash-can",n:"sm red",my1:"","px-3":"",onClick:e[1]||(e[1]=a=>r(d).splice(0,r(d).length))},{default:V(()=>e[3]||(e[3]=[S(" Remove All ")])),_:1,__:[3]})):b("",!0)])])),m(l.$slots,"default")])}}});export{X as _};
