import{_ as j}from"./nbadge-bu0b8pjx.js";import{_ as F}from"./ncode-block.vue-ctkw4rc7.js";import{_ as W}from"./ncheckbox.vue-nc9ppn5r.js";import{a6 as q,ah as A,ai as L,aj as E,Z as G,Q as J,q as Q,_ as Y,M as Z,ak as z,A as H,Y as K,W as O,a3 as X,al as ee}from"./czf9xkmw.js";import{p as te,k as P,J as _,a3 as p,F as $,W as ae,a5 as o,U as w,u as s,aa as M,V as ne,a4 as u,S as v,ab as c,q as oe}from"./vendor/json-editor-vue-m9gzt21j.js";const se={key:0},le={p4:"",flex:"~ col gap-1"},re={class:"text-lg font-medium leading-6"},ue={flex:"~ gap-3",mt2:"","justify-end":""},_e=te({__name:"NpmVersionCheck",props:{packageName:{},options:{},showVersion:{type:Boolean,default:!0}},setup(l){const a=l,t=q(),{info:n,update:i,state:g,processId:y,restart:m}=A(a.packageName,a.options),k=P(!0),N=P(!0),U=L(),x=E(),D=G();async function C(){const r=await i(async e=>x.start(e));Z("npm:update",{packageName:a.packageName,oldVersion:n.value?.current}),r&&N.value&&U.value.push({id:r,message:`${a.packageName} has been updated. Do you want to restart the Nuxt server now?`}),r&&k.value&&(D.value=r,t.push("/modules/terminals"))}return(r,e)=>{const b=j,S=F,h=W,T=Q,V=Y,I=J;return p(),_($,null,[ae(r.$slots,"default",{id:s(y),info:s(n),update:C,state:s(g),restart:s(m)},()=>[s(n)&&r.showVersion?(p(),_("code",se,M(`v${s(n).current}`),1)):w("",!0),s(n)?.latest?(p(),_($,{key:1},[s(n).needsUpdate?(p(),_("button",{key:0,onClick:e[0]||(e[0]=f=>C())},[o(b,{n:"green",title:"updates available",textContent:"updates available"})])):(p(),ne(b,{key:1,n:"gray",title:"latest",textContent:"latest"}))],64)):w("",!0)]),o(s(x),null,{default:u(({resolve:f,args:R})=>[o(I,{"model-value":!0,onClose:d=>f(!1)},{default:u(()=>[v("div",le,[v("h3",re," Update "+M(a.packageName)+"? ",1),e[8]||(e[8]=v("p",{op50:""}," The following command will be executed in your terminal: ",-1)),o(S,{code:R[0],lang:"bash",my3:"",px4:"",py2:"",border:"~ base rounded",lines:!1},null,8,["code"]),o(h,{modelValue:k.value,"onUpdate:modelValue":e[1]||(e[1]=d=>k.value=d),n:"primary"},{default:u(()=>e[3]||(e[3]=[c(" Navigate to terminal ")])),_:1,__:[3]},8,["modelValue"]),o(h,{modelValue:N.value,"onUpdate:modelValue":e[2]||(e[2]=d=>N.value=d),n:"primary"},{default:u(()=>e[4]||(e[4]=[c(" Restart Nuxt server after update ")])),_:1,__:[4]},8,["modelValue"]),v("div",ue,[o(T,{n:"sm amber","flex-auto":"",icon:"i-carbon-data-backup"},{default:u(()=>e[5]||(e[5]=[c(" Please make sure to backup your project first. ")])),_:1,__:[5]}),o(V,{onClick:d=>f(!1)},{default:u(()=>e[6]||(e[6]=[c(" Cancel ")])),_:2,__:[6]},1032,["onClick"]),o(V,{n:"solid primary",onClick:d=>f(!0)},{default:u(()=>e[7]||(e[7]=[c(" Update ")])),_:2,__:[7]},1032,["onClick"])])])]),_:2},1032,["onClose"])]),_:1})],64)}}}),B=["pages","meta","components","imports","nuxt-config-schema","@nuxt/devtools","@nuxt/telemetry"];function ie(){return ee("getModulesList",async()=>(await $fetch("https://api.nuxt.com/modules?version=3")).modules.filter(a=>!B.includes(a.npm)&&a.compatibility.nuxt.includes(">=3")))}function ve(){return z("installed-modules",()=>{const l=H(),a=ie();return oe(()=>(l.value?._installedModules||[]).map(t=>{if(!t.entryPath)return;const n=!!(t.entryPath&&K(t.entryPath)),i=t.meta?.name?t.meta?.name:t.entryPath?n?O(t.entryPath):l.value?.rootDir?X(t.entryPath,l.value?.rootDir).path:void 0:void 0,g=!!l.value?.modules?.includes(i),y=a.value?.find(m=>m.npm===i)||a.value?.find(m=>m.name===i);return{name:i,isPackageModule:n,isUninstallable:g,info:y,...t}}).filter(t=>t&&(!t.name||!B.includes(t.name))))})}export{_e as _,ie as a,ve as u};
