import{_ as S}from"./duration-display.vue-mr4ha0rw.js";import{g as T}from"./color-mt9xuhgy.js";import{p as $,k as H,q as w,J as h,a3 as u,S as o,ab as l,Z as p,F as y,ag as F,aa as m,X as V,u as k,a5 as _,V as C,U as N,a4 as f}from"./vendor/json-editor-vue-m9gzt21j.js";import{_ as B}from"./nsection-block-m4vpsvnn.js";import{_ as D}from"./help-fab.vue-b6h1gmzk.js";import{s as P,f as E}from"./czf9xkmw.js";import"./nicon-title.vue-ejocqf9t.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";const O={"w-full":""},j={border:"b base"},L={"select-none":"","ws-nowrap":"",p1:"","text-right":"","font-bold":""},z={colspan:"2",p1:"",pl5:"","text-left":"","font-bold":""},A={"ws-nowrap":"",p1:"","text-center":"","font-bold":""},U={"ws-nowrap":"",p1:"","text-center":"","font-bold":""},Y={"ws-nowrap":"",p1:"","text-right":"","font-bold":""},q={"w-0":"","ws-nowrap":"","text-center":"","text-sm":"",op25:""},J={"text-sm":""},R={"ws-nowrap":""},X={"text-sm":""},Z={"w-0":"","text-center":"","text-sm":""},G={"w-0":"","text-center":"","text-sm":""},K={"w-0":"","text-right":"","text-sm":""},M=$({__name:"HooksTable",props:{hooks:{}},setup(x){const a=x,r=H("duration"),n=H("asc"),d={duration:(e,t)=>(t.duration??Number.POSITIVE_INFINITY)-(e.duration??Number.POSITIVE_INFINITY),name:(e,t)=>e.name.localeCompare(t.name),listener:(e,t)=>t.listeners-e.listeners,start:(e,t)=>t.start-e.start,executions:(e,t)=>t.executions.length-e.executions.length},g=w(()=>a.hooks.map(e=>e.start).sort((e,t)=>e-t)),b=w(()=>{const e=d[r.value],t=[...a.hooks].sort(e);return n.value==="desc"&&t.reverse(),t});function c(e){const t=e.split(":");return t.length===1?"":`${t[0]}:`}function v(e){const t=e.split(":");return t.length===1?e:t.slice(1).join(":")}function i(e){r.value===e?n.value=n.value==="asc"?"desc":"asc":r.value=e}return(e,t)=>{const I=S;return u(),h("table",O,[o("thead",j,[o("tr",null,[o("th",L,[o("button",{onClick:t[0]||(t[0]=s=>i("start"))},[t[5]||(t[5]=l(" Order ")),o("div",{"ml--1":"","text-xs":"",class:p([r.value==="start"?"op50":"op0",n.value==="asc"?"carbon-arrow-down":"carbon-arrow-up"])},null,2)])]),o("th",z,[o("button",{onClick:t[1]||(t[1]=s=>i("name"))},[t[6]||(t[6]=l(" Hook name ")),o("div",{"ml--1":"","text-xs":"",class:p([r.value==="name"?"op50":"op0",n.value==="asc"?"carbon-arrow-down":"carbon-arrow-up"])},null,2)])]),o("th",A,[o("button",{onClick:t[2]||(t[2]=s=>i("listener"))},[t[7]||(t[7]=l(" Listeners ")),o("div",{"ml--1":"","text-xs":"",class:p([r.value==="listener"?"op50":"op0",n.value==="asc"?"carbon-arrow-down":"carbon-arrow-up"])},null,2)])]),o("th",U,[o("button",{onClick:t[3]||(t[3]=s=>i("executions"))},[t[8]||(t[8]=l(" Executions ")),o("div",{"ml--1":"","text-xs":"",class:p([r.value==="executions"?"op50":"op0",n.value==="asc"?"carbon-arrow-down":"carbon-arrow-up"])},null,2)])]),o("th",Y,[o("button",{onClick:t[4]||(t[4]=s=>i("duration"))},[t[9]||(t[9]=l(" Duration ")),o("div",{"ml--1":"","text-xs":"",class:p([r.value==="duration"?"op50":"op0",n.value==="asc"?"carbon-arrow-down":"carbon-arrow-up"])},null,2)])])])]),o("tbody",null,[(u(!0),h(y,null,F(b.value,s=>(u(),h("tr",{key:s.name,border:"b dashed transparent hover:base"},[o("td",q,m(g.value.indexOf(s.start)),1),o("td",{"w-0":"","ws-nowrap":"","text-right":"",style:V({color:("getHslColorFromStringHash"in e?e.getHslColorFromStringHash:k(T))(c(s.name))})},[o("code",J,m(c(s.name)),1)],4),o("td",R,[o("code",X,m(v(s.name)),1)]),o("td",Z,m(s.listeners),1),o("td",G,m(s.executions.length+1),1),o("td",K,[_(I,{duration:s.duration},null,8,["duration"])])]))),128))])])}}}),Q={class:"markdown-body"},W={__name:"hooks",setup(x,{expose:a}){return a({frontmatter:{}}),(n,d)=>(u(),h("div",Q,d[0]||(d[0]=[o("h1",null,"Hooks",-1),o("p",null,[l("Hooks are an advanced API which can be used for module development or other advanced use cases. The hooking API can be used to expand almost every aspect of Nuxt. Under the hood it is powered by "),o("a",{href:"https://github.com/unjs/hookable",target:"_blank",rel:"noopener"},"unjs/hookable"),l(".")],-1),o("p",null,[o("a",{href:"https://nuxt.com/docs/guide/going-further/hooks",target:"_blank",rel:"noopener"},"Learn more about hooks"),l(".")],-1)])))}},ut=$({__name:"hooks",setup(x){const a=P(),r=E(),n=w(()=>r.value?.metrics.clientHooks());return(d,g)=>{const b=M,c=B,v=W,i=D;return u(),h(y,null,[o("div",null,[n.value?.length?(u(),C(c,{key:0,icon:"carbon-ibm-cloud-direct-link-1-dedicated-hosting",text:"Client Hooks",description:`Total hooks: ${n.value.length}`,padding:"pl4 pr6"},{default:f(()=>[_(b,{hooks:n.value},null,8,["hooks"])]),_:1},8,["description"])):N("",!0),k(a)?.length?(u(),C(c,{key:1,icon:"carbon-ibm-cloud-direct-link-2-dedicated",text:"Server Hooks",description:`Total hooks: ${k(a)?.length}`,padding:"pl4 pr6"},{default:f(()=>[_(b,{hooks:k(a)},null,8,["hooks"])]),_:1},8,["description"])):N("",!0)]),_(i,null,{default:f(()=>[_(v)]),_:1})],64)}}});export{ut as default};
