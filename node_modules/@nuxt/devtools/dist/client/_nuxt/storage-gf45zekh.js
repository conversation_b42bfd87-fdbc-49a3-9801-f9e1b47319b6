import{a4 as ae,a5 as ne,a6 as oe,a7 as P,H as se,r as f,E as k,a8 as le,v as re,I as ue,_ as ie,i as de,n as pe,a9 as T,aa as me}from"./czf9xkmw.js";import{_ as ve}from"./nselect.vue-heccp04i.js";import{_ as fe}from"./nlink.vue-d4id0o0s.js";import{_ as ce}from"./filepath-item.vue-dx8apiq4.js";import{p as G,k as M,au as F,M as _e,x as ye,w as ge,q as ke,V as m,u as c,a3 as s,a4 as r,J as u,S as l,ac as xe,U as R,Y as U,ab as i,a5 as d,aa as x,Z as z,av as be,a6 as we,at as Ce,al as Ke,ag as A,F as b,j as Se}from"./vendor/json-editor-vue-m9gzt21j.js";import"./vendor/unocss-oyl7opas.js";import"./vendor/shiki-imfwxqoq.js";const Ve={class:"h-[48px] flex items-center justify-between gap1 px-3"},Ne={class:"w-full text-sm"},$e=["value"],Ie=["onClick"],he={key:0,"h-full":"","of-hidden":"",flex:"~ col"},je={border:"b base",class:"h-[49px] flex flex-none items-center justify-between px-4 text-sm"},De={class:"flex items-center gap-4"},Me={key:1},Ue=["onKeyup"],Ae={key:0,op50:""},qe={key:1},Be={"font-bold":""},Le={"text-sm":""},Ee=G({__name:"StorageDetails",async setup(H){let p,y;const w=ae(),C=ne(),K=oe(),S=M(""),g=M(""),o=P("storage:current",""),t=M(),_=P("storage:file:state",""),{data:V}=([p,y]=F(()=>T("storageMounts",()=>f.getStorageMounts())),p=await p,y(),p),{data:N,refresh:q}=([p,y]=F(async()=>T("storageKeys",async()=>o.value?await f.getStorageKeys(o.value):[])),p=await p,y(),p),J=C.hook("storage:key:update",async(a,e)=>{if(!(!o.value||a.split(":")[0]!==o.value)&&(await q(),_.value===a)){if(e==="remove")return K.replace({query:{storage:o.value}});await I(_.value)}});_e(J),ye(o,()=>{q(),_.value=""}),ge(async()=>{if(!_.value){t.value=null;return}I(_.value)}),se("keydown",a=>{a.key==="s"&&(a.ctrlKey||a.metaKey)&&(h(),a.preventDefault())});function $(a){return a.replace(`${o.value}:`,"")}const O=ke(()=>N.value?N.value.filter(a=>a.includes(S.value)):[]);async function I(a){const e=await f.getStorageItem(await k(),a);t.value={key:a,updatedKey:$(a),editingKey:!1,content:e,updatedContent:e}}async function W(){if(!g.value||!o.value)return;const a=`${o.value}:${g.value}`;N.value?.includes(a)||await f.setStorageItem(await k(),a,""),K.replace({query:{storage:o.value,key:a}}),g.value=""}async function h(){t.value&&(await f.setStorageItem(await k(),t.value.key,t.value.updatedContent),await I(t.value.key))}async function Y(){!t.value||!o.value||(await f.removeStorageItem(await k(),t.value.key),t.value=null)}async function Z(){if(!t.value||!o.value)return;const a=`${o.value}:${t.value.updatedKey}`,e=await k();await f.setStorageItem(e,a,t.value.updatedContent),await f.removeStorageItem(e,t.value.key),K.replace({query:{storage:o.value,key:a}})}return(a,e)=>{const j=ie,Q=ve,D=re,X=ue,B=fe,L=pe,E=de,ee=le,te=ce;return c(o)?(s(),m(ee,{key:0,"storage-key":"tab-storage"},{left:r(()=>[l("div",Ve,[d(j,{icon:"carbon-chevron-left","ml--1":"",border:!1,onClick:e[0]||(e[0]=n=>o.value="")}),l("div",Ne,[d(Q,{modelValue:c(o),"onUpdate:modelValue":e[1]||(e[1]=n=>Se(o)?o.value=n:null),n:"primary",icon:"carbon-data-base"},{default:r(()=>[(s(!0),u(b,null,A(c(V),(n,v)=>(s(),u("option",{key:v,value:v},x(v),9,$e))),128))]),_:1},8,["modelValue"])])]),d(D,{modelValue:S.value,"onUpdate:modelValue":e[2]||(e[2]=n=>S.value=n),icon:"carbon-search",placeholder:"Search...",n:"primary sm",border:"y x-none base! rounded-0",class:"w-full py2 ring-0!"},null,8,["modelValue"]),(s(!0),u(b,null,A(O.value,n=>(s(),u(b,{key:n},[l("button",{block:"","w-full":"",truncate:"",px2:"",py1:"","text-start":"","text-sm":"","font-mono":"",class:z(n===t.value?.key?"text-primary n-bg-active":"text-secondary hover:n-bg-hover"),onClick:v=>_.value=n},x($(n)),11,Ie),e[8]||(e[8]=l("div",{"x-divider":""},null,-1))],64))),128)),d(D,{modelValue:g.value,"onUpdate:modelValue":e[3]||(e[3]=n=>g.value=n),icon:"carbon-add",placeholder:"key",n:"sm",border:"t-none x-none base! rounded-0",class:"w-full py2 font-mono ring-0!",onKeyup:U(W,["enter"])},null,8,["modelValue"])]),right:r(()=>[t.value?.key?(s(),u("div",he,[l("div",je,[l("div",De,[t.value.editingKey?(s(),m(D,{key:0,modelValue:t.value.updatedKey,"onUpdate:modelValue":e[4]||(e[4]=n=>t.value.updatedKey=n),onKeyup:U(Z,["enter"])},null,8,["modelValue"])):(s(),u("code",Me,[i(x($(t.value.key))+" ",1),d(X,{icon:"carbon-edit",class:"cursor-pointer op50 hover:op100",onClick:e[5]||(e[5]=n=>t.value.editingKey=!0)})])),t.value.editingKey?R("",!0):(s(),m(j,{key:2,n:"green xs",disabled:t.value.content===t.value.updatedContent,class:z({"border-green":t.value.content!==t.value.updatedContent}),onClick:h},{default:r(()=>e[9]||(e[9]=[i(" Save ")])),_:1,__:[9]},8,["disabled","class"]))]),l("div",null,[d(j,{n:"red xs",onClick:Y},{default:r(()=>e[10]||(e[10]=[i(" Delete ")])),_:1,__:[10]})])]),typeof t.value.content=="object"?(s(),m(c(be),we({key:0,modelValue:t.value.updatedContent,"onUpdate:modelValue":e[6]||(e[6]=n=>t.value.updatedContent=n),class:[[c(w)==="dark"?"jse-theme-dark":"light"],"json-editor-vue h-full of-auto text-sm outline-none"]},a.$attrs,{mode:"text","navigation-bar":!1,indentation:2,"tab-size":2}),null,16,["modelValue","class"])):xe((s(),u("textarea",{key:1,"onUpdate:modelValue":e[7]||(e[7]=n=>t.value.updatedContent=n),placeholder:"Item value...",class:"h-full of-auto p-4 text-sm font-mono outline-none",onKeyup:U(Ke(h,["ctrl"]),["enter"])},null,40,Ue)),[[Ce,t.value.updatedContent]])])):(s(),m(E,{key:1},{default:r(()=>[d(L,{px6:"",py4:""},{default:r(()=>[e[12]||(e[12]=i(" Select one key to start.")),e[13]||(e[13]=l("br",null,null,-1)),e[14]||(e[14]=i("Learn more about ")),d(B,{href:"https://nitro.unjs.io/guide/storage",n:"orange",target:"_blank"},{default:r(()=>e[11]||(e[11]=[i(" Nitro storage ")])),_:1,__:[11]})]),_:1,__:[12,13,14]})]),_:1}))]),_:1})):(s(),m(E,{key:1},{default:r(()=>[Object.keys(c(V)).length?(s(),u("p",Ae," Select one storage to start: ")):(s(),u("p",qe,[e[16]||(e[16]=i(" No custom storage defined in ")),e[17]||(e[17]=l("code",null,"nitro.storage",-1)),e[18]||(e[18]=i(".")),e[19]||(e[19]=l("br",null,null,-1)),e[20]||(e[20]=i(" Learn more about ")),d(B,{href:"https://nitro.unjs.io/guide/storage",n:"orange",target:"_blank"},{default:r(()=>e[15]||(e[15]=[i(" Nitro storage ")])),_:1,__:[15]})])),(s(!0),u(b,null,A(c(V),(n,v)=>(s(),m(L,{key:v,"min-w-80":"","cursor-pointer":"","p-4":"","text-left":"",hover:"border-green",onClick:Pe=>o.value=v},{default:r(()=>[l("span",Be,x(v),1),e[21]||(e[21]=l("br",null,null,-1)),l("span",Le,x(n.driver)+" driver",1),e[22]||(e[22]=l("br",null,null,-1)),n.base?(s(),m(te,{key:0,"text-xs":"",filepath:n.base},null,8,["filepath"])):R("",!0)]),_:2,__:[21,22]},1032,["onClick"]))),128))]),_:1}))}}}),Oe=G({__name:"storage",setup(H){return(p,y)=>{const w=Ee,C=me;return s(),m(C,null,{default:r(()=>[d(w)]),_:1})}}});export{Oe as default};
