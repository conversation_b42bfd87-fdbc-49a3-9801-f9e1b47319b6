{"name": "@nuxt/devtools", "type": "module", "version": "2.6.2", "description": "The Nuxt DevTools gives you insights and transparency about your Nuxt App.", "license": "MIT", "homepage": "https://devtools.nuxt.com", "repository": {"type": "git", "url": "git+https://github.com/nuxt/devtools.git", "directory": "packages/devtools"}, "exports": {".": {"types": "./dist/module.d.mts", "import": "./dist/module.mjs"}, "./webcomponents": {"types": "./dist/webcomponents/index.d.mts", "import": "./dist/webcomponents/index.mjs"}, "./types": {"types": "./dist/types.d.mts"}, "./*": "./*"}, "types": "./dist/module.d.mts", "bin": "./cli.mjs", "files": ["*.cjs", "*.d.ts", "*.mjs", "dist"], "peerDependencies": {"vite": ">=6.0"}, "dependencies": {"@nuxt/kit": "^3.17.6", "@vue/devtools-core": "^7.7.7", "@vue/devtools-kit": "^7.7.7", "birpc": "^2.4.0", "consola": "^3.4.2", "destr": "^2.0.5", "error-stack-parser-es": "^1.0.5", "execa": "^8.0.1", "fast-npm-meta": "^0.4.4", "get-port-please": "^3.1.2", "hookable": "^5.5.3", "image-meta": "^0.2.1", "is-installed-globally": "^1.0.0", "launch-editor": "^2.10.0", "local-pkg": "^1.1.1", "magicast": "^0.3.5", "nypm": "^0.6.0", "ohash": "^2.0.11", "pathe": "^2.0.3", "perfect-debounce": "^1.0.0", "pkg-types": "^2.2.0", "semver": "^7.7.2", "simple-git": "^3.28.0", "sirv": "^3.0.1", "structured-clone-es": "^1.0.0", "tinyglobby": "^0.2.14", "vite-plugin-inspect": "^11.3.0", "vite-plugin-vue-tracer": "^1.0.0", "which": "^5.0.0", "ws": "^8.18.3", "@nuxt/devtools-wizard": "2.6.2", "@nuxt/devtools-kit": "2.6.2"}, "devDependencies": {"@antfu/utils": "^9.2.0", "@discoveryjs/cli": "^2.14.2", "@discoveryjs/discovery": "1.0.0-beta.92", "@iconify-json/bxl": "^1.2.2", "@iconify-json/carbon": "^1.2.10", "@iconify-json/logos": "^1.2.4", "@iconify-json/ph": "^1.2.2", "@iconify-json/ri": "^1.2.5", "@iconify-json/simple-icons": "^1.2.41", "@iconify-json/tabler": "^1.2.19", "@nuxt/test-utils": "^3.19.2", "@parcel/watcher": "^2.5.1", "@types/markdown-it-link-attributes": "^3.0.5", "@unhead/schema": "^2.0.11", "@unocss/nuxt": "^66.3.2", "@unocss/preset-icons": "^66.3.2", "@unocss/preset-uno": "^66.3.2", "@unocss/runtime": "^66.3.2", "@vitest/ui": "^3.2.4", "@vue/devtools-applet": "^7.7.7", "@vueuse/nuxt": "^13.4.0", "@xterm/addon-fit": "^0.10.0", "@xterm/xterm": "^5.5.0", "cronstrue": "^2.61.0", "exsolve": "^1.0.7", "floating-vue": "^5.2.2", "fuse.js": "^7.1.0", "json-editor-vue": "^0.18.1", "lightningcss": "^1.30.1", "markdown-it": "^14.1.0", "markdown-it-link-attributes": "^4.0.1", "my-ua-parser": "^2.0.4", "nitropack": "^2.11.13", "nuxt": "^3.17.6", "ofetch": "^1.4.1", "quicktype-core": "22.0.0", "scule": "^1.3.0", "shiki": "^3.7.0", "shiki-codegen": "^3.7.0", "theme-vitesse": "^0.8.3", "tsx": "^4.20.3", "unimport": "^5.1.0", "unocss": "^66.3.2", "unplugin-vue": "^6.2.0", "unplugin-vue-markdown": "^29.1.0", "vanilla-jsoneditor": "^3.6.1", "vis-data": "^7.1.9", "vis-network": "^9.1.13", "vue-tsc": "^2.2.10", "vue-virtual-scroller": "^2.0.0-beta.8", "@nuxt/devtools": "2.6.2"}, "scripts": {"build": "pnpm dev:prepare && pnpm build:module && pnpm build:discovery && pnpm build:client", "build:client": "nuxi generate client && cp -r client/.output/public/ dist/client/", "build:module": "nuxt-build-module build", "build:discovery": "npx discovery-build -c .discoveryrc.cjs -s -o client/public/discovery", "dev:discovery": "discovery -c .discoveryrc.cjs", "stub": "nuxt-build-module build --stub", "dev": "pnpm build:discovery && nuxi dev client", "dev:playground": "pnpm build && nuxi dev playground", "dev:prepare": "pnpm run stub && nuxi prepare client"}}