/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * 
 * @format
 */
// flowlint ambiguous-object-type:error
'use strict';

var FindGraphQLTags = require('../language/javascript/FindGraphQLTags');

var RelaySourceModuleParser = require('./RelaySourceModuleParser');

var JSModuleParser = RelaySourceModuleParser(FindGraphQLTags.find);
module.exports = JSModuleParser;