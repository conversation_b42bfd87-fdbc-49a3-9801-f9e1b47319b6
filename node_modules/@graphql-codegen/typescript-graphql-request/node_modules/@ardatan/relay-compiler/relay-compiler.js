/**
 * Relay v12.0.0
 */
module.exports=function(e){var t={};function n(r){if(t[r])return t[r].exports;var a=t[r]={i:r,l:!1,exports:{}};return e[r].call(a.exports,a,a.exports,n),a.l=!0,a.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)n.d(r,a,function(t){return e[t]}.bind(null,a));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=75)}([function(e,t){e.exports=require("@babel/runtime/helpers/interopRequireDefault")},function(e,t,n){"use strict";var r=n(0)(n(3)),a=n(7).GraphQLError;function i(e,t,n){var r=e;if(null!=t){var i=s(t);r=0===i.length?e:[e].concat(i).join("\n\n")+"\n"}return new a(r,null!=n?n:[])}function o(e,t,n){var r=e;if(null!=t){var i=s(t);r=0===i.length?e:[e].concat(i).join("\n\n")+"\n"}var o=new a("Internal Error: ".concat(r),null!=n?n:[]);return new Error(o.message)}function s(e){var t,n=[],a=(0,r.default)(e);try{for(a.s();!(t=a.n()).done;){for(var i=t.value,s=i;"Derived"===s.kind;)s=s.source;switch(s.kind){case"Source":var u=s===i?"Source: ":"Source (derived): ";n.push(u+l(s.source,c(s.source,s.start)));break;case"Generated":n.push("Source: (generated)");break;case"Unknown":n.push("Source: (unknown)");break;default:throw o("CompilerError: cannot print location '".concat(String(s),"'."))}}}catch(e){a.e(e)}finally{a.f()}return n}function l(e,t){var n=e.locationOffset.column-1,a=u(n)+e.body,i=t.line-1,o=e.locationOffset.line-1,s=t.line+o,l=1===t.line?n:0,c=t.column+l,f=a.split(/\r\n|[\n\r]/g);return"".concat(e.name," (").concat(s,":").concat(c,")\n")+function(e){var t,n=e.filter((function(e){e[0];return void 0!==e[1]})),a=0,i=(0,r.default)(n);try{for(i.s();!(t=i.n()).done;){var o=t.value[0];a=Math.max(a,o.length)}}catch(e){i.e(e)}finally{i.f()}return n.map((function(e){var t,n=e[0],r=e[1];return u(a-(t=n).length)+t+r})).join("\n")}([["".concat(s-1,": "),f[i-1]],["".concat(s,": "),f[i]],["",u(c-1)+"^"],["".concat(s+1,": "),f[i+1]]])}function u(e){return Array(e+1).join(" ")}function c(e,t){for(var n,r=/\r\n|[\n\r]/g,a=1,i=t+1;(n=r.exec(e.body))&&n.index<t;)a+=1,i=t+1-(n.index+n[0].length);return{line:a,column:i}}e.exports={createCompilerError:o,createNonRecoverableUserError:function(e,t,n){var r=e;if(null!=t){var i=s(t);r=0===i.length?e:[e].concat(i).join("\n\n")+"\n"}var o=new a(r,null!=n?n:[]);return new Error(o.message)},createUserError:i,eachWithCombinedError:function(e,t){var n,o=[],s=(0,r.default)(e);try{for(s.s();!(n=s.n()).done;){var l=n.value;try{t(l)}catch(e){if(!(e instanceof a))throw e;o.push(e)}}}catch(e){s.e(e)}finally{s.f()}if(o.length>0){if(1===o.length)throw i(String(o[0]).split("\n").map((function(e,t){return 0===t?"- ".concat(e):"  ".concat(e)})).join("\n"));throw i("Encountered ".concat(o.length," errors:\n")+o.map((function(e){return String(e).split("\n").map((function(e,t){return 0===t?"- ".concat(e):"  ".concat(e)})).join("\n")})).join("\n"))}}}},function(e,t){e.exports=require("@babel/runtime/helpers/objectSpread2")},function(e,t){e.exports=require("@babel/runtime/helpers/createForOfIteratorHelper")},function(e,t,n){"use strict";var r=n(0)(n(2)),a=n(5),i=n(1).eachWithCombinedError;var o=function(){function e(e,t){this._context=e,this._states=[],this._visitor=t}var t=e.prototype;return t.getContext=function(){return this._context},t.visit=function(e,t){this._states.push(t);var n=this._visit(e);return this._states.pop(),n},t.traverse=function(e,t){this._states.push(t);var n=this._traverse(e);return this._states.pop(),n},t._visit=function(e){var t=this._visitor[e.kind];if(t){var n=this._getState();return t.call(this,e,n)}return this._traverse(e)},t._traverse=function(e){var t;switch(e.kind){case"Argument":t=this._traverseChildren(e,null,["value"]);break;case"Literal":case"LocalArgumentDefinition":case"RootArgumentDefinition":case"Variable":t=e;break;case"Defer":t=this._traverseChildren(e,["selections"],["if"]);break;case"Stream":t=this._traverseChildren(e,["selections"],["if","initialCount"]);break;case"ClientExtension":t=this._traverseChildren(e,["selections"]);break;case"Directive":t=this._traverseChildren(e,["args"]);break;case"ModuleImport":(t=this._traverseChildren(e,["selections"])).selections.length||(t=null);break;case"FragmentSpread":case"ScalarField":t=this._traverseChildren(e,["args","directives"]);break;case"InlineDataFragmentSpread":t=this._traverseChildren(e,["selections"]);break;case"LinkedField":(t=this._traverseChildren(e,["args","directives","selections"])).selections.length||(t=null);break;case"ListValue":t=this._traverseChildren(e,["items"]);break;case"ObjectFieldValue":t=this._traverseChildren(e,null,["value"]);break;case"ObjectValue":t=this._traverseChildren(e,["fields"]);break;case"Condition":(t=this._traverseChildren(e,["directives","selections"],["condition"])).selections.length||(t=null);break;case"InlineFragment":(t=this._traverseChildren(e,["directives","selections"])).selections.length||(t=null);break;case"Fragment":case"Root":t=this._traverseChildren(e,["argumentDefinitions","directives","selections"]);break;case"Request":t=this._traverseChildren(e,null,["fragment","root"]);break;case"SplitOperation":t=this._traverseChildren(e,["selections"]);break;default:a(!1,"IRTransformer: Unknown kind `%s`.",e.kind)}return t},t._traverseChildren=function(e,t,n){var i,o=this;return t&&t.forEach((function(t){var n=e[t];if(n){Array.isArray(n)||a(!1,"IRTransformer: Expected data for `%s` to be an array, got `%s`.",t,n);var s=o._map(n);(i||s!==n)&&((i=i||(0,r.default)({},e))[t]=s)}})),n&&n.forEach((function(t){var n=e[t];if(n){var a=o._visit(n);(i||a!==n)&&((i=i||(0,r.default)({},e))[t]=a)}})),i||e},t._map=function(e){var t,n=this;return e.forEach((function(r,a){var i=n._visit(r);(t||i!==r)&&(t=t||e.slice(0,a),i&&t.push(i))})),t||e},t._getState=function(){return this._states.length||a(!1,"IRTransformer: Expected a current state to be set but found none. This is usually the result of mismatched number of pushState()/popState() calls."),this._states[this._states.length-1]},e}();e.exports={transform:function(e,t,n){var r=new o(e,t);return e.withMutations((function(t){var a=t;return i(e.documents(),(function(e){var t;if(void 0===n)t=r.visit(e,void 0);else{var i=n(e);null!=i&&(t=r.visit(e,i))}t?t!==e&&(a=a.replace(t)):a=a.remove(e.name)})),a}))}}},function(e,t){e.exports=require("invariant")},function(e,t){e.exports=require("@babel/runtime/helpers/toConsumableArray")},function(e,t){e.exports=require("graphql")},function(e,t,n){"use strict";var r=n(9),a=n(5),i=!1,o=[{ph:"M",pid:0,tid:0,name:"process_name",args:{name:"relay-compiler"}},{ph:"M",pid:0,tid:0,name:"thread_name",args:{name:"relay-compiler"}}],s=[];function l(e,t){var n;if(!i)return e;var r=null!==(n=null!=t?t:e.displayName)&&void 0!==n?n:e.name,a=function(){var t=p(r);try{return e.apply(this,arguments)}finally{v(t)}};return a.displayName=r,a}function u(e,t){var n;if(!i)return e;var a=null!==(n=null!=t?t:e.displayName)&&void 0!==n?n:e.name,o=function(){var t=r((function*(){var t=p(a);try{return yield e.apply(this,arguments)}finally{v(t)}}));return function(){return t.apply(this,arguments)}}();return o.displayName=a,o}function c(e,t){var n;if(!i)return e;var a=null!==(n=null!=t?t:e.displayName)&&void 0!==n?n:e.name,o=function(){var t=r((function*(){var t=h(a);try{return yield e.apply(this,arguments)}finally{v(t)}}));return function(){return t.apply(this,arguments)}}();return o.displayName=a,o}var f=process.hrtime();function d(){var e=process.hrtime(f);return 0|1e6*e[0]+Math.round(e[1]/1e3)}function p(e){var t={ph:"B",name:e,pid:0,tid:0,ts:d()};return o.push(t),s.push(t),o.length-1}var m=0;function h(e){return o.push({ph:"b",name:e,cat:"wait",id:m++,pid:0,tid:0,ts:d()}),o.length-1}function v(e){var t=o[e];"b"!==t.ph?("B"!==t.ph&&a(!1,"Begin trace phase"),s.pop()!==t&&a(!1,"GraphQLCompilerProfiler: The profile trace %s ended before nested traces. If it is async, try using Profile.waitFor or Profile.profileWait.",t.name),t!==o[o.length-1]?o.push({ph:"E",name:t.name,pid:t.pid,tid:t.tid,ts:d()}):o[e]={ph:"X",name:t.name,pid:t.pid,tid:t.tid,ts:t.ts,dur:d()-t.ts}):o.push({ph:"e",cat:t.cat,name:t.name,id:t.id,pid:t.pid,tid:t.tid,ts:d()})}e.exports={enable:function(){i=!0},getTraces:function(){return o},run:function(e,t){return l(t,e)()},asyncContext:function(e,t){return u(t,e)()},waitFor:function(e,t){return c(t,e)()},instrument:l,instrumentAsyncContext:u,instrumentWait:c,start:p,startWait:h,end:v}},function(e,t){e.exports=require("@babel/runtime/helpers/asyncToGenerator")},function(e,t,n){"use strict";var r=n(1).createCompilerError;e.exports={generateIDField:function(e,t){var n=e.getFieldByName(t,"id");if(null==n)throw new r("Expected an 'id' field on type '".concat(e.getTypeString(t),"'."));return{kind:"ScalarField",alias:"id",args:[],directives:[],handles:null,loc:{kind:"Generated"},metadata:null,name:"id",type:e.assertScalarFieldType(e.getFieldType(n))}},isExecutableDefinitionAST:function(e){return"FragmentDefinition"===e.kind||"OperationDefinition"===e.kind},isSchemaDefinitionAST:function(e){return"SchemaDefinition"===e.kind||"ScalarTypeDefinition"===e.kind||"ObjectTypeDefinition"===e.kind||"InterfaceTypeDefinition"===e.kind||"UnionTypeDefinition"===e.kind||"EnumTypeDefinition"===e.kind||"InputObjectTypeDefinition"===e.kind||"DirectiveDefinition"===e.kind||"ScalarTypeExtension"===e.kind||"ObjectTypeExtension"===e.kind||"InterfaceTypeExtension"===e.kind||"UnionTypeExtension"===e.kind||"EnumTypeExtension"===e.kind||"InputObjectTypeExtension"===e.kind},getNullableBooleanInput:function(e){return e.assertInputType(e.expectBooleanType())},getNonNullBooleanInput:function(e){return e.assertInputType(e.getNonNullType(e.expectBooleanType()))},getNullableStringInput:function(e){return e.assertInputType(e.expectStringType())},getNonNullStringInput:function(e){return e.assertInputType(e.getNonNullType(e.expectStringType()))},getNullableIdInput:function(e){return e.assertInputType(e.expectIdType())},getNonNullIdInput:function(e){return e.assertInputType(e.getNonNullType(e.expectIdType()))}}},function(e,t){e.exports=require("path")},function(e,t){e.exports=require("relay-runtime")},function(e,t){e.exports=require("fs")},function(e,t){e.exports=require("crypto")},function(e,t){e.exports=require("nullthrows")},function(e,t,n){"use strict";e.exports=function(e){return n(14).createHash("md5").update(e,"utf8").digest("hex")}},function(e,t){e.exports=require("immutable")},function(e,t,n){"use strict";e.exports={getName:function(e){if(null==e.name)throw new Error("All fragments and operations have to have names in Relay");return e.name.value}}},function(e,t,n){"use strict";var r=n(8),a=n(5),i=n(1).createUserError,o=n(17).OrderedMap,s=function(){function e(e){this._isMutable=!1,this._documents=new o,this._withTransform=new WeakMap,this._schema=e}var t=e.prototype;return t.documents=function(){return this._documents.toArray()},t.forEachDocument=function(e){this._documents.forEach(e)},t.replace=function(e){return this._update(this._documents.update(e.name,(function(t){return t||a(!1,"CompilerContext: Expected to replace existing node %s, but one was not found in the context.",e.name),e})))},t.add=function(e){return this._update(this._documents.update(e.name,(function(t){return t&&a(!1,"CompilerContext: Duplicate document named `%s`. GraphQL fragments and roots must have unique names.",e.name),e})))},t.addAll=function(e){return this.withMutations((function(t){return e.reduce((function(e,t){return e.add(t)}),t)}))},t.applyTransforms=function(e,t){var n=this;return r.run("applyTransforms",(function(){return e.reduce((function(e,n){return e.applyTransform(n,t)}),n)}))},t.applyTransform=function(e,t){var n=this._withTransform.get(e);if(!n){var a=process.hrtime();n=r.instrument(e)(this);var i=process.hrtime(a),o=Math.round((1e9*i[0]+i[1])/1e6);t&&t.reportTime(e.name,o),this._withTransform.set(e,n)}return n},t.get=function(e){return this._documents.get(e)},t.getFragment=function(e,t){var n=this._documents.get(e);if(null==n)throw i("Cannot find fragment '".concat(e,"'."),null!=t?[t]:null);if("Fragment"!==n.kind)throw i("Cannot find fragment '".concat(e,"', a document with this name exists ")+"but is not a fragment.",[n.loc,t].filter(Boolean));return n},t.getRoot=function(e){var t=this._documents.get(e);if(null==t)throw i("Cannot find root '".concat(e,"'."));if("Root"!==t.kind)throw i("Cannot find root '".concat(e,"', a document with this name exists but ")+"is not a root.",[t.loc]);return t},t.remove=function(e){return this._update(this._documents.delete(e))},t.withMutations=function(e){var t=this._update(this._documents.asMutable());t._isMutable=!0;var n=e(t);return n._isMutable=!1,n._documents=n._documents.asImmutable(),this._documents===n._documents?this:n},t._update=function(t){var n=this._isMutable?this:new e(this.getSchema());return n._documents=t,n},t.getSchema=function(){return this._schema},e}();e.exports=s},function(e,t,n){"use strict";var r=n(7).visit,a={Argument:["value"],ClientExtension:["selections"],Condition:["condition","selections"],Defer:["selections","if"],Directive:["args"],Fragment:["argumentDefinitions","directives","selections"],FragmentSpread:["args","directives"],InlineDataFragmentSpread:["selections"],InlineFragment:["directives","selections"],LinkedField:["args","directives","selections"],Literal:[],LocalArgumentDefinition:[],ModuleImport:["selections"],Request:["fragment","root"],Root:["argumentDefinitions","directives","selections"],RootArgumentDefinition:[],ScalarField:["args","directives"],SplitOperation:["selections"],Stream:["selections","if","initialCount"],Variable:[]};e.exports={visit:function(e,t){return r(e,t,a)}}},function(e,t,n){"use strict";e.exports=function(e,t){for(var n=[],r=[],a=0;a<e.length;a++){var i=e[a];t(i)?n.push(i):r.push(i)}return[n,r]}},function(e,t,n){"use strict";e.exports=function(e){var t={};return e.forEach((function(e){"Literal"===e.value.kind&&(t[e.name]=e.value.value)})),t}},function(e,t,n){"use strict";var r=n(9),a=n(43),i=n(78);function o(e){return new Promise((function(t){return setTimeout(t,e)}))}var s=function(){function e(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this._client=new i.Client,this._attemptLimit=Math.max(Math.min(5,e),0)}e.isAvailable=function(){return new Promise((function(e){var t=a.spawn("watchman",["version"]);t.on("error",(function(){e(!1)})),t.on("close",(function(t){e(0===t)}))}))};var t=e.prototype;return t._command=function(){for(var e=this,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return new Promise((function(t,r){e._client.command(n,(function(e,n){e?r(e):t(n)}))}))},t.command=function(){var e=r((function*(){for(var e=0,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];for(;;)try{return e++,yield this._command.apply(this,n)}catch(t){if(e>this._attemptLimit)throw t;yield o(500*Math.pow(2,e)),this._client.end(),this._client=new i.Client}}));return function(){return e.apply(this,arguments)}}(),t.hasCapability=function(){var e=r((function*(e){return(yield this.command("list-capabilities")).capabilities.includes(e)}));return function(t){return e.apply(this,arguments)}}(),t.watchProject=function(){var e=r((function*(e){var t=yield this.command("watch-project",e);return"warning"in t&&console.error("Warning:",t.warning),{root:t.watch,relativePath:t.relative_path}}));return function(t){return e.apply(this,arguments)}}(),t.on=function(e,t){this._client.on(e,t)},t.end=function(){this._client.end()},e}();e.exports=s},function(e,t,n){"use strict";var r=n(1).createCompilerError;e.exports=function(e,t){if(!e.isAbstractType(t))throw r("Expected an abstract type");return"__is".concat(e.getTypeString(t))}},function(e,t,n){"use strict";var r=n(0),a=r(n(2)),i=r(n(3)),o=r(n(6)),s=n(8),l=n(89),u=n(21),c=n(49).DEFAULT_HANDLE_KEY,f=n(1),d=f.createCompilerError,p=f.createUserError,m=f.eachWithCombinedError,h=n(10).isExecutableDefinitionAST,v=n(57).getFieldDefinitionLegacy,y=n(7),g=y.parse,b=y.parseType,T=y.print,S=y.Source,w=new Set(["argumentDefinitions","uncheckedArguments_DEPRECATED","arguments"]);var _=function(){function e(e,t){var n=this;this._definitions=new Map,this._getFieldDefinition=v,this._schema=e;var r=new Set;if(t.forEach((function(e){if(h(e)){var t=N(e);if(n._definitions.has(t))return void r.add(t);n._definitions.set(t,e)}})),r.size)throw p("RelayParser: Encountered duplicate definitions for one or more documents: each document must have a unique name. Duplicated documents:\n"+Array.from(r,(function(e){return"- ".concat(e)})).join("\n"))}var t=e.prototype;return t.transform=function(){var e=this,t=[],n=new Map;return m(this._definitions,(function(t){var r=t[0],a=t[1],i=e._buildArgumentDefinitions(a);n.set(r,{definition:a,variableDefinitions:i})})),m(n.values(),(function(r){var a=r.definition,i=r.variableDefinitions,o=function(e,t,n,r,a){return new k(e,t,n,r,a).transform()}(e._schema,e._getFieldDefinition,n,a,i);t.push(o)})),t},t._buildArgumentDefinitions=function(e){switch(e.kind){case"OperationDefinition":return this._buildOperationArgumentDefinitions(e);case"FragmentDefinition":return this._buildFragmentArgumentDefinitions(e);default:throw d("Unexpected ast kind '".concat(e.kind,"'."),[e])}},t._buildFragmentArgumentDefinitions=function(e){var t=this,n=(e.directives||[]).filter((function(e){return"argumentDefinitions"===N(e)}));if(!n.length)return new Map;if(1!==n.length)throw p("Directive @".concat("argumentDefinitions"," may be defined at most once per ")+"fragment.",null,n);var r=n[0],a=r.arguments;if(null==r||!Array.isArray(a))return new Map;if(!a.length)throw p("Directive @".concat("argumentDefinitions"," requires arguments: remove the ")+"directive to skip defining local variables for this fragment.",null,[r]);var i=new Map;return a.forEach((function(e){var n,r,a,o=N(e),s=i.get(o);if(null!=s)throw p("Duplicate definition for variable '$".concat(o,"'."),null,[s.ast,e]);if("ObjectValue"!==e.value.kind)throw p("Expected definition for variable '$".concat(o,"' to be an object ")+"with the shape: '{type: string, defaultValue?: mixed}.",null,[e.value]);if(e.value.fields.forEach((function(t){var n=N(t);if("type"===n)a=E(t.value,t);else{if("defaultValue"!==n)throw p("Expected definition for variable '$".concat(o,"' to be an object ")+"with the shape: '{type: string, defaultValue?: mixed}.",null,[e.value]);r=t.value}})),"string"!=typeof a)throw p("Expected definition for variable '$".concat(o,"' to be an object ")+"with the shape: '{type: string, defaultValue?: mixed}.",null,[e.value]);var l=t._schema.getTypeFromAST(b(a));if(null==l)throw p('Unknown type "'.concat(a,'" referenced in the argument definitions.'),null,[e]);var u=t._schema.asInputType(l);if(null==u)throw p('Expected type "'.concat(a,'" to be an input type in the "').concat(e.name.value,'" argument definitions.'),null,[e.value]);var c=null!=r?F(t._schema,r,u,(function(e){throw p("Expected 'defaultValue' to be a literal, got a variable.",null,[e])})):null;if(null!=c&&"Literal"!==c.kind)throw p("Expected 'defaultValue' to be a literal, got a variable.",[c.loc]);i.set(o,{ast:e,defaultValue:null!==(n=null==c?void 0:c.value)&&void 0!==n?n:null,defined:!0,name:o,type:u})})),i},t._buildOperationArgumentDefinitions=function(e){var t=this._schema,n=new Map;return(e.variableDefinitions||[]).forEach((function(e){var r=N(e.variable),a=t.getTypeFromAST(e.type);if(null==a)throw p("Unknown type: '".concat(D(e.type),"'."),null,[e.type]);var i=t.asInputType(a);if(null==i)throw p('Expected type "'.concat(D(e.type),'" to be an input type.'),null,[e.type]);var o=e.defaultValue?E(e.defaultValue,e):null,s=n.get(r);if(null!=s)throw p("Duplicate definition for variable '$".concat(r,"'."),null,[s.ast,e]);n.set(r,{ast:e,defaultValue:o,defined:!0,name:r,type:i})})),n},e}();var k=function(){function e(e,t,n,r,a){this._definition=r,this._entries=n,this._getFieldDefinition=t,this._schema=e,this._variableDefinitions=a,this._unknownVariables=new Map}var t=e.prototype;return t.transform=function(){var e=this._definition;switch(e.kind){case"OperationDefinition":return this._transformOperation(e);case"FragmentDefinition":return this._transformFragment(e);default:throw d("Unsupported definition type ".concat(e.kind),[e])}},t._recordAndVerifyVariableReference=function(e,t,n){if(null!=n){var r=this._variableDefinitions.get(t);if(null!=r){var a=r.type;if(null!=r.defaultValue&&(a=this._schema.getNonNullType(this._schema.getNullableType(a))),!this._schema.isTypeSubTypeOf(a,n))throw p("Variable '$".concat(t,"' was defined as type '").concat(String(r.type),"' but used in a location expecting the type '").concat(String(n),"'"),null,[r.ast,e])}else{var i=this._unknownVariables.get(t);if(i&&i.type){var o=i.ast,s=i.type;if(!this._schema.isTypeSubTypeOf(n,s)&&!this._schema.isTypeSubTypeOf(s,n))throw p("Variable '$".concat(t,"' was used in locations expecting the conflicting types '").concat(String(s),"' and '").concat(String(n),"'."),null,[o,e]);this._schema.isTypeSubTypeOf(n,s)&&this._unknownVariables.set(t,{ast:e,type:n})}else this._unknownVariables.set(t,{ast:e,type:n})}}else this._variableDefinitions.has(t)||this._unknownVariables.has(t)||this._unknownVariables.set(t,{ast:e,type:null})},t._getDirectiveLocations=function(){if(!this._directiveLocations){var e=this._schema.getDirectives();this._directiveLocations=new Map;var t,n=(0,i.default)(e);try{for(n.s();!(t=n.n()).done;){var r=t.value;this._directiveLocations.set(r.name,r.locations)}}catch(e){n.e(e)}finally{n.f()}}return this._directiveLocations},t._validateDirectivesLocation=function(e,t){if(e&&e.length){var n=this._getDirectiveLocations(),r=e.filter((function(e){var r=N(e);if(w.has(r))return!1;var a=n.get(r);if(null==a)throw p("Unknown directive '".concat(r,"'."),null,[e]);return!a.some((function(e){return e===t}))}));if(r.length){var a=r.map((function(e){return"@"+N(e)})).join(", ");throw p("Invalid directives ".concat(a," found on ").concat(t,"."),null,r)}}},t._transformFragment=function(e){var t=this._transformDirectives((e.directives||[]).filter((function(e){return"argumentDefinitions"!==N(e)})),"FRAGMENT_DEFINITION"),n=this._schema.getTypeFromAST(e.typeCondition);if(null==n)throw p('Fragment "'.concat(e.name.value,'" cannot condition on unknown ')+'type "'.concat(String(e.typeCondition.name.value),'".'),null,[e.typeCondition]);var r=this._schema.asCompositeType(n);if(null==r)throw p('Fragment "'.concat(e.name.value,'" cannot condition on non composite ')+'type "'.concat(String(r),'".'),null,[e.typeCondition]);var a,s=this._transformSelections(e.selectionSet,r,e.typeCondition),l=(0,o.default)(x(this._variableDefinitions)),u=(0,i.default)(this._unknownVariables);try{for(u.s();!(a=u.n()).done;){var c=a.value,f=c[0],d=c[1];l.push({kind:"RootArgumentDefinition",loc:C(d.ast.loc),name:f,type:d.type})}}catch(e){u.e(e)}finally{u.f()}return{kind:"Fragment",directives:t,loc:C(e.loc),metadata:null,name:N(e),selections:s,type:r,argumentDefinitions:l}},t._getLocationFromOperation=function(e){switch(e.operation){case"query":return"QUERY";case"mutation":return"MUTATION";case"subscription":return"SUBSCRIPTION";default:throw e.operation,d("Unknown operation type '".concat(e.operation,"'."),null,[e])}},t._transformOperation=function(e){var t,n,r=N(e),a=this._transformDirectives(e.directives||[],this._getLocationFromOperation(e)),i=this._schema;switch(e.operation){case"query":n="query",t=i.expectQueryType();break;case"mutation":n="mutation",t=i.expectMutationType();break;case"subscription":n="subscription",t=i.expectSubscriptionType();break;default:throw e.operation,d("Unknown operation type '".concat(e.operation,"'."),null,[e])}if(!e.selectionSet)throw p("Expected operation to have selections.",null,[e]);var o=this._transformSelections(e.selectionSet,t),s=x(this._variableDefinitions);if(0!==this._unknownVariables.size)throw p("Query '".concat(r,"' references undefined variables."),null,Array.from(this._unknownVariables.values(),(function(e){return e.ast})));return{kind:"Root",operation:n,loc:C(e.loc),metadata:null,name:r,argumentDefinitions:s,directives:a,selections:o,type:t}},t._transformSelections=function(e,t,n){var r=this;return e.selections.map((function(e){var i;if("Field"===e.kind)i=r._transformField(e,t);else if("FragmentSpread"===e.kind)i=r._transformFragmentSpread(e,t,n);else{if("InlineFragment"!==e.kind)throw e.kind,d("Unknown ast kind '".concat(e.kind,"'."),[e]);i=r._transformInlineFragment(e,t,n)}var o=r._splitConditions(i.directives),s=o[0],l=o[1],u=function(e,t){var n=t;return e.forEach((function(e){n=[(0,a.default)((0,a.default)({},e),{},{selections:n})]})),n}(s,[(0,a.default)((0,a.default)({},i),{},{directives:l})]);if(1!==u.length)throw d("Expected exactly one condition node.",null,e.directives);return u[0]}))},t._transformInlineFragment=function(e,t,n){var r,a=this._schema,i=null!=e.typeCondition?a.getTypeFromAST(e.typeCondition):t;if(null==i)throw p("Inline fragments can only be on object, interface or union types"+", got unknown type '".concat(D(e.typeCondition),"'."),null,[null!==(r=e.typeCondition)&&void 0!==r?r:e]);var o,s=a.getTypeString(i);if(null==(i=a.asCompositeType(i)))throw p("Inline fragments can only be on object, interface or union types"+", got '".concat(s,"'."),null,[null!==(o=e.typeCondition)&&void 0!==o?o:e]);var l=this._schema.assertCompositeType(this._schema.getRawType(t));I(this._schema,i,l,null,e.typeCondition,n);var u=this._transformDirectives(e.directives||[],"INLINE_FRAGMENT"),c=this._transformSelections(e.selectionSet,i,e.typeCondition);return{kind:"InlineFragment",directives:u,loc:C(e.loc),metadata:null,selections:c,typeCondition:i}},t._transformFragmentSpread=function(e,t,n){var r=this,a=N(e),i=u(e.directives||[],(function(e){var t=N(e);return"arguments"===t||"uncheckedArguments_DEPRECATED"===t})),o=i[0],s=i[1];if(o.length>1)throw p("Directive @".concat("arguments"," may be used at most once per a fragment spread."),null,o);var l=this._entries.get(a);if(null==l)throw p("Unknown fragment '".concat(a,"'."),null,[e.name]);var c=function(e){if("FragmentDefinition"===e.kind)return e.typeCondition;throw d("Expected ast node to be a FragmentDefinition node.",null,[e])}(l.definition),f=this._schema.assertCompositeType(this._schema.expectTypeFromAST(c)),m=this._schema.assertCompositeType(this._schema.getRawType(t));I(this._schema,f,m,e.name.value,e,n);var h,v=l.variableDefinitions,y=o[0];if(null!=y){var g="uncheckedArguments_DEPRECATED"===N(y),b=!1;if(h=(y.arguments||[]).map((function(e){var t,n=N(e),i=e.value,o=v.get(n),s=null!==(t=null==o?void 0:o.type)&&void 0!==t?t:null;if("Variable"===i.kind){if(null==o&&!g)throw p("Variable @".concat("arguments"," values are only supported when the ")+"argument is defined with @".concat("argumentDefinitions",". Check ")+"the definition of fragment '".concat(a,"'."),null,[e.value,l.definition]);return b=b||null==o,{kind:"Argument",loc:C(e.loc),name:n,value:r._transformVariable(i,null),type:null}}if(null==s)throw p("Literal @".concat("arguments"," values are only supported when the ")+"argument is defined with @".concat("argumentDefinitions",". Check ")+"the definition of fragment '".concat(a,"'."),null,[e.value,l.definition]);var u=r._transformValue(i,s);return{kind:"Argument",loc:C(e.loc),name:n,value:u,type:s}})),g&&!b)throw p("Invalid use of @".concat("uncheckedArguments_DEPRECATED",": all arguments ")+"are defined, use @".concat("arguments"," instead."),null,[y])}var T=this._transformDirectives(s,"FRAGMENT_SPREAD");return{kind:"FragmentSpread",args:h||[],metadata:null,loc:C(e.loc),name:a,directives:T}},t._transformField=function(e,t){var n,r,a=this._schema,i=N(e),o=this._getFieldDefinition(a,t,i,e);if(null==o)throw p("Unknown field '".concat(i,"' on type '").concat(a.getTypeString(t),"'."),null,[e]);var s=null!==(n=null===(r=e.alias)||void 0===r?void 0:r.value)&&void 0!==n?n:i,l=this._transformArguments(e.arguments||[],a.getFieldArgs(o),o),c=u(e.directives||[],(function(e){return"__clientField"!==N(e)})),f=c[0],d=c[1],m=this._transformDirectives(f,"FIELD"),h=a.getFieldType(o),v=this._transformHandle(i,l,d);if(a.isLeafType(a.getRawType(h))){if(e.selectionSet&&e.selectionSet.selections&&e.selectionSet.selections.length)throw p("Expected no selections for scalar field '".concat(i,"'."),null,[e]);return{kind:"ScalarField",alias:s,args:l,directives:m,handles:v,loc:C(e.loc),metadata:null,name:i,type:a.assertScalarFieldType(h)}}var y=e.selectionSet?this._transformSelections(e.selectionSet,h):null;if(null==y||0===y.length)throw p("Expected at least one selection for non-scalar field '".concat(i,"' on type '").concat(a.getTypeString(h),"'."),null,[e]);return{kind:"LinkedField",alias:s,args:l,connection:!1,directives:m,handles:v,loc:C(e.loc),metadata:null,name:i,selections:y,type:a.assertLinkedFieldType(h)}},t._transformHandle=function(e,t,n){var r=null;return n.forEach((function(n){var a=(n.arguments||[]).find((function(e){return"handle"===N(e)}));if(a){var i,o=c,s=null,l=E(a.value,a);if("string"!=typeof l)throw p("Expected a string literal argument for the @".concat("__clientField"," directive."),null,[a.value]);i=l;var u=(n.arguments||[]).find((function(e){return"key"===N(e)}));if(u){var f=E(u.value,u);if("string"!=typeof f)throw p("Expected a string literal argument for the @".concat("__clientField"," directive."),null,[u.value]);o=f}var d=(n.arguments||[]).find((function(e){return"filters"===N(e)}));if(d){var m=E(d.value,d);if(!Array.isArray(m)||!m.every((function(e){return"string"==typeof e&&t.some((function(t){return t.name===e}))})))throw p("Expected an array of argument names on field '".concat(e,"'."),null,[d.value]);s=m}var h=(n.arguments||[]).find((function(e){return"dynamicKey_UNSTABLE"===N(e)}));if(null!=h)throw p("Dynamic keys are only supported with @connection.",null,[h.value]);(r=r||[]).push({name:i,key:o,filters:s,dynamicKey:null})}})),r},t._transformDirectives=function(e,t){var n=this;return this._validateDirectivesLocation(e,t),e.map((function(e){var t=N(e),r=n._schema.getDirective(t);if(null==r)throw p("Unknown directive '".concat(t,"'."),null,[e]);var a=n._transformArguments(e.arguments||[],r.args.map((function(e){return{name:e.name,type:e.type,defaultValue:e.defaultValue}})),null,t);return{kind:"Directive",loc:C(e.loc),name:t,args:a}}))},t._transformArguments=function(e,t,n,r){var a=this;return e.map((function(e){var i=N(e),o=t.find((function(e){return e.name===i}));if(null==o){var s="Unknown argument '".concat(i,"'")+(n?" on field '".concat(a._schema.getFieldName(n),"'")+" of type '".concat(a._schema.getTypeString(a._schema.getFieldParentType(n)),"'."):null!=r?" on directive '@".concat(r,"'."):".");throw p(s,null,[e])}var l=a._transformValue(e.value,o.type);return{kind:"Argument",loc:C(e.loc),name:i,value:l,type:o.type}}))},t._splitConditions=function(e){var t=u(e,(function(e){return"include"===e.name||"skip"===e.name})),n=t[0],r=t[1];return[n.map((function(e){var t="include"===e.name,n=e.args[0];if(null==n||"if"!==n.name)throw p("Expected an 'if' argument to @".concat(e.name,"."),[e.loc]);if("Variable"!==n.value.kind&&"Literal"!==n.value.kind)throw p("Expected the 'if' argument to @".concat(e.name," to be a variable or literal."),[e.loc]);return{kind:"Condition",condition:n.value,loc:e.loc,passingValue:t,selections:[]}})).sort((function(e,t){return"Variable"===e.condition.kind&&"Variable"===t.condition.kind?e.condition.variableName<t.condition.variableName?-1:e.condition.variableName>t.condition.variableName?1:0:"Variable"===e.condition.kind?1:"Variable"===t.condition.kind?-1:0})),r]},t._transformVariable=function(e,t){var n=N(e);return this._recordAndVerifyVariableReference(e,n,t),{kind:"Variable",loc:C(e.loc),variableName:n,type:t}},t._transformValue=function(e,t){var n=this;return F(this._schema,e,t,(function(e,t){return n._transformVariable(e,t)}))},e}();function F(e,t,n,r){if("Variable"===t.kind)return r(t,n);if("NullValue"===t.kind){if(e.isNonNull(n))throw p("Expected a value matching type '".concat(String(n),"'."),null,[t]);return{kind:"Literal",loc:C(t.loc),value:null}}return function(e,t,n,r){var a=e.getNullableType(n);if(e.isList(a)){if("ListValue"!==t.kind){if(!e.isInputType(e.getListItemType(a)))throw p("Expected type ".concat(e.getTypeString(a)," to be an input type."),null,[t]);return F(e,t,e.assertInputType(e.getListItemType(a)),r)}var i=e.assertInputType(e.getListItemType(a)),o=[],s=[],u=!0;return t.values.forEach((function(t){var n=F(e,t,i,r);"Literal"===n.kind&&o.push(n.value),s.push(n),u=u&&"Literal"===n.kind})),u?{kind:"Literal",loc:C(t.loc),value:o}:{kind:"ListValue",loc:C(t.loc),items:s}}if(e.isInputObject(a)){if("ObjectValue"!==t.kind)throw p("Expected a value matching type '".concat(e.getTypeString(n),"'."),null,[t]);var c={},f=[],m=!0,h=e.assertInputObjectType(a),v=new Set(e.getFields(h).filter((function(t){return e.isNonNull(e.getFieldType(t))})).map((function(t){return e.getFieldName(t)}))),y=new Map;if(t.fields.forEach((function(t){var n=N(t),a=y.get(n);if(a)throw p("Duplicated field name '".concat(n,"' in the input object."),null,[t,a]);var i=e.getFieldByName(h,n);if(!i)throw p("Unknown field '".concat(n,"' on type '").concat(e.getTypeString(h),"'."),null,[t]);var o=e.getFieldConfig(i),s=e.assertInputType(o.type),l=F(e,t.value,s,r);"Literal"===l.kind&&(c[t.name.value]=l.value),f.push({kind:"ObjectFieldValue",loc:C(t.loc),name:n,value:l}),y.set(n,t),v.delete(n),m=m&&"Literal"===l.kind})),v.size>0){var g=Array.from(v).map((function(e){return"'".concat(e,"'")})).join(", ");throw p("Missing non-optional field".concat(v.size>1?"s:":""," ").concat(g," for input type '").concat(e.getTypeString(h),"'."),null,[t])}return m?{kind:"Literal",loc:C(t.loc),value:c}:{kind:"ObjectValue",loc:C(t.loc),fields:f}}if(e.isId(a)){if("IntValue"===t.kind)return{kind:"Literal",loc:C(t.loc),value:parseInt(t.value,10)};if("StringValue"===t.kind)return{kind:"Literal",loc:C(t.loc),value:t.value};throw p("Invalid value, expected a value matching type '".concat(e.getTypeString(n),"'."),null,[t])}if(e.isEnum(a)){var b=e.assertEnumType(a),T=e.parseLiteral(b,t);if(null==T){var S=e.getEnumValues(b);throw p("Expected a value matching type '".concat(e.getTypeString(n),"'. Possible values: ").concat(l(S),"?'"),null,[t])}return{kind:"Literal",loc:C(t.loc),value:T}}if(e.isScalar(a)){var w=e.parseLiteral(e.assertScalarType(a),t);if(null==w)throw p("Expected a value matching type '".concat(e.getTypeString(n),"'."),null,[t]);return{kind:"Literal",loc:C(t.loc),value:w}}throw d("Unsupported type '".concat(e.getTypeString(n),"' for input value, expected a GraphQLList, ")+"GraphQLInputObjectType, GraphQLEnumType, or GraphQLScalarType.",null,[t])}(e,t,n,r)}function E(e,t){switch(e.kind){case"IntValue":return parseInt(e.value,10);case"FloatValue":return parseFloat(e.value);case"StringValue":case"BooleanValue":case"EnumValue":return e.value;case"ListValue":return e.values.map((function(e){return E(e,t)}));case"NullValue":return null;case"ObjectValue":var n={};return e.fields.forEach((function(e){var r=N(e),a=E(e.value,t);n[r]=a})),n;case"Variable":throw p("Unexpected variable where a literal (static) value is required.",null,[e,t]);default:throw e.kind,d("Unknown ast kind '".concat(e.kind,"'."),[e])}}function x(e){return Array.from(e.values(),(function(e){var t=e.ast,n=e.name,r=e.defaultValue,a=e.type;return{kind:"LocalArgumentDefinition",loc:C(t.loc),name:n,type:a,defaultValue:r}}))}function C(e){return null==e?{kind:"Unknown"}:{kind:"Source",start:e.start,end:e.end,source:e.source}}function N(e){var t,n=null===(t=e.name)||void 0===t?void 0:t.value;if("string"!=typeof n)throw d("Expected ast node to have a 'name'.",null,[e]);return n}function D(e){return e?T(e):"Undefined Type Name"}function I(e,t,n,r,a,i){if(!e.doTypesOverlap(t,e.assertCompositeType(n))){var o=[];i&&o.push(i),a&&o.push(a);var s=e.isAbstractType(n)?Array.from(e.getPossibleTypes(e.assertAbstractType(n))):[],l="";throw 0!==s.length&&(l=" Possible concrete types include ".concat(s.sort().slice(0,3).map((function(t){return"'".concat(e.getTypeString(t),"'")})).join(", "),", etc.")),p((null!=r?"Fragment '".concat(r,"' cannot be spread here as objects of "):"Fragment cannot be spread here as objects of ")+"type '".concat(e.getTypeString(n),"' ")+"can never be of type '".concat(e.getTypeString(t),"'.")+l,null,o)}}e.exports={parse:function(e,t,n){var r=g(new S(t,n));return new _(e.extend(r),r.definitions).transform()},transform:function(e,t){return s.run("RelayParser.transform",(function(){return new _(e,t).transform()}))}}},function(e,t,n){"use strict";var r=n(0),a=r(n(3)),i=r(n(2)),o=n(19),s=n(20),l=n(10),u=n(1).createCompilerError;function c(e,t,n){var r=n.name,a=t.get(r);if(null!=a)return a;var i=new Map;return n.argumentDefinitions.forEach((function(e){"LocalArgumentDefinition"===e.kind&&i.set(e.name,e)})),t.set(r,i),f(e,t,i,n),t.set(r,i),i}function f(e,t,n,r){s.visit(r,{FragmentSpread:function(r){var i=e.getFragment(r.name,r.loc),o=c(e,t,i);r.args.forEach((function(e){var t=o.get(e.name);null==t||"Variable"!==e.value.kind||n.has(e.value.variableName)||n.set(e.value.variableName,{kind:"RootArgumentDefinition",loc:{kind:"Derived",source:e.loc},name:e.value.variableName,type:t.type})}));var s,l=(0,a.default)(o.values());try{for(l.s();!(s=l.n()).done;){var u=s.value;"RootArgumentDefinition"===u.kind&&n.set(u.name,u)}}catch(e){l.e(e)}finally{l.f()}},Argument:function(e){if("Literal"===e.value.kind)return!1;for(var t=[e.value];t.length>0;){var r=t.pop();if("Variable"===r.kind){var a,i=null!==(a=r.type)&&void 0!==a?a:e.type;if(null==i)continue;n.has(r.variableName)||n.set(r.variableName,{kind:"RootArgumentDefinition",loc:{kind:"Derived",source:e.loc},name:r.variableName,type:i})}else"ObjectValue"===r.kind?r.fields.forEach((function(e){"Literal"!==e.value.kind&&t.push(e.value)})):"ListValue"===r.kind&&r.items.forEach((function(e){"Literal"!==e.kind&&t.push(e)}))}return!1},Condition:function(t){var r,a=t.condition;if("Variable"===a.kind){var i=null!==(r=a.type)&&void 0!==r?r:l.getNonNullBooleanInput(e.getSchema());n.has(a.variableName)||n.set(a.variableName,{kind:"RootArgumentDefinition",loc:{kind:"Derived",source:a.loc},name:a.variableName,type:i})}},Defer:function(t){var r,a=t.if;if(null!=a&&"Variable"===a.kind){var i=null!==(r=a.type)&&void 0!==r?r:l.getNonNullBooleanInput(e.getSchema());n.has(a.variableName)||n.set(a.variableName,{kind:"RootArgumentDefinition",loc:{kind:"Derived",source:a.loc},name:a.variableName,type:i})}},Stream:function(t){[t.if,t.initialCount].forEach((function(t){var r;if(null!=t&&"Variable"===t.kind){var a=null!==(r=t.type)&&void 0!==r?r:l.getNonNullBooleanInput(e.getSchema());n.has(t.variableName)||n.set(t.variableName,{kind:"RootArgumentDefinition",loc:{kind:"Derived",source:t.loc},name:t.variableName,type:a})}}))},LinkedField:function(t){t.handles&&t.handles.forEach((function(t){var r,a=t.dynamicKey;if(null!=a){var i=null!==(r=a.type)&&void 0!==r?r:l.getNullableStringInput(e.getSchema());n.has(a.variableName)||n.set(a.variableName,{kind:"RootArgumentDefinition",loc:{kind:"Derived",source:a.loc},name:a.variableName,type:i})}}))}})}e.exports=function(e){var t=new Map;return new o(e.getSchema()).addAll(Array.from(e.documents(),(function(n){switch(n.kind){case"Fragment":var r=c(e,t,n);return(0,i.default)((0,i.default)({},n),{},{argumentDefinitions:Array.from(r.values())});case"Root":return function(e,t,n){var r,o=new Map,s=new Map,l=(0,a.default)(n.argumentDefinitions.entries());try{for(l.s();!(r=l.n()).done;){var c=r.value,d=c[0],p=c[1];"LocalArgumentDefinition"===p.kind&&s.set(d,p)}}catch(e){l.e(e)}finally{l.f()}return f(e,t,o,n),(0,i.default)((0,i.default)({},n),{},{argumentDefinitions:Array.from(o.values(),(function(e){var t,n;if("RootArgumentDefinition"!==e.kind)throw u("inferRootArgumentDefinitions: Expected inferred variable '$".concat(e.name,"' to be a root variables."),[e.loc]);var r=s.get(e.name),a=null!==(t=null==r?void 0:r.type)&&void 0!==t?t:e.type;return{defaultValue:null!==(n=null==r?void 0:r.defaultValue)&&void 0!==n?n:null,kind:"LocalArgumentDefinition",loc:e.loc,name:e.name,type:a}}))})}(e,t,n);case"SplitOperation":return n;default:throw u("inferRootArgumentDefinitions: Unsupported kind '".concat(n.kind,"'."))}})))}},function(e,t,n){"use strict";var r=n(0)(n(3));e.exports={buildFragmentSpread:function(e){var t,n=[],a=(0,r.default)(e.argumentDefinitions);try{for(a.s();!(t=a.n()).done;){var i=t.value;"LocalArgumentDefinition"===i.kind&&n.push({kind:"Argument",loc:{kind:"Derived",source:i.loc},name:i.name,type:i.type,value:{kind:"Variable",loc:{kind:"Derived",source:i.loc},variableName:i.name,type:i.type}})}}catch(e){a.e(e)}finally{a.f()}return{args:n,directives:[],kind:"FragmentSpread",loc:{kind:"Derived",source:e.loc},metadata:null,name:e.name}},buildOperationArgumentDefinitions:function(e){var t=e.map((function(e){return"LocalArgumentDefinition"===e.kind?e:{kind:"LocalArgumentDefinition",name:e.name,type:e.type,defaultValue:null,loc:e.loc}}));return t.sort((function(e,t){return e.name<t.name?-1:e.name>t.name?1:0})),t}}},function(e,t,n){"use strict";var r=n(8),a=n(17).Map,i=function(){function e(e){this._documents=new Map,this._baseDir=e.baseDir,this._parse=r.instrument(e.parse,"ASTCache.parseFn")}var t=e.prototype;return t.documents=function(){return a(this._documents)},t.parseFiles=function(e){var t=this,n=a();return e.forEach((function(e){if(e.exists){var r=function(){try{return t._parse(t._baseDir,e)}catch(t){throw new Error("Parse error: ".concat(t,' in "').concat(e.relPath,'"'))}}();r?(n=n.set(e.relPath,r),t._documents.set(e.relPath,r)):t._documents.delete(e.relPath)}else t._documents.delete(e.relPath)})),n},e}();e.exports=i},function(e,t,n){"use strict";var r=n(8),a=n(10),i=a.isExecutableDefinitionAST,o=a.isSchemaDefinitionAST,s=n(7),l=s.extendSchema,u=s.parse,c=s.print,f=s.visit;function d(e,t,n){var r=[];return t.forEach((function(e){i(e)&&r.push(e)})),n(e,r)}function p(e){var t=[];return e.forEach((function(e){e.definitions.forEach((function(e){return t.push(e)}))})),t}var m=new Map;function h(e,t,n){var r=m.get(e);r||(r={},m.set(e,r));var a=r[t];return a||(a=n(),r[t]=a),a}e.exports={convertASTDocuments:function(e,t,n){return r.run("ASTConvert.convertASTDocuments",(function(){var r=p(t),a=[];return t.forEach((function(e){e.definitions.forEach((function(e){i(e)&&a.push(e)}))})),d(e,r,n)}))},convertASTDocumentsWithBase:function(e,t,n,a){return r.run("ASTConvert.convertASTDocumentsWithBase",(function(){var r=p(t),o=p(n),s=new Map,l=new Map;r.forEach((function(e){if(i(e)){var t=e.name&&e.name.value;if(null!=t){if(l.has(t))throw new Error("Duplicate definition of '".concat(t,"'."));l.set(t,e)}}}));var u=[];for(o.forEach((function(e){i(e)&&u.push(e)}));u.length>0;){var c=u.pop(),m=c.name&&c.name.value;if(null!=m)if(s.has(m)){if(s.get(m)!==c)throw new Error("Duplicate definition of '".concat(m,"'."))}else s.set(m,c),f(c,{FragmentSpread:function(e){var t=l.get(e.name.value);t&&u.push(t)}})}var h=[];return s.forEach((function(e){return h.push(e)})),d(e,h,a)}))},extendASTSchema:function(e,t){return r.run("ASTConvert.extendASTSchema",(function(){var n=[];if(t.forEach((function(e){e.definitions.forEach((function(e){o(e)&&n.push(e)}))})),0===n.length)return e;var r=n.map(c).join("\n");return h(e,r,(function(){return l(e,{kind:"Document",definitions:n},{assumeValid:!0})}))}))},transformASTSchema:function(e,t){return r.run("ASTConvert.transformASTSchema",(function(){if(0===t.length)return e;var n=t.join("\n");return h(e,n,(function(){return l(e,u(n))}))}))}}},function(e,t){e.exports=require("@babel/runtime/helpers/defineProperty")},function(e,t,n){"use strict";e.exports={moduleDependency:function(e){return"@@MODULE_START@@".concat(e,"@@MODULE_END@@")},postProcess:function(e,t){return e.replace(/"@@MODULE_START@@(.*?)@@MODULE_END@@"/g,(function(e,n){return t(n)}))},transform:function e(t,n){if(null==t)return t;if(Array.isArray(t))return t.map((function(t){return e(t,n)}));if("object"==typeof t){var r={};return Object.keys(t).forEach((function(a){r[a]=e(t[a],n)})),r}if("string"==typeof t){var a=/^@@MODULE_START@@(.*?)@@MODULE_END@@$/.exec(t);if(null!=a){var i=a[1];if(n.hasOwnProperty(i))return n[i];throw new Error("Could not find a value for CodeMarker value '".concat(i,"', ")+"make sure to supply one in the module mapping.")}if(t.indexOf("@@MODULE_START")>=0)throw new Error("Found unprocessed CodeMarker value '".concat(t,"'."));return t}return t}}},function(e,t,n){"use strict";var r=n(0),a=n(9),i=r(n(6)),o=n(8),s=n(14),l=n(5),u=n(11),c=function(){function e(e,t){var r,a,i=this;if(this._filesystem=null!==(r=t.filesystem)&&void 0!==r?r:n(13),this.onlyValidate=t.onlyValidate,this._shards=null!==(a=t.shards)&&void 0!==a?a:1,this._filesystem.existsSync(e)&&(this._filesystem.statSync(e).isDirectory()||l(!1,"Expected `%s` to be a directory.",e)),!this.onlyValidate){for(var o=[],s=e;!this._filesystem.existsSync(s);)o.unshift(s),s=u.dirname(s);if(o.forEach((function(e){return i._filesystem.mkdirSync(e)})),this._shards>1)for(var c=0;c<this._shards;c++){var f=u.join(e,this._getShardName(c));this._filesystem.existsSync(f)?this._filesystem.statSync(e).isDirectory()||l(!1,"Expected `%s` to be a directory.",e):this._filesystem.mkdirSync(f)}}this._files=new Set,this.changes={deleted:[],updated:[],created:[],unchanged:[]},this._dir=e}e.combineChanges=function(e){var t={deleted:[],updated:[],created:[],unchanged:[]};return e.forEach((function(e){var n,r,a,o;(n=t.deleted).push.apply(n,(0,i.default)(e.changes.deleted)),(r=t.updated).push.apply(r,(0,i.default)(e.changes.updated)),(a=t.created).push.apply(a,(0,i.default)(e.changes.created)),(o=t.unchanged).push.apply(o,(0,i.default)(e.changes.unchanged))})),t},e.hasChanges=function(e){return e.created.length>0||e.updated.length>0||e.deleted.length>0},e.formatChanges=function(e,t){var n=[];function r(e,t){t.length>0&&(n.push(e+":"),t.forEach((function(e){n.push(" - "+e)})))}return t.onlyValidate?(r("Missing",e.created),r("Out of date",e.updated),r("Extra",e.deleted)):(r("Created",e.created),r("Updated",e.updated),r("Deleted",e.deleted),n.push("Unchanged: ".concat(e.unchanged.length," files"))),n.join("\n")},e.printChanges=function(t,n){o.run("CodegenDirectory.printChanges",(function(){var r=e.formatChanges(t,n);console.log(r)}))},e.getAddedRemovedFiles=function(e){var t=[],n=[];return e.forEach((function(e){e.changes.created.forEach((function(n){t.push(e.getPath(n))})),e.changes.deleted.forEach((function(t){n.push(e.getPath(t))}))})),{added:t,removed:n}},e.sourceControlAddRemove=function(){var t=a((function*(t,n){var r=e.getAddedRemovedFiles(n),a=r.added,i=r.removed;t.addRemove(a,i)}));return function(e,n){return t.apply(this,arguments)}}();var t=e.prototype;return t.printChanges=function(){e.printChanges(this.changes,{onlyValidate:this.onlyValidate})},t.read=function(e){var t=u.join(this._dir,e);return this._filesystem.existsSync(t)?this._filesystem.readFileSync(t,"utf8"):null},t.markUnchanged=function(e){this._addGenerated(e),this.changes.unchanged.push(e)},t.markUpdated=function(e){this._addGenerated(e),this.changes.updated.push(e)},t.writeFile=function(e,t){var n=this,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];o.run("CodegenDirectory.writeFile",(function(){n._addGenerated(e);var a=n.getPath(e);n._filesystem.existsSync(a)?n._filesystem.readFileSync(a,"utf8")!==t||r?(n._writeFile(a,t),n.changes.updated.push(e)):n.changes.unchanged.push(e):(n._writeFile(a,t),n.changes.created.push(e))}))},t._writeFile=function(e,t){this.onlyValidate||this._filesystem.writeFileSync(e,t,"utf8")},t.deleteExtraFiles=function(e){var t=this;o.run("CodegenDirectory.deleteExtraFiles",(function(){t._shards>1?t._filesystem.readdirSync(t._dir).forEach((function(n){if(!n.startsWith(".")){var r=u.join(t._dir,n);t._filesystem.statSync(r).isDirectory()?t._filesystem.readdirSync(r).forEach((function(n){if(!(e&&e(n)||t._files.has(n))){if(!t.onlyValidate)try{t._filesystem.unlinkSync(u.join(r,n))}catch(e){throw new Error("CodegenDirectory: Failed to delete `"+n+"` in `"+t._dir+"`.")}t.changes.deleted.push(n)}})):t._filesystem.unlinkSync(r)}})):t._filesystem.readdirSync(t._dir).forEach((function(n){if(!(e&&e(n)||n.startsWith(".")||t._files.has(n))){if(!t.onlyValidate)try{t._filesystem.unlinkSync(u.join(t._dir,n))}catch(e){throw new Error("CodegenDirectory: Failed to delete `"+n+"` in `"+t._dir+"`.")}t.changes.deleted.push(n)}}))}))},t.getPath=function(e){if(this._shards>1){var t=s.createHash("md5");t.update(e,"utf8");var n=t.digest().readUInt32BE(0)%this._shards;return u.join(this._dir,this._getShardName(n),e)}return u.join(this._dir,e)},t._getShardName=function(e){var t=Math.ceil(Math.log2(256)/4);return e.toString(16).padStart(t,"0")},t._addGenerated=function(e){this._files.has(e)&&l(!1,"CodegenDirectory: Tried to generate `%s` twice in `%s`.",e,this._dir),this._files.add(e)},e}();e.exports=c},function(e,t,n){"use strict";var r=n(0),a=r(n(3)),i=r(n(44)),o=n(1).createCompilerError,s=n(10).isSchemaDefinitionAST,l=n(7),u=l.GraphQLFloat,c=l.GraphQLInt,f=l.GraphQLBoolean,d=l.GraphQLString,p=l.GraphQLID,m=l.parse,h=l.parseType,v=l.print,y=l.valueFromASTUntyped,g=function(){function e(e,t){this.name=e,this.isClient=t}var t=e.prototype;return t.toString=function(){return this.name},t.toJSON=function(){return String(this)},e}(),b=function(e){function t(){return e.apply(this,arguments)||this}return(0,i.default)(t,e),t}(g),T=function(e){function t(t,n,r){var a;return(a=e.call(this,t,r)||this).values=n,a}return(0,i.default)(t,e),t}(g),S=function(e){function t(){return e.apply(this,arguments)||this}return(0,i.default)(t,e),t}(g),w=function(e){function t(){return e.apply(this,arguments)||this}return(0,i.default)(t,e),t}(g),_=function(e){function t(){return e.apply(this,arguments)||this}return(0,i.default)(t,e),t}(g),k=function(e){function t(){return e.apply(this,arguments)||this}return(0,i.default)(t,e),t}(g),F=function(){function e(e){this.ofType=e,this._typeString="[".concat(String(this.ofType),"]")}var t=e.prototype;return t.toString=function(){return this._typeString},t.toJSON=function(){return this.toString()},e}(),E=function(){function e(e){this.ofType=e,this._typeString="".concat(String(this.ofType),"!")}var t=e.prototype;return t.toString=function(){return this._typeString},t.toJSON=function(){return this.toString()},e}(),x=function(e,t,n,r,a,i,o){this.name=t,this.type=n,this.belongsTo=r,this.isClient=o,this.args=function(e,t){return new Map(H(e,t).map((function(e){return[e.name,e]})))}(e,a),this.directives=i};function C(e){return e instanceof E||e instanceof F?C(e.ofType):e}function N(e,t,n){return U(t)&&function(e,t){var n=new Set;return e.getPossibleTypes(t).forEach((function(e){O(e)&&n.add(e)})),Array.from(n)}(e,t).some((function(t){return e.implementsInterface(e.assertCompositeType(t),n)}))}var D=Symbol("Query"),I=Symbol("Mutation"),A=Symbol("Subscription");function R(e){return e instanceof b}function O(e){return e instanceof w}function M(e){return e instanceof T}function L(e){return e instanceof S}function j(e){return e instanceof _}function V(e){return e instanceof k}function q(e){return e instanceof F||e instanceof E}function P(e){return e instanceof b||e instanceof w||e instanceof T||e instanceof S||e instanceof _||e instanceof k}function U(e){return e instanceof S||e instanceof k}function G(e){return e instanceof w||e instanceof S||e instanceof k}function B(e){return e instanceof _||e instanceof b||e instanceof T}var W=function(){function e(e){var t=this;this._typeMap=e,this._typeWrappersMap=new Map,this._fieldsMap=new Map,this._typeNameMap=new Map,this._clientIdMap=new Map,this._directiveMap=new Map(e.getDirectives().map((function(e){return[e.name,{locations:e.locations,args:H(t,e.args),name:e.name,isClient:e.isClient}]})))}var t=e.prototype;return t.getTypes=function(){return this._typeMap.getTypes()},t.getTypeFromAST=function(e){if("NonNullType"===e.kind){var t=this.getTypeFromAST(e.type);if(!t)return;if(t instanceof E)throw o("Unable to wrap non-nullable type with non-null wrapper.");var n="".concat(this.getTypeString(t),"!"),r=this._typeWrappersMap.get(n);return r||(r=new E(t),this._typeWrappersMap.set(n,r),r)}if("ListType"===e.kind){var a=this.getTypeFromAST(e.type);if(!a)return;var i="[".concat(this.getTypeString(a),"]"),s=this._typeWrappersMap.get(i);return s||(s=new F(a),this._typeWrappersMap.set(i,s),s)}return this._typeMap.getTypeByName(e.name.value)},t._getRawType=function(e){var t,n=this._typeWrappersMap.get(e);return n||("string"==typeof e?this.getTypeFromAST(h(e)):(e===D?t=this._typeMap.getQueryType():e===I?t=this._typeMap.getMutationType():e===A&&(t=this._typeMap.getSubscriptionType()),t instanceof w?t:void 0))},t.getTypeFromString=function(e){return this._getRawType(e)},t.expectTypeFromString=function(e){var t=this.getTypeFromString(e);if(null==t)throw o("Unknown type: '".concat(e,"'."));return t},t.expectTypeFromAST=function(e){var t=this.getTypeFromAST(e);if(null==t)throw o("Unknown type: '".concat(v(e),"'."),null,[e]);return t},t.getNonNullType=function(e){if(e instanceof E)return e;var t="".concat(String(e),"!"),n=this._typeWrappersMap.get(t);return n||(n=new E(e),this._typeWrappersMap.set(t,n),n)},t.getRawType=function(e){return C(e)},t.getNullableType=function(e){return e instanceof E?e.ofType:e},t.getListItemType=function(e){return e instanceof F?e.ofType:e},t.mapListItemType=function(e,t){if(!(e instanceof F))throw o("Expected List type");var n=t(e.ofType),r="[".concat(this.getTypeString(n),"]"),a=this._typeWrappersMap.get(r);return a||(a=new F(n),this._typeWrappersMap.set(r,a),a)},t.areEqualTypes=function(e,t){return e===t||(e instanceof E&&t instanceof E||e instanceof F&&t instanceof F?this.areEqualTypes(e.ofType,t.ofType):!(!P(e)||!P(t))&&e.name===t.name)},t.mayImplement=function(e,t){return this.areEqualTypes(e,t)||this.implementsInterface(e,t)||this.isAbstractType(e)&&N(this,e,t)},t.implementsInterface=function(e,t){var n=this;return this.getInterfaces(e).some((function(e){return n.areEqualTypes(e,t)}))},t.canHaveSelections=function(e){return this.isObject(e)||this.isInterface(e)},t.getTypeString=function(e){return e.toString()},t.isTypeSubTypeOf=function(e,t){return e===t||(t instanceof E?e instanceof E&&this.isTypeSubTypeOf(e.ofType,t.ofType):e instanceof E?this.isTypeSubTypeOf(e.ofType,t):t instanceof F?e instanceof F&&this.isTypeSubTypeOf(e.ofType,t.ofType):!(e instanceof F)&&!!(this.isAbstractType(t)&&this.isObject(e)&&this.isPossibleType(this.assertAbstractType(t),this.assertObjectType(e))))},t.doTypesOverlap=function(e,t){var n=this;return e===t||(U(e)?U(t)?Array.from(this.getPossibleTypes(e)).some((function(e){if(O(e))return n.isPossibleType(t,e)})):this.isPossibleType(e,t):!!U(t)&&this.isPossibleType(t,e))},t.isPossibleType=function(e,t){return this._typeMap.getPossibleTypeSet(e).has(t)},t.assertScalarFieldType=function(e){if(q(e)&&!R(C(e))&&!M(C(e))||!q(e)&&!R(e)&&!M(e))throw o("Expected ".concat(String(e)," to be a Scalar or Enum type."));return e},t.assertLinkedFieldType=function(e){if(q(e)&&!G(C(e))||!q(e)&&!G(e))throw o("Expected ".concat(String(e)," to be a Object, Interface or a Union Type."));return e},t.assertInputType=function(e){if(q(e)&&!B(C(e))||!q(e)&&!B(e))throw o("Expected ".concat(String(e)," to be a Input, Scalar or Enum type."));return e},t.asCompositeType=function(e){if(G(e))return e},t.asInputType=function(e){if(q(e)&&B(C(e))||!q(e)&&B(e))return e},t.asScalarFieldType=function(e){if(R(e)||M(e))return e},t.assertScalarType=function(e){if(!R(e))throw o("Expected ".concat(this.getTypeString(e)," to be a scalar type, got ").concat(this.getTypeString(e),"."));return e},t.assertObjectType=function(e){if(!O(e))throw o("Expected ".concat(this.getTypeString(e)," to be an object type."));return e},t.assertInputObjectType=function(e){if(!j(e))throw o("Expected ".concat(this.getTypeString(e)," to be an input type."));return e},t.asInputObjectType=function(e){return j(e)?e:null},t.assertInterfaceType=function(e){if(!V(e))throw o("Expected ".concat(this.getTypeString(e)," to be an interface type."));return e},t.assertCompositeType=function(e){if(!G(e))throw o("Expected ".concat(this.getTypeString(e)," to be a composite type."));return e},t.assertAbstractType=function(e){if(!U(e))throw o("Expected ".concat(this.getTypeString(e)," to be an abstract type."));return e},t.assertLeafType=function(e){if(!this.isLeafType(e))throw o("Expected ".concat(this.getTypeString(e)," to be a leaf type."));return e},t.assertUnionType=function(e){if(!L(e))throw o("Expected ".concat(this.getTypeString(e)," to be a union type."));return e},t.assertEnumType=function(e){if(!M(e))throw o("Expected ".concat(String(e)," to be an enum type."));return e},t.assertIntType=function(e){if(!R(e)||!this.isInt(e))throw o("Expected ".concat(String(e)," to be an 'Int' type."));return e},t.assertFloatType=function(e){if(!R(e)||!this.isFloat(e))throw o("Expected ".concat(this.getTypeString(e)," to be a 'Float' type."));return e},t.assertBooleanType=function(e){if(!R(e)||!this.isBoolean(e))throw o("Expected ".concat(this.getTypeString(e)," to be a 'Boolean' type."));return e},t.assertStringType=function(e){if(!R(e)||!this.isString(e))throw o("Expected ".concat(this.getTypeString(e)," to be a 'String' type."));return e},t.assertIdType=function(e){if(!R(e)||!this.isId(e))throw o("Expected ".concat(this.getTypeString(e)," to be an ID type."));return e},t.expectBooleanType=function(){return this.assertScalarType(this.expectTypeFromString("Boolean"))},t.expectIntType=function(){return this.assertScalarType(this.expectTypeFromString("Int"))},t.expectFloatType=function(){return this.assertScalarType(this.expectTypeFromString("Float"))},t.expectStringType=function(){return this.assertScalarType(this.expectTypeFromString("String"))},t.expectIdType=function(){return this.assertScalarType(this.expectTypeFromString("ID"))},t.getQueryType=function(){var e=this._getRawType(D);if(e&&O(e))return e},t.getMutationType=function(){var e=this._getRawType(I);if(e&&O(e))return e},t.getSubscriptionType=function(){var e=this._getRawType(A);if(e&&O(e))return e},t.expectQueryType=function(){var e=this.getQueryType();if(null==e)throw o("Query type is not defined on the Schema");return e},t.expectMutationType=function(){var e=this.getMutationType();if(null==e)throw o("Mutation type is not defined the Schema");return e},t.expectSubscriptionType=function(){var e=this.getSubscriptionType();if(null==e)throw o("Subscription type is not defined the Schema");return e},t.isNonNull=function(e){return e instanceof E},t.isList=function(e){return e instanceof F},t.isWrapper=function(e){return q(e)},t.isScalar=function(e){return R(e)},t.isObject=function(e){return O(e)},t.isEnum=function(e){return M(e)},t.isUnion=function(e){return L(e)},t.isInputObject=function(e){return j(e)},t.isInterface=function(e){return V(e)},t.isInputType=function(e){return B(e)||q(e)&&B(C(e))},t.isCompositeType=function(e){return G(e)},t.isAbstractType=function(e){return U(e)},t.isLeafType=function(e){return this.isScalar(e)||this.isEnum(e)},t.isId=function(e){return e instanceof b&&"ID"===e.name},t.isInt=function(e){return e instanceof b&&"Int"===e.name},t.isFloat=function(e){return e instanceof b&&"Float"===e.name},t.isBoolean=function(e){return e instanceof b&&"Boolean"===e.name},t.isString=function(e){return e instanceof b&&"String"===e.name},t.hasField=function(e,t){return!(!this.isObject(e)&&!this.isAbstractType(e)||"__typename"!==t&&"__id"!==t)||(e instanceof w||e instanceof k?null!=this._typeMap.getField(e,t):e instanceof _&&null!=this._typeMap.getInputField(e,t))},t.hasId=function(e){if(!this.hasField(e,"id"))return!1;var t=this.expectField(e,"id");return this.areEqualTypes(this.getNullableType(this.getFieldType(t)),this.expectIdType())},t.getFields=function(e){var t=this._getFieldsMap(e);return Array.from(t.values())},t._getFieldsMap=function(e){var t=this._fieldsMap.get(e);if(null!=t)return t;var n=new Map;if(e instanceof w||e instanceof k){var r=this._typeMap.getFieldMap(e);if(r){var i,s=(0,a.default)(r);try{for(s.s();!(i=s.n()).done;){var l=i.value,u=l[0],c=l[1],f=this.expectTypeFromAST(c.type);n.set(u,new x(this,u,f,this.assertCompositeType(e),c.arguments,c.directives,c.isClient))}}catch(e){s.e(e)}finally{s.f()}}}else if(e instanceof _){var d=this._typeMap.getInputFieldMap(e);if(d){var p,m=(0,a.default)(d);try{for(m.s();!(p=m.n()).done;){var h=p.value,v=h[0],y=h[1],g=this.expectTypeFromAST(y);n.set(v,new x(this,v,g,e,[],null,!1))}}catch(e){m.e(e)}finally{m.f()}}}if(0===n.size)throw o("_getFieldsMap: Type '".concat(e.name,"' should have fields."));return this._fieldsMap.set(e,n),n},t.getFieldByName=function(e,t){if(this.hasField(e,t)){if("__typename"===t){var n=this._typeNameMap.get(e);return n||(n=new x(this,"__typename",this.getNonNullType(this.expectStringType()),e,[],null,!1),this._typeNameMap.set(e,n)),n}if("__id"===t){var r=this._clientIdMap.get(e);return r||(r=new x(this,"__id",this.getNonNullType(this.expectIdType()),e,[],null,!0),this._clientIdMap.set(e,r)),r}if(L(e))throw o("Unexpected union type '".concat(this.getTypeString(e),"' in the 'getFieldByName(...)'. Expected type with fields"));return this._getFieldsMap(e).get(t)}},t.expectField=function(e,t){var n=this.getFieldByName(e,t);if(!n)throw o("Unknown field '".concat(t,"' on type '").concat(this.getTypeString(e),"'."));return n},t.getFieldConfig=function(e){return{type:e.type,args:Array.from(e.args.values())}},t.getFieldName=function(e){return e.name},t.getFieldType=function(e){return e.type},t.getFieldParentType=function(e){return e.belongsTo},t.getFieldArgs=function(e){return Array.from(e.args.values())},t.getFieldArgByName=function(e,t){return e.args.get(t)},t.getEnumValues=function(e){return e.values},t.getUnionTypes=function(e){return Array.from(this._typeMap.getPossibleTypeSet(e))},t.getInterfaces=function(e){return e instanceof w?this._typeMap.getInterfaces(e):[]},t.getPossibleTypes=function(e){return this._typeMap.getPossibleTypeSet(e)},t.getFetchableFieldName=function(e){return this._typeMap.getFetchableFieldName(e)},t.parseLiteral=function(e,t){if(e instanceof T&&"EnumValue"===t.kind)return this.parseValue(e,t.value);if(e instanceof b){if("BooleanValue"===t.kind&&"Boolean"===e.name)return f.parseLiteral(t);if("FloatValue"===t.kind&&"Float"===e.name)return u.parseLiteral(t);if("IntValue"===t.kind&&("Int"===e.name||"ID"===e.name||"Float"===e.name))return c.parseLiteral(t);if("StringValue"===t.kind&&("String"===e.name||"ID"===e.name))return d.parseLiteral(t);if(!z(e.name))return y(t)}},t.parseValue=function(e,t){if(e instanceof T)return e.values.includes(t)?t:void 0;if(e instanceof b)switch(e.name){case"Boolean":return f.parseValue(t);case"Float":return u.parseValue(t);case"Int":return c.parseValue(t);case"String":return d.parseValue(t);case"ID":return p.parseValue(t);default:return t}},t.serialize=function(e,t){if(e instanceof T)return e.values.includes(t)?t:void 0;if(e instanceof b)switch(e.name){case"Boolean":return f.serialize(t);case"Float":return u.serialize(t);case"Int":return c.serialize(t);case"String":return d.serialize(t);case"ID":return p.serialize(t);default:return t}},t.getDirectives=function(){return Array.from(this._directiveMap.values())},t.getDirective=function(e){return this._directiveMap.get(e)},t.isServerType=function(e){return!1===C(e).isClient},t.isServerField=function(e){return!1===e.isClient},t.isServerDirective=function(e){var t=this._directiveMap.get(e);return!1===(null==t?void 0:t.isClient)},t.isServerDefinedField=function(e,t){return this.isAbstractType(e)&&t.directives.some((function(e){return"fixme_fat_interface"===e.name}))||this.hasField(e,t.name)&&this.isServerField(this.expectField(e,t.name))},t.isClientDefinedField=function(e,t){return!this.isServerDefinedField(e,t)},t.extend=function(t){var n=Array.isArray(t)?m(t.join("\n")):t,r=[];return n.definitions.forEach((function(e){s(e)&&r.push(e)})),r.length>0?new e(this._typeMap.extend(r)):this},e}(),Q=function(){function e(e,t){this._types=new Map([["ID",new b("ID",!1)],["String",new b("String",!1)],["Boolean",new b("Boolean",!1)],["Float",new b("Float",!1)],["Int",new b("Int",!1)]]),this._typeInterfaces=new Map,this._unionTypes=new Map,this._interfaceImplementations=new Map,this._fields=new Map,this._inputFields=new Map,this._directives=new Map([["include",{name:"include",isClient:!1,locations:["FIELD","FRAGMENT_SPREAD","INLINE_FRAGMENT"],args:[{name:"if",typeNode:h("Boolean!"),defaultValue:void 0}]}],["skip",{name:"skip",isClient:!1,locations:["FIELD","FRAGMENT_SPREAD","INLINE_FRAGMENT"],args:[{name:"if",typeNode:h("Boolean!"),defaultValue:void 0}]}],["deprecated",{name:"deprecated",isClient:!1,locations:["FIELD_DEFINITION","ENUM_VALUE"],args:[{name:"reason",typeNode:h("String"),defaultValue:{kind:"StringValue",value:"No longer supported"}}]}]]),this._queryTypeName="Query",this._mutationTypeName="Mutation",this._subscriptionTypeName="Subscription",this._source=e,this._extensions=t,this._fetchable=new Map,this._parse(e),this._extend(t)}var t=e.prototype;return t._parse=function(e){var t=this;m(e,{noLocation:!0}).definitions.forEach((function(e){switch(e.kind){case"SchemaDefinition":t._parseSchemaDefinition(e);break;case"ScalarTypeDefinition":t._parseScalarNode(e,!1);break;case"EnumTypeDefinition":t._parseEnumNode(e,!1);break;case"ObjectTypeDefinition":t._parseObjectTypeNode(e,!1);break;case"InputObjectTypeDefinition":t._parseInputObjectTypeNode(e,!1);break;case"UnionTypeDefinition":t._parseUnionNode(e,!1);break;case"InterfaceTypeDefinition":t._parseInterfaceNode(e,!1);break;case"DirectiveDefinition":t._parseDirective(e,!1)}}))},t._parseSchemaDefinition=function(e){var t=this;e.operationTypes.forEach((function(e){switch(e.operation){case"query":t._queryTypeName=e.type.name.value;break;case"mutation":t._mutationTypeName=e.type.name.value;break;case"subscription":t._subscriptionTypeName=e.type.name.value}}))},t._parseScalarNode=function(e,t){var n=e.name.value;if(!z(n)&&this._types.has(n))throw o("_parseScalarNode: Duplicate definition for type ".concat(n,"."),null,[e]);this._types.set(n,new b(n,t))},t._parseEnumNode=function(e,t){var n=e.name.value;if(this._types.has(n))throw o("_parseEnumNode: Duplicate definition for type ".concat(n,"."),null,[e]);var r=e.values?e.values.map((function(e){return e.name.value})):[];this._types.set(n,new T(n,r,t))},t._parseObjectTypeNode=function(e,t){var n,r=this,a=e.name.value,i=null!==(n=this._types.get(a))&&void 0!==n?n:new w(a,t);if(!(i instanceof w))throw o("_parseObjectTypeNode: Expected object type, got ".concat(String(i)),null,[e]);if(i.isClient!==t)throw o("_parseObjectTypeNode: Cannot create object type '".concat(a,"' defined as a client type."),null,[e]);var s=[];e.interfaces&&e.interfaces.forEach((function(e){var n,a=e.name.value,l=r._types.get(a);if(l||(l=new k(a,t),r._types.set(a,l)),!(l instanceof k))throw o("_parseObjectTypeNode: Expected interface type",null,[e]);var u=null!==(n=r._interfaceImplementations.get(l))&&void 0!==n?n:new Set;u.add(i),r._interfaceImplementations.set(l,u),s.push(l)}));var l=null;e.directives&&e.directives.forEach((function(e){if("fetchable"===e.name.value){var t=e.arguments&&e.arguments.find((function(e){return"field_name"===e.name.value}));null!=t&&"StringValue"===t.value.kind&&(l={field_name:t.value.value})}})),this._typeInterfaces.set(i,s),this._types.set(a,i),null!=l&&this._fetchable.set(i,l),e.fields&&this._handleTypeFieldsStrict(i,e.fields,t)},t._parseInputObjectTypeNode=function(e,t){var n=e.name.value;if(this._types.has(n))throw o("_parseInputObjectTypeNode: Unable to parse schema file. Duplicate definition for object type",null,[e]);var r=new _(n,t);this._types.set(n,r),this._parseInputObjectFields(r,e)},t._parseUnionNode=function(e,t){var n=this,r=e.name.value;if(this._types.has(r))throw o("_parseUnionNode: Unable to parse schema file. Duplicate definition for object type",null,[e]);var a=new S(r,t);this._types.set(r,a),this._unionTypes.set(a,new Set(e.types?e.types.map((function(e){var t,r=e.name.value,a=null!==(t=n._types.get(r))&&void 0!==t?t:new w(r,!1);if(!(a instanceof w))throw o("_parseUnionNode: Expected object type",null,[e]);return n._types.set(r,a),a})):[]))},t._parseInterfaceNode=function(e,t){var n=e.name.value,r=this._types.get(n);if(r||(r=new k(n,t),this._types.set(n,r)),!(r instanceof k))throw o("_parseInterfaceNode: Expected interface type. Got ".concat(String(r)),null,[e]);if(r.isClient!==t)throw o("_parseInterfaceNode: Cannot create interface '".concat(n,"' defined as a client interface"),null,[e]);e.fields&&this._handleTypeFieldsStrict(r,e.fields,t)},t._handleTypeFieldsStrict=function(e,t,n){if(this._fields.has(e))throw o("_handleTypeFieldsStrict: Unable to parse schema file. Duplicate definition for object type");this._handleTypeFields(e,t,n)},t._handleTypeFields=function(e,t,n){var r,a=null!==(r=this._fields.get(e))&&void 0!==r?r:new Map;t.forEach((function(e){var t=e.name.value;if(a.has(t))throw o("_handleTypeFields: Duplicate definition for field '".concat(t,"'."));a.set(t,{arguments:e.arguments?e.arguments.map((function(e){return{name:e.name.value,typeNode:e.type,defaultValue:e.defaultValue}})):[],directives:e.directives?e.directives.map((function(e){return{name:e.name.value,args:e.arguments?e.arguments.map((function(e){return{name:e.name.value,value:e.value}})):[]}})):null,type:e.type,isClient:n})})),this._fields.set(e,a)},t._parseInputObjectFields=function(e,t){if(this._inputFields.has(e))throw o("_parseInputObjectFields: Unable to parse schema file. Duplicate definition for type",null,[t]);var n=new Map;t.fields&&t.fields.forEach((function(e){n.set(e.name.value,e.type)})),this._inputFields.set(e,n)},t._parseDirective=function(e,t){var n=e.name.value;this._directives.set(n,{name:n,args:e.arguments?e.arguments.map((function(e){return{name:e.name.value,typeNode:e.type,defaultValue:e.defaultValue}})):[],locations:e.locations.map((function(e){switch(e.value){case"QUERY":case"MUTATION":case"SUBSCRIPTION":case"FIELD":case"FRAGMENT_DEFINITION":case"FRAGMENT_SPREAD":case"INLINE_FRAGMENT":case"VARIABLE_DEFINITION":case"SCHEMA":case"SCALAR":case"OBJECT":case"FIELD_DEFINITION":case"ARGUMENT_DEFINITION":case"INTERFACE":case"UNION":case"ENUM":case"ENUM_VALUE":case"INPUT_OBJECT":case"INPUT_FIELD_DEFINITION":return e.value;default:throw o("Invalid directive location")}})),isClient:t})},t._parseObjectTypeExtension=function(e){var t=this._types.get(e.name.value);if(!(t instanceof w))throw o("_parseObjectTypeExtension: Expected to find type with the name '".concat(e.name.value,"'"),null,[e]);e.fields&&this._handleTypeFields(t,e.fields,!0)},t._parseInterfaceTypeExtension=function(e){var t=this._types.get(e.name.value);if(!(t instanceof k))throw o("_parseInterfaceTypeExtension: Expected to have an interface type");e.fields&&this._handleTypeFields(t,e.fields,!0)},t._extend=function(e){var t=this;e.forEach((function(e){if("ObjectTypeDefinition"===e.kind)t._parseObjectTypeNode(e,!0);else if("InterfaceTypeDefinition"===e.kind)t._parseInterfaceNode(e,!0);else if("ScalarTypeDefinition"===e.kind)t._parseScalarNode(e,!0);else if("EnumTypeDefinition"===e.kind)t._parseEnumNode(e,!0);else if("InterfaceTypeExtension"===e.kind)t._parseInterfaceTypeExtension(e);else if("ObjectTypeExtension"===e.kind)t._parseObjectTypeExtension(e);else{if("DirectiveDefinition"!==e.kind)throw o("Unexpected extension kind: '".concat(e.kind,"'"),null,[e]);t._parseDirective(e,!0)}}))},t.getTypes=function(){return Array.from(this._types.values())},t.getTypeByName=function(e){return this._types.get(e)},t.getInterfaces=function(e){var t;return null!==(t=this._typeInterfaces.get(e))&&void 0!==t?t:[]},t.getPossibleTypeSet=function(e){var t;if(e instanceof k){var n;t=null!==(n=this._interfaceImplementations.get(e))&&void 0!==n?n:new Set}else{if(!(e instanceof S))throw o('Invalid type supplied to "getPossibleTypeSet"');var r;t=null!==(r=this._unionTypes.get(e))&&void 0!==r?r:new Set}if(!t)throw o("Unable to find possible types for ".concat(e.name));return t},t.getFetchableFieldName=function(e){var t,n;return null!==(t=null===(n=this._fetchable.get(e))||void 0===n?void 0:n.field_name)&&void 0!==t?t:null},t.getQueryType=function(){return this._types.get(this._queryTypeName)},t.getMutationType=function(){return this._types.get(this._mutationTypeName)},t.getSubscriptionType=function(){return this._types.get(this._subscriptionTypeName)},t.getField=function(e,t){var n=this._fields.get(e);if(n)return n.get(t)},t.getFieldMap=function(e){return this._fields.get(e)},t.getInputField=function(e,t){var n=this._inputFields.get(e);if(n)return n.get(t)},t.getInputFieldMap=function(e){return this._inputFields.get(e)},t.getDirectives=function(){return Array.from(this._directives.values())},t.extend=function(t){return new e(this._source,this._extensions.concat(t))},e}();function H(e,t){return t.map((function(t){var n,r=e.assertInputType(e.expectTypeFromAST(t.typeNode)),a=t.defaultValue;if(null!=a){var i=e.getNullableType(r);if(!1===e.isNonNull(r)&&"NullValue"===a.kind?n=null:i instanceof b||i instanceof T?n=e.parseLiteral(i,a):(i instanceof F&&"ListValue"===a.kind||i instanceof _&&"ObjectValue"===a.kind)&&(n=y(a)),void 0===n)throw o("parseInputArgumentDefinitions: Unexpected default value: ".concat(String(a),". Expected to have a value of type ").concat(String(i),"."))}return{name:t.name,type:r,defaultValue:n}}))}function z(e){return new Set(["ID","String","Boolean","Int","Float"]).has(e)}e.exports={create:function(e,t,n){var r=[];return n&&n.forEach((function(e){m(e,{noLocation:!0}).definitions.forEach((function(e){s(e)&&r.push(e)}))})),t&&t.forEach((function(e){e.definitions.forEach((function(e){s(e)&&r.push(e)}))})),new W(new Q(e,r))}}},function(e,t,n){"use strict";var r=n(8),a=n(80),i=n(47),o={allowImportExportEverywhere:!0,allowReturnOutsideFunction:!0,allowSuperOutsideMethod:!0,sourceType:"module",plugins:["asyncGenerators","classProperties",["decorators",{decoratorsBeforeExport:!0}],"doExpressions","dynamicImport","exportExtensions",["flow",{enums:!0}],"functionBind","functionSent","jsx","nullishCoalescingOperator","objectRestSpread","optionalChaining","optionalCatchBinding"],strictMode:!1};var s={comments:!0,end:!0,leadingComments:!0,loc:!0,name:!0,start:!0,trailingComments:!0,type:!0};function l(e){var t=e.quasis;return t&&1===t.length||function(e,t){if(!e){for(var n=arguments.length,r=new Array(n>2?n-2:0),a=2;a<n;a++)r[a-2]=arguments[a];throw new Error(i.format.apply(i,[t].concat(r)))}}(!1,"FindGraphQLTags: Substitutions are not allowed in graphql tags."),t[0]}function u(e,t){var n=t[e.type];null==n?function(e,t){for(var n in e)if(!s[n]){var r=e[n];r&&"object"==typeof r&&"string"==typeof r.type?u(r,t):Array.isArray(r)&&r.forEach((function(e){e&&"object"==typeof e&&"string"==typeof e.type&&u(e,t)}))}}(e,t):n(e)}e.exports={find:r.instrument((function(e){var t=[];return u(a.parse(e,o),{TaggedTemplateExpression:function(e){var n,r,a;"Identifier"===(a=e.tag).type&&"graphql"===a.name&&t.push({keyName:null,template:e.quasi.quasis[0].value.raw,sourceLocationOffset:(n=e.quasi,r=l(n).loc.start,{line:r.line,column:r.column+1})})}}),t}),"FindGraphQLTags.find")}},function(e,t,n){"use strict";var r=n(5),a=n(49).DEFAULT_HANDLE_KEY;function i(e,t,n,a){var c=t.selections;if(null==c)return"";var f=c.map((function(t){return function e(t,n,a,c){var f,d,p=null!==(f=null==c?void 0:c.parentDirectives)&&void 0!==f?f:"",m=!0===(null==c?void 0:c.isClientExtension);if("LinkedField"===n.kind)d=o(t,n,{parentDirectives:p,isClientExtension:m}),d+=i(t,n,a+"  ",{isClientExtension:m});else if("ModuleImport"===n.kind)d=n.selections.map((function(n){return e(t,n,a,{parentDirectives:p,isClientExtension:m})})).join("\n"+a+"  ");else if("ScalarField"===n.kind)d=o(t,n,{parentDirectives:p,isClientExtension:m});else if("InlineFragment"===n.kind)d="",m&&(d+="# "),d+="... on "+t.getTypeString(n.typeCondition),d+=p,d+=s(t,n.directives),d+=i(t,n,a+"  ",{isClientExtension:m});else if("FragmentSpread"===n.kind)d="",m&&(d+="# "),d+="..."+n.name,d+=p,d+=function(e,t){var n=l(e,t);if(!n.length)return"";return" @arguments".concat(n)}(t,n.args),d+=s(t,n.directives);else if("InlineDataFragmentSpread"===n.kind)d="# ".concat(n.name," @inline")+"\n".concat(a).concat("  ","...")+p+i(t,n,a+"  ",{});else if("Condition"===n.kind){var h=u(t,n.condition,null);null==h&&r(!1,"IRPrinter: Expected a variable for condition, got a literal `null`.");var v=n.passingValue?" @include":" @skip";v+="(if: "+h+")",v+=p;var y=n.selections.map((function(n){return e(t,n,a,{parentDirectives:v,isClientExtension:m})}));d=y.join("\n"+a+"  ")}else if("Stream"===n.kind){var g,b,T,S=p;if(S+=' @stream(label: "'.concat(n.label,'"'),null!==n.if)S+=", if: ".concat(null!==(g=u(t,n.if,null))&&void 0!==g?g:"");if(null!==n.initialCount)S+=", initial_count: ".concat(null!==(b=u(t,n.initialCount,null))&&void 0!==b?b:"");if(null!==n.useCustomizedBatch)S+=", use_customized_batch: ".concat(null!==(T=u(t,n.useCustomizedBatch,null))&&void 0!==T?T:"false");S+=")";var w=n.selections.map((function(n){return e(t,n,a,{parentDirectives:S,isClientExtension:m})}));d=w.join("\n  ")}else if("Defer"===n.kind){var _,k=p;if(k+=' @defer(label: "'.concat(n.label,'"'),null!==n.if)k+=", if: ".concat(null!==(_=u(t,n.if,null))&&void 0!==_?_:"");if(k+=")",n.selections.every((function(e){return"InlineFragment"===e.kind||"FragmentSpread"===e.kind}))){var F=n.selections.map((function(n){return e(t,n,a,{parentDirectives:k,isClientExtension:m})}));d=F.join("\n  ")}else d="..."+k,d+=i(t,n,a+"  ",{isClientExtension:m})}else"ClientExtension"===n.kind?(!1!==m&&r(!1,"IRPrinter: Did not expect to encounter a ClientExtension node as a descendant of another ClientExtension node."),d="# Client-only selections:\n"+a+"  "+n.selections.map((function(n){return e(t,n,a,{parentDirectives:p,isClientExtension:!0})})).join("\n"+a+"  ")):r(!1,"IRPrinter: Unknown selection kind `%s`.",n.kind);return d}(e,t,n,a)}));return f.length?" {\n".concat(n+"  ").concat(f.join("\n"+n+"  "),"\n").concat(n).concat(!0===(null==a?void 0:a.isClientExtension)?"# ":"","}"):""}function o(e,t,n){var r,i=null!==(r=null==n?void 0:n.parentDirectives)&&void 0!==r?r:"";return(!0===(null==n?void 0:n.isClientExtension)?"# ":"")+(t.alias===t.name?t.name:t.alias+": "+t.name)+l(e,t.args)+i+s(e,t.directives)+function(e,t){if(!t.handles)return"";var n=t.handles.map((function(t){var n=t.key===a?"":', key: "'.concat(t.key,'"'),r=null==t.filters?"":", filters: ".concat(JSON.stringify(Array.from(t.filters).sort())),i=null==t.handleArgs?"":", handleArgs: ".concat(l(e,t.handleArgs));return'@__clientField(handle: "'.concat(t.name,'"').concat(n).concat(r).concat(i,")")}));return n.length?" "+n.join(" "):""}(e,t)}function s(e,t){var n=t.map((function(t){return"@"+t.name+l(e,t.args)}));return n.length?" "+n.join(" "):""}function l(e,t){var n=[];return t.forEach((function(t){var r=u(e,t.value,t.type);null!=r&&n.push(t.name+": "+r)})),n.length?"("+n.join(", ")+")":""}function u(e,t,n){if(null!=n&&e.isNonNull(n)&&(n=e.getNullableType(n)),"Variable"===t.kind)return"$"+t.variableName;if("ObjectValue"===t.kind){var a=null!=n?e.asInputObjectType(n):null;return"{"+t.fields.map((function(t){var n=null!=a&&e.hasField(a,t.name)?e.getFieldConfig(e.expectField(a,t.name)):null,r=u(e,t.value,null==n?void 0:n.type);return null==r?null:t.name+": "+r})).filter(Boolean).join(", ")+"}"}if("ListValue"===t.kind){n&&e.isList(n)||r(!1,"GraphQLIRPrinter: Need a type in order to print arrays.");var i=e.getListItemType(n);return"[".concat(t.items.map((function(t){return u(e,t,i)})).join(", "),"]")}return null!=t.value?c(e,t.value,n):null}function c(e,t,n){var a,i,o;if(null==t)return null!==(a=JSON.stringify(t))&&void 0!==a?a:"null";if(null!=n&&e.isNonNull(n)&&(n=e.getNullableType(n)),n&&e.isEnum(n)){var s,l=e.serialize(e.assertEnumType(n),t);return null==l&&"string"==typeof t&&(l=t),"string"!=typeof l&&r(!1,"IRPrinter: Expected value of type %s to be a valid enum value, got `%s`.",e.getTypeString(n),null!==(s=JSON.stringify(t))&&void 0!==s?s:"null"),l}if(n&&(e.isId(n)||e.isInt(n)))return null!==(i=JSON.stringify(t))&&void 0!==i?i:"";if(n&&e.isScalar(n)){var u,f=e.serialize(e.assertScalarType(n),t);return null!==(u=JSON.stringify(f))&&void 0!==u?u:""}if(Array.isArray(t)){n&&e.isList(n)||r(!1,"IRPrinter: Need a type in order to print arrays.");var d=e.getListItemType(n);return"["+t.map((function(t){return c(e,t,d)})).join(", ")+"]"}if(n&&e.isList(n)&&null!=t)return c(e,t,e.getListItemType(n));if("object"==typeof t&&null!=t){var p=[];n&&e.isInputObject(n)||r(!1,"IRPrinter: Need an InputObject type to print objects.");var m=e.assertInputObjectType(n);for(var h in t)if(t.hasOwnProperty(h)){var v=e.getFieldConfig(e.expectField(m,h));p.push(h+": "+c(e,t[h],v.type))}return"{"+p.join(", ")+"}"}return null!==(o=JSON.stringify(t))&&void 0!==o?o:"null"}e.exports={print:function(e,t){switch(t.kind){case"Fragment":return"fragment ".concat(t.name," on ").concat(e.getTypeString(t.type))+function(e,t){var n;return t.forEach((function(t){if("LocalArgumentDefinition"===t.kind){n=n||[];var r="".concat(t.name,': {type: "').concat(e.getTypeString(t.type),'"');null!=t.defaultValue&&(r+=", defaultValue: ".concat(c(e,t.defaultValue,t.type))),r+="}",n.push(r)}})),n&&n.length?" @argumentDefinitions(\n".concat("  ").concat(n.join("\n  "),"\n)"):""}(e,t.argumentDefinitions)+s(e,t.directives)+i(e,t,"",{})+"\n";case"Root":return"".concat(t.operation," ").concat(t.name)+function(e,t){var n=t.map((function(t){var n="$".concat(t.name,": ").concat(e.getTypeString(t.type));return null!=t.defaultValue&&(n+=" = "+c(e,t.defaultValue,t.type)),n}));return n.length?"(\n".concat("  ").concat(n.join("\n  "),"\n)"):""}(e,t.argumentDefinitions)+s(e,t.directives)+i(e,t,"",{})+"\n";case"SplitOperation":return"SplitOperation ".concat(t.name," on ").concat(e.getTypeString(t.type))+i(e,t,"",{})+"\n";default:r(!1,"IRPrinter: Unsupported IR node `%s`.",t.kind)}},printField:o,printArguments:l,printDirectives:s}},function(e,t,n){"use strict";var r=n(0),a=r(n(3)),i=r(n(6)),o=n(35),s=n(8),l=n(53),u=n(58);var c={Root:0,SplitOperation:1,Fragment:2};function f(e,t){var n=e.getRoot(t);return u(n,e).documents().sort((function(e,t){return e.kind!==t.kind?c[e.kind]-c[t.kind]:e.name<t.name?-1:1})).map((function(t){return o.print(e.getSchema(),t)})).join("\n")}e.exports=function(e,t,n){return s.run("GraphQLCompiler.compile",(function(){var r=function(e,t,n){return e.applyTransforms([].concat((0,i.default)(t.commonTransforms),(0,i.default)(t.fragmentTransforms)),n)}(e,t,n),o=function(e,t,n){return e.applyTransforms([].concat((0,i.default)(t.commonTransforms),(0,i.default)(t.queryTransforms),(0,i.default)(t.printTransforms)),n)}(e,t,n),s=function(e,t,n){return e.applyTransforms([].concat((0,i.default)(t.commonTransforms),(0,i.default)(t.queryTransforms),(0,i.default)(t.codegenTransforms)),n)}(e,t,n);return function(e,t,n,r){var i,o=[],s=e.getSchema(),u=(0,a.default)(r.documents());try{for(u.s();!(i=u.n()).done;){var c=i.value;if("Root"===c.kind){var d=t.getRoot(c.name),p={kind:"Request",fragment:{kind:"Fragment",argumentDefinitions:d.argumentDefinitions,directives:d.directives,loc:{kind:"Derived",source:c.loc},metadata:null,name:d.name,selections:d.selections,type:d.type},id:null,loc:c.loc,metadata:c.metadata||{},name:d.name,root:c,text:f(n,d.name)};o.push([p,l.generate(s,p)])}else o.push([c,l.generate(s,c)])}}catch(e){u.e(e)}finally{u.f()}var m,h=(0,a.default)(t.documents());try{for(h.s();!(m=h.n()).done;){var v=m.value;"Fragment"===v.kind&&o.push([v,l.generate(s,v)])}}catch(e){h.e(e)}finally{h.f()}return o}(e,r,o,s)}))}},function(e,t,n){"use strict";var r=n(0),a=r(n(3)),i=r(n(2)),o=r(n(6)),s=n(61),l=n(20),u=n(63),c=n(64),f=n(8),d=n(65),p=n(66),m=n(67),h=(n(24),n(21),n(68)),v=h.anyTypeAlias,y=h.declareExportOpaqueType,g=h.exactObjectTypeAnnotation,b=h.exportType,T=h.exportTypes,S=h.importTypes,w=h.inexactObjectTypeAnnotation,_=h.intersectionTypeAnnotation,k=h.lineComments,F=h.readOnlyArrayOfType,E=h.readOnlyObjectTypeProperty,x=h.unionTypeAnnotation,C=n(100),N=C.transformInputType,D=C.transformScalarType,I=n(101).default,A=n(39),R=n(5),O=n(15);function M(e,t,n,r,a){var i=t.key,o=t.schemaName,s=t.value,l=t.conditional,u=t.nodeType,c=t.nodeSelections;"__typename"===o&&a?s=A.stringLiteralTypeAnnotation(a):u&&(s=D(e,u,n,V(e,[Array.from(O(c).values())],n,r)));var f=E(i,s);return l&&(f.optional=!0),f}var L=function(e){return"__typename"===e.schemaName},j=function(e){return e.some(L)};function V(e,t,n,r,a){var s=new Map,l={};$(t).forEach((function(e){var t=e.concreteType;if(t){var n;l[t]=null!==(n=l[t])&&void 0!==n?n:[],l[t].push(e)}else{var r=s.get(e.key);s.set(e.key,r?q(e,r):e)}}));var u=[];if(Object.keys(l).length>0&&function(e){return e.every(L)}(Array.from(s.values()))&&(j(Array.from(s.values()))||Object.keys(l).every((function(e){return j(l[e])}))))!function(){var t=new Set,a=function(a){u.push(J([].concat((0,o.default)(Array.from(s.values())),(0,o.default)(l[a]))).map((function(i){return"__typename"===i.schemaName&&t.add(i.key),M(e,i,n,r,a)})))};for(var i in l)a(i);u.push(Array.from(t).map((function(e){var t=E(e,A.stringLiteralTypeAnnotation("%other"));return t.leadingComments=k("This will never be '%other', but we need some","value in case none of the concrete values match."),t})))}();else{var c=K(Array.from(s.values()));for(var f in l)c=P(c,K(l[f].map((function(e){return(0,i.default)((0,i.default)({},e),{},{conditional:!0})}))));var d=J(Array.from(c.values())).map((function(t){return L(t)&&t.concreteType?M(e,(0,i.default)((0,i.default)({},t),{},{conditional:!1}),n,r,t.concreteType):M(e,t,n,r)}));u.push(d)}return x(u.map((function(e){return a&&e.push(E("$refType",A.genericTypeAnnotation(A.identifier(a)))),r?w(e):g(e)})))}function q(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return e?(0,i.default)((0,i.default)({},e),{},{nodeSelections:e.nodeSelections?P(e.nodeSelections,O(t.nodeSelections),n):null,conditional:e.conditional&&t.conditional}):n?(0,i.default)((0,i.default)({},t),{},{conditional:!0}):t}function P(e,t){var n,r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],i=new Map,o=(0,a.default)(e.entries());try{for(o.s();!(n=o.n()).done;){var s=n.value,l=s[0],u=s[1];i.set(l,u)}}catch(e){o.e(e)}finally{o.f()}var c,f=(0,a.default)(t.entries());try{for(f.s();!(c=f.n()).done;){var d=c.value,p=d[0],m=d[1];i.set(p,q(e.get(p),m,r))}}catch(e){f.e(e)}finally{f.f()}return i}function U(e){return $(e.selections)}function G(e,t,n){var r,a=null!=(null===(r=t.metadata)||void 0===r?void 0:r.required)?e.getNonNullType(t.type):t.type;return[{key:t.alias,schemaName:t.name,value:D(e,a,n)}]}function B(e,t){var n,r;if(null!=(null===(n=t.metadata)||void 0===n?void 0:n.required))return e.getNonNullType(t.type);if(!0===(null===(r=t.metadata)||void 0===r?void 0:r.childrenCanBubbleNull)){if(e.isList(t.type))return e.mapListItemType(t.type,(function(t){return e.getNullableType(t)}));if(e.isNonNull(t.type)){var a=e.getNullableType(t.type);return e.isList(a)?e.getNonNullType(e.mapListItemType(a,(function(t){return e.getNullableType(t)}))):a}return t.type}return t.type}function W(e,t){return[{key:t.alias,schemaName:t.name,nodeType:B(e,t),nodeSelections:K($(t.selections),!0)}]}function Q(e,t,n,r){var a=t.key,i=t.schemaName,o=t.value,s=t.conditional,l=t.nodeType,u=t.nodeSelections;if("ModuleImport"===t.kind)return A.objectTypeSpreadProperty(A.genericTypeAnnotation(A.identifier(a)));"__typename"===i&&r?o=A.stringLiteralTypeAnnotation(r):l&&(o=D(e,l,n,H(e,[Array.from(O(u).values())],n,e.isAbstractType(l)||e.isWrapper(l)?null:e.getTypeString(l))));var c=E(a,o);return s&&(c.optional=!0),c}function H(e,t,n,r){var a=[],i={};$(t).forEach((function(e){var t,n=e.concreteType;n?(i[n]=null!==(t=i[n])&&void 0!==t?t:[],i[n].push(e)):a.push(e)}));var o=[];if(Object.keys(i).length){var s=K(a),l=function(t){var r=Array.from(P(s,K(i[t]),!1).values());o.push(g(r.map((function(r){return Q(e,r,n,t)})))),z(o,r,e,n,t)};for(var u in i)l(u)}return a.length>0&&(o.push(g(a.map((function(t){return Q(e,t,n,r)})))),z(o,a,e,n,r)),x(o)}function z(e,t,n,r,a){var i=t.find((function(e){return"ModuleImport"===e.kind}));i&&(r.runtimeImports.add("Local3DPayload"),e.push(A.genericTypeAnnotation(A.identifier("Local3DPayload"),A.typeParameterInstantiation([A.stringLiteralTypeAnnotation(O(i.documentName)),g(t.filter((function(e){return"js"!==e.schemaName})).map((function(e){return Q(n,e,r,a)})))]))))}function K(e,t){var n=new Map;return e.forEach((function(e){var r=t&&e.concreteType?"".concat(e.key,"::").concat(e.concreteType):e.key,a=n.get(r);n.set(r,a?q(a,e):e)})),n}function $(e){var t=[];return e.forEach((function(e){t.push.apply(t,(0,o.default)(e))})),t}function J(e){var t=[],n=[];if(e.forEach((function(e){e.ref?n.push(e.ref):t.push(e)})),n.length>0){var r=_(n.map((function(e){return A.genericTypeAnnotation(A.identifier(Z(e)))})));t.push({key:"$fragmentRefs",conditional:!1,value:r})}return t}function X(e){var t=[];if(e.usedFragments.size>0){var n,r=Array.from(e.usedFragments).sort(),i=(0,a.default)(r);try{for(i.s();!(n=i.n()).done;){var o=n.value,s=Z(o);e.generatedFragments.has(o)||(e.useHaste?t.push(S([s],o+".graphql")):e.useSingleArtifactDirectory?t.push(S([s],"./"+o+".graphql")):t.push(v(s)))}}catch(e){i.e(e)}finally{i.f()}}return t}function Y(e,t){var n=t.enumsHasteModule,r=t.usedEnums,a=t.noFutureProofEnums,i=Object.keys(r).sort();return 0===i.length?[]:"string"==typeof n?[S(i,n)]:"function"==typeof n?i.map((function(e){return S([e],n(e))})):i.map((function(t){var n=[].concat(e.getEnumValues(r[t]));return n.sort(),a||n.push("%future added value"),b(t,A.unionTypeAnnotation(n.map((function(e){return A.stringLiteralTypeAnnotation(e)}))))}))}function Z(e){return"".concat(e,"$ref")}function ee(e){return"".concat(e,"$fragmentType")}var te=[p.transform,u.transform,c.transform,m.transform,s.transformWithOptions({}),d.transform],ne="raw_response_type";e.exports={generate:f.instrument((function(e,t,n){var r=l.visit(t,function(e,t){var n={customScalars:t.customScalars,enumsHasteModule:t.enumsHasteModule,generatedFragments:new Set,generatedInputObjectTypes:{},optionalInputFields:t.optionalInputFields,usedEnums:{},usedFragments:new Set,useHaste:t.useHaste,useSingleArtifactDirectory:t.useSingleArtifactDirectory,noFutureProofEnums:t.noFutureProofEnums,matchFields:new Map,runtimeImports:new Set};return{leave:{Root:function(r){var s,u=function(e,t,n){return b("".concat(t.name,"Variables"),g(t.argumentDefinitions.map((function(t){var r=A.objectTypeProperty(A.identifier(t.name),N(e,t.type,n));return e.isNonNull(t.type)||(r.optional=!0),r}))))}(e,r,n),c=function(e){return Object.keys(e.generatedInputObjectTypes).map((function(t){var n=e.generatedInputObjectTypes[t];return"string"==typeof n&&R(!1,"RelayCompilerFlowGenerator: Expected input object type to have been defined before calling `generateInputObjectTypes`"),b(t,n)}))}(n),f=V(e,r.selections,n,!1);!0===(null===(s=r.metadata)||void 0===s?void 0:s.childrenCanBubbleNull)&&(f=A.nullableTypeAnnotation(f));var d,p=b("".concat(r.name,"Response"),f),m=[A.objectTypeProperty(A.identifier("variables"),A.genericTypeAnnotation(A.identifier("".concat(r.name,"Variables")))),A.objectTypeProperty(A.identifier("response"),A.genericTypeAnnotation(A.identifier("".concat(r.name,"Response"))))],h=t.normalizationIR;h&&r.directives.some((function(e){return e.name===ne}))&&(d=l.visit(h,function(e,t){return{leave:{Root:function(n){return b("".concat(n.name,"RawResponse"),H(e,n.selections,t,null))},InlineFragment:function(t){var n=t.typeCondition;return $(t.selections).map((function(t){return e.isAbstractType(n)?t:(0,i.default)((0,i.default)({},t),{},{concreteType:e.getTypeString(n)})}))},ScalarField:function(n){return G(e,n,t)},ClientExtension:function(e){return $(e.selections).map((function(e){return(0,i.default)((0,i.default)({},e),{},{conditional:!0})}))},LinkedField:function(t){return W(e,t)},Condition:U,Defer:U,Stream:U,ModuleImport:function(n){return function(e,t,n){var r=t.selections,a=t.name,i=r.filter((function(e){return e.length&&"js"===e[0].schemaName})).map((function(e){return e[0]}));if(!n.matchFields.has(a)){var s=H(e,t.selections.filter((function(e){return e.length>1||"js"!==e[0].schemaName})),n,null);n.matchFields.set(a,s)}return[].concat((0,o.default)(i),[{key:a,kind:"ModuleImport",documentName:t.key}])}(e,n,t)},FragmentSpread:function(e){R(!1,"A fragment spread is found when traversing the AST, make sure you are passing the codegen IR")}}}}(e,n)));var v=function(e,t){if(!(null==t?void 0:t.isRefetchableQuery)||!e.useHaste&&!e.useSingleArtifactDirectory)return null;var n=null==t?void 0:t.derivedFrom;if(null!=n&&"string"==typeof n)return n;return null}(n,r.metadata);null!=v&&n.runtimeImports.add("FragmentReference");var T,w,_,k=[];if(n.runtimeImports.size&&k.push(S(Array.from(n.runtimeImports).sort(),"relay-runtime")),k.push.apply(k,(0,o.default)(v?(T=v,w=Z(T),_=ee(T),[y(w,"FragmentReference"),y(_,w)]):X(n)).concat((0,o.default)(Y(e,n)),(0,o.default)(c),[u,p])),d){var F,E=(0,a.default)(n.matchFields);try{for(E.s();!(F=E.n()).done;){var x=F.value,C=x[0],D=x[1];k.push(b(C,D))}}catch(e){E.e(e)}finally{E.f()}m.push(A.objectTypeProperty(A.identifier("rawResponse"),A.genericTypeAnnotation(A.identifier("".concat(r.name,"RawResponse"))))),k.push(d)}return k.push(b(r.name,g(m))),A.program(k)},Fragment:function(t){var r,a=$(t.selections),s=a.filter((function(e){return e.concreteType})).length;a=a.map((function(n){return s<=1&&L(n)&&!e.isAbstractType(t.type)?[(0,i.default)((0,i.default)({},n),{},{concreteType:e.getTypeString(t.type)})]:[n]})),n.generatedFragments.add(t.name);var l,u=function(e,t){var n=Z(e),r=ee(e);if(t)return[S([n,r],t),T([n,r])];return[y(n,"FragmentReference"),y(r,n)]}(t.name,function(e,t){var n,r;if(!e.useHaste&&!e.useSingleArtifactDirectory)return;var a=null===(n=t.find((function(e){return"refetchable"===e.name})))||void 0===n?void 0:n.args;if(!a)return;var i=a.find((function(e){return"Argument"===e.kind&&"queryName"===e.name}));i&&i.value&&"Literal"===i.value.kind&&"string"==typeof i.value.value&&(r=i.value.value,e.useHaste||(r="./"+r),r+=".graphql");return r}(n,t.directives)),c=(l=t.name,"".concat(l,"$key")),f=E("$data",A.genericTypeAnnotation(A.identifier("".concat(t.name,"$data"))));f.optional=!0;var d=E("$fragmentRefs",A.genericTypeAnnotation(A.identifier(Z(t.name)))),p=function(e){return Boolean(e.metadata&&e.metadata.plural)}(t),m=w([f,d]),h=function(e){return"".concat(e,"$data")}(t.name),v=A.genericTypeAnnotation(A.identifier(t.name)),g=null!=t.metadata&&!1===t.metadata.mask,_=V(e,a,n,g,g?void 0:Z(t.name)),k=p?F(_):_;return!0===(null===(r=t.metadata)||void 0===r?void 0:r.childrenCanBubbleNull)&&(k=A.nullableTypeAnnotation(k)),n.runtimeImports.add("FragmentReference"),A.program([].concat((0,o.default)(X(n)),(0,o.default)(Y(e,n)),[S(Array.from(n.runtimeImports).sort(),"relay-runtime")],(0,o.default)(u),[b(t.name,k),b(h,v),b(c,p?F(m):m)]))},InlineFragment:function(t){return $(t.selections).map((function(n){return e.isAbstractType(t.typeCondition)?(0,i.default)((0,i.default)({},n),{},{conditional:!0}):(0,i.default)((0,i.default)({},n),{},{concreteType:e.getTypeString(t.typeCondition)})}))},Condition:function(e){return $(e.selections).map((function(e){return(0,i.default)((0,i.default)({},e),{},{conditional:!0})}))},ScalarField:function(t){return G(e,t,n)},LinkedField:function(t){return W(e,t)},ModuleImport:function(t){return[{key:"__fragmentPropName",conditional:!0,value:D(e,e.expectStringType(),n)},{key:"__module_component",conditional:!0,value:D(e,e.expectStringType(),n)},{key:"__fragments_"+t.name,ref:t.name}]},FragmentSpread:function(e){return n.usedFragments.add(e.name),[{key:"__fragments_"+e.name,ref:e.name}]}}}}(e,n));return I(r).code}),"RelayFlowGenerator.generate"),transforms:te,SCHEMA_EXTENSION:"directive @".concat(ne," on QUERY | MUTATION | SUBSCRIPTION")}},function(e,t,n){"use strict";e.exports=function(e){return"".concat(e,"$normalization")}},function(e,t){e.exports=require("@babel/types")},function(e,t,n){"use strict";var r=n(5);e.exports=function e(t){switch(t.kind){case"Variable":return{variable:t.variableName};case"Literal":return{value:t.value};case"ListValue":return{list:t.items.map((function(t){return e(t)}))};case"ObjectValue":return{object:t.fields.map((function(t){return{name:t.name,value:e(t.value)}}))};default:r(!1,"getIdentifierForArgumentValue(): Unsupported AST kind `%s`.",t.kind)}}},function(e,t,n){"use strict";var r=n(0),a=n(9),i=r(n(3)),o=r(n(6)),s=n(32),l=n(42),u=n(23),c=n(8),f=n(5),d=n(11),p=n(33).create,m=n(17).Map,h=function(){function e(e){var t=this;for(var n in this.parsers={},this.parserConfigs=e.parserConfigs,this.writerConfigs=e.writerConfigs,this.onlyValidate=e.onlyValidate,this.onComplete=e.onComplete,this._reporter=e.reporter,this._sourceControl=e.sourceControl,this.parserWriters={},e.parserConfigs)this.parserWriters[n]=new Set;var r=function(n){var r=e.writerConfigs[n];r.baseParsers&&r.baseParsers.forEach((function(e){return t.parserWriters[e].add(n)})),t.parserWriters[r.parser].add(n)};for(var a in e.writerConfigs)r(a)}var t=e.prototype;return t.compileAll=function(){var e=a((function*(){for(var e in this.parsers={},this.parserConfigs)try{yield this.parseEverything(e)}catch(e){return this._reporter.reportError("CodegenRunner.compileAll",e),"ERROR"}var t=!1;for(var n in this.writerConfigs){var r=yield this.write(n);if("ERROR"===r)return"ERROR";"HAS_CHANGES"===r&&(t=!0)}return t?"HAS_CHANGES":"NO_CHANGES"}));return function(){return e.apply(this,arguments)}}(),t.compile=function(){var e=a((function*(e){var t=this,n=this.writerConfigs[e],r=[n.parser];return n.baseParsers&&n.baseParsers.forEach((function(e){return r.push(e)})),yield c.asyncContext("CodegenRunner:parseEverything",(function(){return Promise.all(r.map((function(e){return t.parseEverything(e)})))})),yield this.write(e)}));return function(t){return e.apply(this,arguments)}}(),t.getDirtyWriters=function(e){var t=this;return c.asyncContext("CodegenRunner:getDirtyWriters",a((function*(){var n=new Set;for(var r in t.writerConfigs){var o,s=t.writerConfigs[r],l=(0,i.default)(e);try{for(l.s();!(o=l.n()).done;){var f=o.value;s.isGeneratedFile(f)&&n.add(r)}}catch(e){l.e(e)}finally{l.f()}}return yield Promise.all(Object.keys(t.parserConfigs).map((function(r){return c.waitFor("Watchman:query",a((function*(){var a=new u,i=t.parserConfigs[r],o=yield a.watchProject(i.baseDir),s=e.map((function(e){return d.relative(i.baseDir,e)})),l={expression:["allof",i.watchmanExpression,["name",s,"wholename"]],fields:["exists"],relative_root:o.relativePath},c=yield a.command("query",o.root,l);a.end(),c.files.length>0&&t.parserWriters[r].forEach((function(e){return n.add(e)}))})))}))),n})))},t.parseEverything=function(){var e=a((function*(e){if(!this.parsers[e]){var t=this.parserConfigs[e];this.parsers[e]=t.getParser(t.baseDir);var n,r=t.getFileFilter?t.getFileFilter(t.baseDir):v;if(t.filepaths&&t.watchmanExpression)throw new Error("Provide either `watchmanExpression` or `filepaths` but not both.");if(t.watchmanExpression)n=yield l.queryFiles(t.baseDir,t.watchmanExpression,r);else{if(!t.filepaths)throw new Error("Either `watchmanExpression` or `filepaths` is required to query files");n=yield l.queryFilepaths(t.baseDir,t.filepaths,r)}this.parseFileChanges(e,n)}}));return function(t){return e.apply(this,arguments)}}(),t.parseFileChanges=function(e,t){var n=this;return c.run("CodegenRunner.parseFileChanges",(function(){n.parsers[e].parseFiles(t)}))},t.write=function(e){var t=this;return c.asyncContext("CodegenRunner.write",a((function*(){try{t._reporter.reportMessage("\nWriting ".concat(e));var n=t.writerConfigs[e],r=n.writeFiles,a=n.parser,u=n.baseParsers,h=n.isGeneratedFile,v=m();u&&u.forEach((function(e){null==t.parsers[e]&&f(!1,"Trying to access an uncompiled base parser config: %s",e),v=v.merge(t.parsers[e].documents())}));var y=t.parserConfigs[a],g=y.baseDir,b=y.generatedDirectoriesWatchmanExpression,T=[];if(b)T=(yield l.queryDirectories(g,b)).map((function(e){return d.join(g,e)}));var S,w=t.parsers[a].documents(),_=c.run("getSchema",(function(){return p(t.parserConfigs[a].getSchemaSource(),v.toArray(),t.parserConfigs[a].schemaExtensions)})),k=yield r({onlyValidate:t.onlyValidate,schema:_,documents:w,baseDocuments:v,generatedDirectories:T,sourceControl:t._sourceControl,reporter:t._reporter}),F=(0,i.default)(k.values());try{for(F.s();!(S=F.n()).done;){var E,x=S.value,C=[].concat((0,o.default)(x.changes.created),(0,o.default)(x.changes.updated),(0,o.default)(x.changes.deleted),(0,o.default)(x.changes.unchanged)),N=(0,i.default)(C);try{for(N.s();!(E=N.n()).done;){var D=E.value,I=x.getPath(D);h(I)||f(!1,"CodegenRunner: %s returned false for isGeneratedFile, but was in generated directory",I)}}catch(e){N.e(e)}finally{N.f()}}}catch(e){F.e(e)}finally{F.f()}var A=t.onComplete;null!=A&&A(Array.from(k.values()));var R=s.combineChanges(Array.from(k.values()));return t._reporter.reportMessage(s.formatChanges(R,{onlyValidate:t.onlyValidate})),s.hasChanges(R)?"HAS_CHANGES":"NO_CHANGES"}catch(e){return t._reporter.reportError("CodegenRunner.write",e),"ERROR"}})))},t.watchAll=function(){var e=a((function*(){for(var e in yield this.compileAll(),this.parserConfigs)yield this.watch(e)}));return function(){return e.apply(this,arguments)}}(),t.watch=function(){var e=a((function*(e){var t=this,n=this.parserConfigs[e];if(!n.watchmanExpression)throw new Error("`watchmanExpression` is required to watch files");var r=!0;yield l.watchCompile(n.baseDir,n.watchmanExpression,n.getFileFilter?n.getFileFilter(n.baseDir):v,function(){var n=a((function*(n){if(null==t.parsers[e]&&f(!1,"Trying to watch an uncompiled parser config: %s",e),r)r=!1;else{var a=[];t.parserWriters[e].forEach((function(e){return a.push(e)}));try{t.parsers[e]?t.parseFileChanges(e,n):yield t.parseEverything(e),yield Promise.all(a.map((function(e){return t.write(e)})))}catch(e){t._reporter.reportError("CodegenRunner.watch",e)}t._reporter.reportMessage("Watching for changes to ".concat(e,"..."))}}));return function(e){return n.apply(this,arguments)}}()),this._reporter.reportMessage("Watching for changes to ".concat(e,"..."))}));return function(t){return e.apply(this,arguments)}}(),e}();function v(e){return!0}e.exports=h},function(e,t,n){"use strict";var r=n(9),a=n(23),i=n(8),o=n(14),s=n(13),l=n(11);function u(){return(u=r((function*(e,t,n){return yield i.waitFor("Watchman:query",r((function*(){var r=new a(3),i=yield Promise.all([r.watchProject(e),f(r)]),o=i[0],s=i[1],l=yield r.command("query",o.root,{expression:t,fields:s,relative_root:o.relativePath});return r.end(),S(new Set,e,n,l.files)})))}))).apply(this,arguments)}function c(){return(c=r((function*(e,t){return yield i.waitFor("Watchman:query",r((function*(){var n=new a,r=yield n.watchProject(e),i=yield n.command("query",r.root,{expression:t,fields:["name"],relative_root:r.relativePath});return n.end(),i.files})))}))).apply(this,arguments)}function f(e){return d.apply(this,arguments)}function d(){return(d=r((function*(e){var t=["name","exists"];return(yield e.hasCapability("field-content.sha1hex"))&&t.push("content.sha1hex"),t}))).apply(this,arguments)}function p(){return(p=r((function*(e,t,n){var r=t.map((function(e){return{name:e,exists:!0,"content.sha1hex":null}}));return S(new Set,e,n,r)}))).apply(this,arguments)}function m(e,t,n){return h.apply(this,arguments)}function h(){return(h=r((function*(e,t,n){return yield i.waitFor("Watchman:subscribe",r((function*(){var r=new a,i=yield r.watchProject(e);yield v(r,i.root,i.relativePath,t,n)})))}))).apply(this,arguments)}function v(e,t,n,r,a){return y.apply(this,arguments)}function y(){return(y=r((function*(e,t,n,r,a){e.on("subscription",(function(e){"graphql-codegen"===e.subscription&&a(e)}));var i=yield f(e);yield e.command("subscribe",t,"graphql-codegen",{expression:r,fields:i,relative_root:n})}))).apply(this,arguments)}function g(e,t,n,r){return b.apply(this,arguments)}function b(){return(b=r((function*(e,t,n,r){var a=new Set;yield m(e,t,(function(t){t.files&&(a=S(a,e,n,t.files),r(a))}))}))).apply(this,arguments)}function T(){return(T=r((function*(e,t,n,a){var i=!1,o=!1,s=null;g(e,t,n,function(){var e=r((function*(e){if(o=!0,s=e,!i){for(i=!0;o;)o=!1,yield a(s);i=!1}}));return function(t){return e.apply(this,arguments)}}())}))).apply(this,arguments)}function S(e,t,n,r){var a=new Map;return e.forEach((function(e){e.exists&&a.set(e.relPath,e)})),r.forEach((function(e){var r,i,u=e.name,c=e.exists,f=e["content.sha1hex"],d=!c;if(!d){var p={exists:!0,relPath:u,hash:f||(r=l.join(t,u),i=s.readFileSync(r),o.createHash("sha1").update(i).digest("hex"))};n(p)?a.set(u,p):d=!0}d&&a.set(u,{exists:!1,relPath:u})})),new Set(a.values())}e.exports={queryDirectories:function(e,t){return c.apply(this,arguments)},queryFiles:function(e,t,n){return u.apply(this,arguments)},queryFilepaths:function(e,t,n){return p.apply(this,arguments)},watch:m,watchFiles:g,watchCompile:function(e,t,n,r){return T.apply(this,arguments)}}},function(e,t){e.exports=require("child_process")},function(e,t){e.exports=require("@babel/runtime/helpers/inheritsLoose")},function(e,t,n){"use strict";var r=n(79);var a=function(){function e(e){this._verbose=e.verbose,this._quiet=e.quiet}var t=e.prototype;return t.reportMessage=function(e){this._quiet||process.stdout.write(e+"\n")},t.reportTime=function(e,t){if(this._verbose&&!this._quiet){var n=0===t?r.gray(" <1ms"):t<1e3?r.blue((a=t+"ms",new Array(5-a.length+1).join(" ")+a)):r.red(Math.floor(t/10)/100+"s");process.stdout.write("  "+n+" "+r.gray(e)+" ["+r.blue(Math.round(process.memoryUsage().heapUsed/1024/1024)+"Mb")+"]\n")}var a},t.reportError=function(e,t){if(!this._quiet&&(process.stdout.write(r.red("ERROR:\n"+t.message+"\n")),this._verbose)){var n=t.stack.match(/^ {4}at .*$/gm);n&&process.stdout.write(r.gray("From: "+e+"\n"+n.join("\n")+"\n"))}},e}();e.exports=a},function(e,t,n){"use strict";var r=n(28),a=n(13),i=n(11),o=n(7),s=o.parse,l=o.Source;function u(e,t){var n=a.readFileSync(i.join(e,t.relPath),"utf8");return s(new l(n,t.relPath),{experimentalFragmentVariables:!0})}e.exports={parseFile:u,getParser:function(e){return new r({baseDir:e,parse:u})}}},function(e,t){e.exports=require("util")},function(e,t,n){"use strict";var r=n(0),a=r(n(3)),i=r(n(44)),o=r(n(82)),s=n(1),l=s.createUserError,u=s.createCompilerError,c=n(18).getName,f=function(e){function t(){return e.apply(this,arguments)||this}return(0,i.default)(t,e),t.from=function(e){var n,r=new t,i=(0,a.default)(e);try{for(i.s();!(n=i.n()).done;){var o=n.value,s=c(o),u=r.get(s);if(u)throw l("Duplicate node named '".concat(s,"'"),null,[o,u]);r.set(s,o)}}catch(e){i.e(e)}finally{i.f()}return r},t.fromSources=function(e){return t.from(e.nodes())},t.prototype.enforceGet=function(e){var t=this.get(e);if(!t)throw u("GraphQLNodeMap: expected to have a node named ".concat(e,"."));return t},t}((0,o.default)(Map));e.exports=f},function(e,t,n){"use strict";e.exports={DEFAULT_HANDLE_KEY:""}},function(e,t,n){"use strict";var r=n(34),a=n(51)(r.find);e.exports=a},function(e,t,n){"use strict";var r=n(0)(n(6)),a=n(28),i=n(7),o=n(8),s=n(13),l=n(5),u=n(11),c=n(52).memoizedFind,f=o.instrument(i.parse,"GraphQL.parse");e.exports=function(e,t){var n=c.bind(null,e);function o(e,t){var n=d(e,t);if(n)return n.document}function d(e,t){var a=u.join(e,t.relPath),o="";try{o=s.readFileSync(a,"utf8")}catch(e){l(!1,"RelaySourceModuleParser: Files should be filtered before passed to the parser, got unfiltered file `%s`.",t.relPath)}var c=[],d=[];return n(o,e,t).forEach((function(e){var n=new i.Source(e,t.relPath),a=f(n);a.definitions.length||l(!1,"RelaySourceModuleParser: Expected GraphQL text to contain at least one definition (fragment, mutation, query, subscription), got `%s`.",e),d.push(n.body),c.push.apply(c,(0,r.default)(a.definitions))})),{document:{kind:"Document",definitions:c},sources:d}}return{getParser:function(e){return new a({baseDir:e,parse:o})},getFileFilter:null!=t?t:function(e){return function(t){var n=u.join(e,t.relPath),r="";try{r=s.readFileSync(n,"utf8")}catch(e){return console.warn('RelaySourceModuleParser: Unable to read the file "'.concat(n,'". Looks like it was removed.')),!1}return r.indexOf("graphql")>=0}},parseFile:o,parseFileWithSources:d}}},function(e,t,n){"use strict";var r=n(83),a=n(85),i=n(7),o=n(11),s=n(47),l=new r("RelayFindGraphQLTags","v1");function u(e,t,n){var r=e(t,n),o=a(n);return r.forEach((function(e){return function(e,t,n){var r=e.template,a=e.keyName,o=e.sourceLocationOffset;i.parse(new i.Source(r,n,o)).definitions.forEach((function(e){if("OperationDefinition"===e.kind){null==e.name&&c(!1,"RelayFindGraphQLTags: In module `%s`, an operation requires a name.",t,e.kind);var n=e.name.value;n.match(/^(.*)(Mutation|Query|Subscription)$/)&&n.startsWith(t)||c(!1,'RelayFindGraphQLTags: Operation names in graphql tags must be prefixed with the module name and end in "Mutation", "Query", or "Subscription". Got `%s` in module `%s`.',n,t)}else if("FragmentDefinition"===e.kind){var r=e.name.value;null!=a?r!==t+"_"+a&&c(!1,"RelayFindGraphQLTags: Container fragment names must be `<ModuleName>_<propName>`. Got `%s`, expected `%s`.",r,t+"_"+a):r.startsWith(t)||c(!1,"RelayFindGraphQLTags: Fragment names in graphql tags must be prefixed with the module name. Got `%s` in module `%s`.",r,t)}}))}(e,o,n)})),r.map((function(e){return e.template}))}function c(e,t){if(!e){for(var n=arguments.length,r=new Array(n>2?n-2:0),a=2;a<n;a++)r[a-2]=arguments[a];throw new Error(s.format.apply(s,[t].concat(r)))}}e.exports={find:u,memoizedFind:function(e,t,n,r){return r.exists||c(!1,"RelayFindGraphQLTags: Called with non-existent file `%s`",r.relPath),l.getOrCompute(r.hash,u.bind(null,e,t,o.join(n,r.relPath)))}}},function(e,t,n){"use strict";var r=n(87),a=n(88),i=n(55),o=n(16),s=n(15),l=n(1).createCompilerError;e.exports={generate:function(e,t){var n;switch(t.kind){case"Fragment":return!0===(null===(n=t.metadata)||void 0===n?void 0:n.inlineData)?{kind:"InlineDataFragment",name:t.name}:a.generate(e,t);case"Request":return{fragment:a.generate(e,t.fragment),kind:"Request",operation:r.generate(e,t.root),params:null!=t.id?{id:t.id,metadata:i(t.metadata),name:t.name,operationKind:t.root.operation}:{cacheID:o(s(t.text)),metadata:i(t.metadata),name:t.name,operationKind:t.root.operation,text:t.text}};case"SplitOperation":return r.generate(e,t)}throw l("RelayCodeGenerator: Unknown AST kind '".concat(t.kind,"'."),[t.loc])}}},function(e,t,n){"use strict";e.exports=function e(t){if(null==t)return!1;switch(t.kind){case"Variable":return!0;case"Literal":return!1;case"ListValue":return t.items.some(e);case"ObjectValue":return t.fields.some(e);default:return t.kind,!1}}},function(e,t,n){"use strict";var r=n(0)(n(3));e.exports=function(e){if(null==e)return e;var t,n={},a=(0,r.default)(Object.keys(e).sort());try{for(a.s();!(t=a.n()).done;){var i=t.value;n[i]=e[i]}}catch(e){a.e(e)}finally{a.f()}return n}},function(e,t,n){"use strict";var r=n(0),a=n(9),i=r(n(3)),o=r(n(6)),s=n(29),l=n(32),u=n(19),c=n(8),f=n(25),d=n(36),p=n(7),m=n(5),h=n(16),v=n(15),y=n(11),g=n(59),b=n(60).getReaderSourceDefinitionName,T=n(10).isExecutableDefinitionAST;n(17).Map;e.exports={writeAll:function(e){var t=e.config,n=e.onlyValidate,r=e.baseDocuments,S=e.documents,w=e.schema,_=e.reporter,k=e.sourceControl,F=e.languagePlugin;return c.asyncContext("RelayFileWriter.writeAll",a((function*(){var e=function(e){e.baseDir;var t=e.baseDocuments,n=e.schema,r=e.compilerTransforms,a=e.documents,i=e.reporter,l=e.typeGenerator,c=s.convertASTDocumentsWithBase(n,t,a,f.transform),p=new u(n).addAll(c),m=p.applyTransforms(l.transforms,i),h=p.applyTransforms([].concat((0,o.default)(r.commonTransforms),(0,o.default)(r.queryTransforms)),i);return{artifacts:d(p,r,i),definitions:c,transformedQueryContext:h,transformedTypeContext:m}}({schema:w,baseDir:t.baseDir,baseDocuments:r.valueSeq().toArray(),compilerTransforms:t.compilerTransforms,documents:S.valueSeq().toArray(),reporter:_,typeGenerator:t.typeGenerator}),E=e.artifacts,x=e.transformedTypeContext,C=e.transformedQueryContext,N=new Set;r.forEach((function(e){e.definitions.forEach((function(e){T(e)&&e.name&&N.add(e.name.value)}))}));var D=E.filter((function(e){e[0];var t=e[1],n=b(t);return!N.has(n)})),I=new Map(D.map((function(e){e[0];var t=e[1];return["Request"===t.kind?t.params.name:t.name,t]}))),A=new Map,R=function(e){var t=v(I.get(e)),n=b(t),r=A.get(n);return r||m(!1,"RelayFileWriter: Could not determine source for definition: `%s`.",e),r};S.forEach((function(e,n){e.definitions.forEach((function(e){e.name&&A.set(e.name.value,{dir:y.join(t.baseDir,y.dirname(n)),ast:e})}))}));var O,M,L=new Map,j=function(e){var r=new l(e,{onlyValidate:n,filesystem:t.filesystem});return L.set(e,r),r},V=(0,i.default)(t.generatedDirectories||[]);try{for(V.s();!(O=V.n()).done;){var q=O.value;j(q)}}catch(e){V.e(e)}finally{V.f()}t.outputDir&&(M=j(t.outputDir));var P=function(e){if(M)return M;var t=y.join(R(e).dir,"__generated__"),n=L.get(t);return n||(n=j(t)),n},U=c.instrument(t.formatModule,"RelayFileWriter:formatModule"),G=t.persistQuery?c.instrumentWait(t.persistQuery,"RelayFileWriter:persistQuery"):null;try{yield Promise.all(D.map(function(){var e=a((function*(e){var n,r,a=e[0],i=e[1],o="Request"===i.kind?i.params.name:i.name;if(!N.has(o)){var s=x.get(o),l=s?t.typeGenerator.generate(w,s,{customScalars:t.customScalars,enumsHasteModule:t.enumsHasteModule,optionalInputFields:t.optionalInputFieldsForFlow,useHaste:t.useHaste,useSingleArtifactDirectory:!!t.outputDir,noFutureProofEnums:t.noFutureProofEnums,normalizationIR:"Request"===a.kind?a.root:void 0}):"",u=c.run("hashGraphQL",(function(){return h(p.print(R(o).ast))}));yield g(w,P(o),a,i,U,l,G,u,t.extension,t.printModuleDependency,null!==(n=t.repersist)&&void 0!==n&&n,null!==(r=t.writeQueryParameters)&&void 0!==r?r:function(){},F)}}));return function(t){return e.apply(this,arguments)}}()));var B=t.generateExtraFiles;B&&c.run("RelayFileWriter:generateExtraFiles",(function(){var e=t.outputDir;B((function(t){var n=t||e;n||m(!1,"RelayFileWriter: cannot generate extra files without specifying an outputDir in the config or passing it in.");var r=L.get(n);return r||(r=j(n)),r}),C,P)})),L.forEach((function(e){e.deleteExtraFiles(null==F?void 0:F.keepExtraFile)})),k&&!n&&(yield l.sourceControlAddRemove(k,Array.from(L.values())))}catch(e){var W;try{W=JSON.parse(e.message)}catch(e){}if(W&&"GraphQL2Exception"===W.name&&W.message)throw new Error("GraphQL error writing modules:\n"+W.message);throw new Error("Error writing modules:\n"+String(e.stack||e))}return L})))}}},function(e,t,n){"use strict";var r=n(0)(n(3)),a=n(1).createCompilerError,i=n(7),o=i.SchemaMetaFieldDef,s=i.TypeMetaFieldDef;function l(e,t,n){var r,a=e.getRawType(t),i=e.getQueryType(),l=null!=i&&e.areEqualTypes(a,i),u=e.isAbstractType(a)||e.isObject(a);if(l&&n===o.name)r=null!=i?e.getFieldByName(i,"__schema"):null;else if(l&&n===s.name)r=null!=i?e.getFieldByName(i,"__type"):null;else if(u&&"__typename"===n)r=e.getFieldByName(e.assertCompositeType(a),"__typename");else if(u&&"__id"===n)r=e.getFieldByName(e.assertCompositeType(a),"__id");else if(e.isInterface(a)||e.isObject(a)){var c=e.assertCompositeType(a);if(!e.hasField(c,n))return null;r=e.getFieldByName(c,n)}return r}function u(e){var t=e.name?e.name.value:null;if("string"!=typeof t)throw a("Expected ast node to have a 'name'.",null,[e]);return t}e.exports={getFieldDefinitionLegacy:function(e,t,n,a){var i,o=l(e,t,n);return o||(o=function(e,t,n,a){var i=e.getRawType(t);if(e.isAbstractType(i)&&a&&a.directives&&a.directives.some((function(e){return"fixme_fat_interface"===u(e)}))){var o,s,l=e.getPossibleTypes(e.assertAbstractType(i)),c=(0,r.default)(l);try{var f=function(){var t=s.value,r=e.getFieldByName(t,n);if(r&&(o=r,a&&a.arguments&&a.arguments.every((function(t){return null!=e.getFieldArgByName(r,u(t))}))))return"break"};for(c.s();!(s=c.n()).done;){if("break"===f())break}}catch(e){c.e(e)}finally{c.f()}return o}}(e,t,n,a)),null!==(i=o)&&void 0!==i?i:null},getFieldDefinitionStrict:l}},function(e,t,n){"use strict";var r=n(19),a=n(20).visit;e.exports=function(e,t){for(var n=[e],i=new r(t.getSchema()).add(e),o={FragmentSpread:function(e){!function(e){var r=e.name;if(!i.get(r)){var a=t.getFragment(r);i=i.add(a),n.push(a)}}(e)}};n.length;)a(n.pop(),o);return i}},function(e,t,n){"use strict";var r=n(0),a=n(9),i=r(n(2)),o=r(n(90)),s=n(31),l=n(91),u=n(92),c=n(5),f=n(16),d=n(12).RelayConcreteNode;function p(e){switch(e.kind){case d.FRAGMENT:return"ReaderFragment";case d.REQUEST:return"ConcreteRequest";case d.SPLIT_OPERATION:return"NormalizationSplitOperation";case d.INLINE_DATA_FRAGMENT:return"ReaderInlineDataFragment";default:c(!1,"Unexpected GeneratedNode kind: `%s`.",e.kind)}}function m(e){if(null==e||0===e.length)return null;if(/<<<<<|>>>>>/.test(e))return null;var t=e.match(/@relayHash (\w{32})\b/m);return t&&t[1]}function h(e){if(null==e||0===e.length)return null;if(/<<<<<|>>>>>/.test(e))return null;var t=e.match(/@relayRequestID (.+)/);return t?t[1]:null}e.exports=function(e,t,n,r,v,y,g,b,T){var S,w=arguments.length>9&&void 0!==arguments[9]?arguments[9]:l(),_=arguments.length>10?arguments[10]:void 0,k=arguments.length>11?arguments[11]:void 0,F=arguments.length>12?arguments[12]:void 0,E=r,x=g,C="Request"===E.kind?E.params.name:E.name,N=(null==F?void 0:F.getModuleName)?F.getModuleName(C):C+".graphql",D=N+"."+T,I="Request"===E.kind?"".concat(E.params.name,"$Parameters.").concat(T):null,A=p(E);return E.kind===d.REQUEST&&(S=null!=E.params.text?E.params.text:null),Promise.resolve().then(a((function*(){var r,a=null;if(E.kind===d.REQUEST){null==S&&c(!1,"writeRelayGeneratedFile: Expected `text` for operations to be set.");var l,p=E.params.metadata,g=(p.isRefetchableQuery,p.derivedFrom,(0,o.default)(p,["isRefetchableQuery","derivedFrom"]));if(null!=x){a=f(S);var T=null;if(!_){var F=t.read(D),C=m(F),R=h(F);a===C&&null!=R&&(T=R)}null==T&&(T=yield x(S)),l={id:T,metadata:g,name:E.params.name,operationKind:E.params.operationKind,text:null}}else l={cacheID:f(S),id:null,metadata:g,name:E.params.name,operationKind:E.params.operationKind,text:S};E=(0,i.default)((0,i.default)({},E),{},{params:l})}if(E.kind===d.SPLIT_OPERATION&&null!=(null===(r=E.metadata)||void 0===r?void 0:r.derivedFrom)){var O=E.metadata,M=(O.derivedFrom,(0,o.default)(O,["derivedFrom"]));E=(0,i.default)((0,i.default)({},E),{},{metadata:M})}var L=v({moduleName:N,documentType:A,definition:n,kind:E.kind,docText:S,typeText:y,hash:null!=a?"@relayHash ".concat(a):null,concreteText:s.postProcess(u(E),w),sourceHash:b,node:E,schema:e});return t.writeFile(D,L,_),k&&null!=I&&E.kind===d.REQUEST&&"query"===E.params.operationKind&&k(t,I,N,E.params),E})))}},function(e,t,n){"use strict";e.exports={getReaderSourceDefinitionName:function(e){var t,n,r="Request"===e.kind?[e.params.name,null===(t=e.params.metadata)||void 0===t?void 0:t.derivedFrom]:"SplitOperation"===e.kind?[e.name,null===(n=e.metadata)||void 0===n?void 0:n.derivedFrom]:[e.name,null],a=r[1];return"string"==typeof a?a:r[0]}}},function(e,t,n){"use strict";var r=n(0)(n(2)),a=n(4),i=n(93),o=n(62),s=n(1),l=s.createCompilerError,u=s.createUserError;function c(e,t){var n,i={isForCodegen:!(!t||!t.isForCodegen),parentType:null},o=(n=new Map,function(e,t){var a=this.getContext(),i=n.get(e);null==i&&(i=new Map,n.set(e,i));var o=t.parentType,s=i.get(o);if(null!=s)return s;var u="LinkedField"===e.kind||"Fragment"===e.kind||"Root"===e.kind||"SplitOperation"===e.kind?e.type:"InlineFragment"===e.kind?e.typeCondition:o;if(null==u)throw l("FlattenTransform: Expected a parent type.",[e.loc]);var c=new Map,d=f(a.getSchema(),c,e,t,u)?(0,r.default)((0,r.default)({},e),{},{selections:Array.from(c.values())}):e;t.parentType=u;var p=this.traverse(d,t);return t.parentType=o,i.set(o,p),p});return a.transform(e,{Condition:o,Defer:o,Fragment:o,InlineDataFragmentSpread:o,InlineFragment:o,LinkedField:o,ModuleImport:o,Root:o,SplitOperation:o},(function(){return i}))}function f(e,t,n,a,i){var s=!1;return n.selections.forEach((function(n){if("InlineFragment"===n.kind&&function(e,t,n,r){return e.areEqualTypes(t.typeCondition,e.getRawType(r))&&(n.isForCodegen||0===t.directives.length)}(e,n,a,i))return s=!0,void f(e,t,n,a,i);var c=o(e,n),h=t.get(c);if(h)if(s=!0,"InlineFragment"===h.kind){if("InlineFragment"!==n.kind)throw l("FlattenTransform: Expected an InlineFragment, got a '".concat(n.kind,"'"),[n.loc]);t.set(c,(0,r.default)((0,r.default)({},h),{},{selections:d(e,h,n,a,n.typeCondition)}))}else if("Condition"===h.kind){if("Condition"!==n.kind)throw l("FlattenTransform: Expected a Condition, got a '".concat(n.kind,"'"),[n.loc]);t.set(c,(0,r.default)((0,r.default)({},h),{},{selections:d(e,h,n,a,i)}))}else if("ClientExtension"===h.kind){if("ClientExtension"!==n.kind)throw l("FlattenTransform: Expected a ClientExtension, got a '".concat(n.kind,"'"),[n.loc]);t.set(c,(0,r.default)((0,r.default)({},h),{},{selections:d(e,h,n,a,i)}))}else if("FragmentSpread"===h.kind);else if("ModuleImport"===h.kind){if("ModuleImport"!==n.kind)throw l("FlattenTransform: Expected a ModuleImport, got a '".concat(n.kind,"'"),[n.loc]);if(n.name!==h.name||n.module!==h.module||n.key!==h.key)throw u("Found conflicting @module selections: use a unique alias on the parent fields.",[n.loc,h.loc]);t.set(c,(0,r.default)((0,r.default)({},h),{},{selections:d(e,h,n,a,i)}))}else if("Defer"===h.kind){if("Defer"!==n.kind)throw l("FlattenTransform: Expected a Defer, got a '".concat(n.kind,"'"),[n.loc]);t.set(c,(0,r.default)((0,r.default)({kind:"Defer"},h),{},{selections:d(e,h,n,a,i)}))}else if("Stream"===h.kind){if("Stream"!==n.kind)throw l("FlattenTransform: Expected a Stream, got a '".concat(n.kind,"'"),[n.loc]);t.set(c,(0,r.default)((0,r.default)({kind:"Stream"},h),{},{selections:d(e,h,n,a,i)}))}else if("LinkedField"===h.kind){if("LinkedField"!==n.kind)throw l("FlattenTransform: Expected a LinkedField, got a '".concat(n.kind,"'"),[n.loc]);p(n,h),t.set(c,{kind:"LinkedField",alias:h.alias,args:h.args,connection:h.connection||n.connection,directives:h.directives,handles:m(h,n),loc:h.loc,metadata:h.metadata,name:h.name,selections:d(e,h,n,a,n.type),type:h.type})}else{if("ScalarField"!==h.kind)throw"InlineDataFragmentSpread"===h.kind?l("FlattenTransform: did not expect an InlineDataFragmentSpread node. Only expecting InlineDataFragmentSpread in reader ASTs and this transform to run only on normalization ASTs.",[n.loc]):(h.kind,l("FlattenTransform: Unknown kind '".concat(h.kind,"'")));if("ScalarField"!==n.kind)throw l("FlattenTransform: Expected a ScalarField, got a '".concat(n.kind,"'"),[n.loc]);p(n,h),n.handles&&n.handles.length>0&&t.set(c,(0,r.default)((0,r.default)({kind:"ScalarField"},h),{},{handles:m(n,h)}))}else t.set(c,n)})),s}function d(e,t,n,r,a){var i=new Map;return f(e,i,t,r,a),f(e,i,n,r,a),Array.from(i.values())}function p(e,t){if(r=t,(n=e).kind!==r.kind||n.name!==r.name||n.alias!==r.alias||(a=n.args,o=r.args,a.length!==o.length||!a.every((function(e,t){var n=o[t];return e.name===n.name&&e.value.kind===n.value.kind&&e.value.variableName===n.value.variableName&&i(e.value.value,n.value.value)}))))throw u("Expected all fields on the same parent with the name or alias "+"'".concat(e.alias,"' to have the same name and arguments."),[e.loc,t.loc]);var n,r,a,o}function m(e,t){if(!e.handles)return t.handles;if(!t.handles)return e.handles;var n=new Map;return e.handles.concat(t.handles).forEach((function(e){return n.set(e.name+e.key,e)})),Array.from(n.values())}e.exports={transformWithOptions:function(e){return function(t){return c(t,e)}}}},function(e,t,n){"use strict";var r=n(5),a=n(35),i=a.printArguments,o=a.printDirectives;e.exports=function(e,t){return"LinkedField"===t.kind||"ScalarField"===t.kind?"Field: "+t.directives.length===0?t.alias:t.alias+o(e,t.directives):"FragmentSpread"===t.kind?"FragmentSpread:"+t.args.length===0?t.name:t.name+i(e,t.args):"ModuleImport"===t.kind?"ModuleImport:":"Defer"===t.kind?"Defer:"+t.label:"Stream"===t.kind?"Stream:"+t.label:"InlineFragment"===t.kind?"InlineFragment:"+e.getTypeString(t.typeCondition)+o(e,t.directives):"ClientExtension"===t.kind?"ClientExtension:":"InlineDataFragmentSpread"===t.kind?"InlineDataFragment:"+t.name:"Condition"===t.kind?"Condition:"+("Variable"===t.condition.kind?"$"+t.condition.variableName:String(t.condition.value))+String(t.passingValue):void r(!1,"getIdentifierForSelection: Unexpected kind `%s`.",t.kind)}},function(e,t,n){"use strict";var r=n(0),a=r(n(3)),i=r(n(2)),o=n(4),s=n(5),l=n(94),u=n(1).createUserError;function c(e,t){var n=this.traverse(e,t);if(0===t.reachableArguments.length)return n;var r=l(this.getContext().getSchema(),e,t.reachableArguments,"@relay(unmask: true)");return(0,i.default)((0,i.default)({},n),{},{argumentDefinitions:r})}function f(e,t){if(n=e,!Boolean(n.metadata&&!1===n.metadata.mask))return e;var n;0!==e.args.length&&s(!1,"MaskTransform: Cannot unmask fragment spread `%s` with arguments. Use the `ApplyFragmentArgumentTransform` before flattening",e.name);var r=this.getContext().getFragment(e.name),i={kind:"InlineFragment",directives:e.directives,loc:{kind:"Derived",source:e.loc},metadata:e.metadata,selections:r.selections,typeCondition:r.type};if(r.directives.length>0)throw new u("Cannot use @relay(mask: false) on fragment spreads for fragments with directives.",[e.loc,r.directives[0].loc]);var o=r.argumentDefinitions.find((function(e){return"LocalArgumentDefinition"===e.kind}));if(null!=o)throw u("MaskTransform: Cannot use @relay(mask: false) on fragment spread because the fragment definition uses @argumentDefinitions.",[e.loc,o.loc]);var l,c=(0,a.default)(r.argumentDefinitions);try{for(c.s();!(l=c.n()).done;){var f=l.value;t.reachableArguments.push(f)}}catch(e){c.e(e)}finally{c.f()}return this.traverse(i,t)}e.exports={transform:function(e){return o.transform(e,{FragmentSpread:f,Fragment:c},(function(){return{reachableArguments:[]}}))}}},function(e,t,n){"use strict";var r=n(0),a=r(n(2)),i=r(n(6)),o=n(4),s=n(22),l=n(38),u=n(1),c=u.createCompilerError,f=u.createUserError,d=n(12),p=d.getModuleComponentKey,m=d.getModuleOperationKey,h="module";function v(e,t){return this.traverse(e,(0,a.default)((0,a.default)({},t),{},{parentType:e.typeCondition}))}function y(e){var t=this.getContext().getSchema();if("js"===e.name){var n=t.getTypeFromString("JSDependency");if(null==n||!t.isServerType(n))throw new f("'".concat("js","' should be defined on the server schema."),[e.loc]);if(t.isScalar(n)&&t.areEqualTypes(t.getRawType(e.type),n))throw new f("Direct use of the '".concat("js","' field is not allowed, use ")+"@match/@module instead.",[e.loc])}return e}function g(e,t){var n,r=this.getContext().getSchema(),o=e.directives.find((function(e){return"match"===e.name})),l=null;if(null!=o&&(null!=(l=s(o.args).key)&&("string"!=typeof l||!l.startsWith(t.documentName))))throw f("Expected the 'key' argument of @match to be a literal string starting "+"with the document name, e.g. '".concat(t.documentName,"_<localName>'."),[(null!==(n=o.args.find((function(e){return"key"===e.name})))&&void 0!==n?n:o).loc]);t.path.push(e);var u=this.traverse(e,(0,a.default)((0,a.default)({},t),{},{moduleKey:l,parentType:e.type}));if(t.path.pop(),null==o)return u;var c=t.parentType,d=r.getRawType(c);if(!r.isInterface(d)&&!r.isObject(d))throw f("@match used on incompatible field '".concat(u.name,"'.")+"@match may only be used with fields whose parent type is an "+"interface or object, got invalid type '".concat(r.getTypeString(c),"'."),[e.loc]);var p=r.getFieldConfig(r.expectField(r.assertCompositeType(d),u.name)).args.find((function(e){return"supported"===e.name}));if(null==p){if(null==l)throw f("@match on a field without the `supported` argument is a no-op, please remove the `@match`.",[e.loc]);return u}var m=r.getNullableType(p.type),h=null!=m&&r.isList(m)?r.getListItemType(m):null;if(null==m||null==h||!r.isString(r.getNullableType(h)))throw f("@match used on incompatible field '".concat(u.name,"'. ")+"@match may only be used with fields that accept a 'supported: [String!]!' argument.",[e.loc]);var v=r.getRawType(u.type);if(!r.isAbstractType(v))throw f("@match used on incompatible field '".concat(u.name,"'.")+"@match may only be used with fields that return a union or interface.",[e.loc]);var y=new Map,g=[];if(u.selections.forEach((function(e){if("ScalarField"!==e.kind||"__typename"!==e.name){var t="InlineFragment"===e.kind?e.selections[0]:null;if("InlineFragment"!==e.kind||null==t||"ModuleImport"!==t.kind)throw f("Invalid @match selection: all selections should be fragment spreads with @module.",[e.loc]);var n=e.typeCondition;y.set(n,e),g.push(e)}else g.push(e)})),0===y.size)throw f("Invalid @match selection: expected at least one @module selection. Remove @match or add a '...Fragment @module()' selection.",[o.loc]);var b=u.args.find((function(e){return"supported"===e.name}));if(null!=b)throw f("Invalid @match selection: the '".concat("supported","' argument ")+"is automatically added and cannot be supplied explicitly.",[b.loc]);return{kind:"LinkedField",alias:u.alias,args:[].concat((0,i.default)(u.args),[{kind:"Argument",name:"supported",type:p.type,value:{kind:"Literal",loc:e.loc,value:Array.from(y.keys()).map((function(e){return r.getTypeString(e)}))},loc:e.loc}]),connection:!1,directives:[],handles:null,loc:e.loc,metadata:null,name:u.name,type:u.type,selections:g}}function b(e,t){var n,r,i,o,u,d,v,y=t.documentName,g=t.path,b=t.matchesForPath,T=t.moduleKey,S=this.traverse(e),w=S.directives.find((function(e){return"module"===e.name}));if(null==w)return S;if(0!==e.args.length)throw f("@module does not support @arguments.",[null===(v=e.args[0])||void 0===v?void 0:v.loc].filter(Boolean));var _=this.getContext(),k=_.getSchema(),F=k.asScalarFieldType(k.getTypeFromString("JSDependency"));if(null==F||!k.isServerType(F))throw new f("'".concat("js","' should be defined on the server schema."),[e.loc]);if(!k.isScalar(F))throw f("Using @module requires the schema to define a scalar "+"'".concat("JSDependency","' type."));var E=_.getFragment(e.name,e.loc);if(!k.isObject(E.type))throw f("@module used on invalid fragment spread '...".concat(e.name,"'. @module ")+"may only be used with fragments on a concrete (object) type, "+"but the fragment has abstract type '".concat(k.getTypeString(E.type),"'."),[e.loc,E.loc]);var x=k.getFieldByName(E.type,"js");if(!x)throw f("@module used on invalid fragment spread '...".concat(e.name,"'. @module ")+"requires the fragment type '".concat(k.getTypeString(E.type),"' to have a ")+"'".concat("js","(").concat(h,": String! ")+"[".concat("id",": String]): ").concat("JSDependency","' field (your ")+"schema may choose to omit the 'id'  argument but if present it must accept a 'String').",[w.loc]);var C=k.getFieldConfig(x),N=C?C.args.find((function(e){return e.name===h})):null,D=C?C.args.find((function(e){return"id"===e.name})):null;if(null==N||!k.isString(k.getNullableType(N.type))||null!=D&&!k.isString(D.type)||C.type!==F)throw f("@module used on invalid fragment spread '...".concat(e.name,"'. @module ")+"requires the fragment type '".concat(k.getTypeString(E.type),"' to have a ")+"'".concat("js","(").concat(h,": String! ")+"[".concat("id",": String]): ").concat("JSDependency","' field (your ")+"schema may choose to omit the 'id'  argument but if present it must accept a 'String').",[w.loc]);if(1!==e.directives.length)throw f("@module used on invalid fragment spread '...".concat(e.name,"'. @module ")+"may not have additional directives.",[e.loc]);var I,A=s(w.args).name;if("string"!=typeof A)throw f("Expected the 'name' argument of @module to be a literal string",[(null!==(I=w.args.find((function(e){return"name"===e.name})))&&void 0!==I?I:e).loc]);var R,O=g[g.length-1],M=null!=T?T:y,L=g.map((function(e){return e.alias})).join("."),j=""===L?y:"".concat(y,".").concat(L),V=k.getTypeString(E.type),q=b.get(L);if(null==q){var P;if(0!==b.size)if(null!=Array.from(b.values()).find((function(e){return e.key===M}))){if(null==O)throw c("Cannot have @module selections at multiple paths unless the selections are within fields.",[e.loc]);throw f("Invalid @module selection: documents with multiple fields containing 3D selections must specify a unique 'key' value "+"for each field: use '".concat(O.alias,' @match(key: "').concat(y,"_<localName>\")'."),[O.loc])}q={key:M,location:null!==(P=null==O?void 0:O.loc)&&void 0!==P?P:e.loc,types:new Map},b.set(L,q)}if(M!==q.key)throw c("Invalid @module selection: expected all selections at path "+"'".concat(L," to have the same 'key', got '").concat(M,"' and '").concat(q.key,"'."),[null!==(R=null==O?void 0:O.loc)&&void 0!==R?R:e.loc]);var U=q.types.get(V);if(null!=U&&(U.fragment!==e.name||U.module!==A))throw f("Invalid @module selection: concrete type "+"'".concat(V,"' was matched multiple times at path ")+"'".concat(L,"' but with a different fragment or module name."),[e.loc,U.location]);q.types.set(V,{location:e.loc,fragment:e.name,module:A});var G=l(e.name)+".graphql",B={alias:p(M),args:[{kind:"Argument",name:h,type:N.type,value:{kind:"Literal",loc:null!==(n=null===(r=w.args[0])||void 0===r?void 0:r.loc)&&void 0!==n?n:w.loc,value:A},loc:w.loc},null!=D?{kind:"Argument",name:"id",type:D.type,value:{kind:"Literal",loc:null!==(i=null===(o=w.args[0])||void 0===o?void 0:o.loc)&&void 0!==i?i:w.loc,value:j},loc:w.loc}:null].filter(Boolean),directives:[],handles:null,kind:"ScalarField",loc:w.loc,metadata:{skipNormalizationNode:!0},name:"js",type:F},W={alias:m(M),args:[{kind:"Argument",name:h,type:N.type,value:{kind:"Literal",loc:w.loc,value:G},loc:w.loc},null!=D?{kind:"Argument",name:"id",type:D.type,value:{kind:"Literal",loc:null!==(u=null===(d=w.args[0])||void 0===d?void 0:d.loc)&&void 0!==u?u:w.loc,value:j},loc:w.loc}:null].filter(Boolean),directives:[],handles:null,kind:"ScalarField",loc:w.loc,metadata:{skipNormalizationNode:!0},name:"js",type:F};return{kind:"InlineFragment",directives:[],loc:w.loc,metadata:null,selections:[{kind:"ModuleImport",loc:w.loc,key:M,id:j,module:A,sourceDocument:y,name:e.name,selections:[(0,a.default)((0,a.default)({},e),{},{directives:e.directives.filter((function(e){return e!==w}))}),W,B]}],typeCondition:E.type}}e.exports={SCHEMA_EXTENSION:"\n  directive @match(key: String) on FIELD\n\n  directive @module(\n    name: String!\n  ) on FRAGMENT_SPREAD\n",transform:function(e){return o.transform(e,{FragmentSpread:b,LinkedField:g,InlineFragment:v,ScalarField:y},(function(e){return{documentName:e.name,matchesForPath:new Map,moduleKey:null,parentType:e.type,path:[]}}))}}},function(e,t,n){"use strict";var r=n(0)(n(2)),a=n(20),i=n(22),o=n(26),s=n(1),l=s.createUserError,u=s.eachWithCombinedError,c=n(95).buildRefetchOperation;function f(e,t){var n;return null!==(n=e.args.find((function(e){return e.name===t})))&&void 0!==n?n:null}e.exports={SCHEMA_EXTENSION:"\n  directive @refetchable(\n    queryName: String!\n  ) on FRAGMENT_DEFINITION\n",transform:function(e){var t=e.getSchema(),n=function(e){var t=new Map;u(e.documents(),(function(e){if("Fragment"===e.kind){var n=function(e){var t=e.directives.find((function(e){return"refetchable"===e.name}));if(null==t)return null;var n=i(t.args).queryName;if(null==n)throw l("Expected the 'queryName' argument of @refetchable to be provided",[t.loc]);if("string"!=typeof n){var r,a=t.args.find((function(e){return"queryName"===e.name}));throw l("Expected the 'queryName' argument of @refetchable to be a string, got '".concat(String(n),"'."),[null!==(r=null==a?void 0:a.loc)&&void 0!==r?r:t.loc])}return n}(e);if(null!==n){var r=t.get(n);if(null!=r)throw l("Duplicate definition for @refetchable operation '".concat(n,"' from fragments '").concat(e.name,"' and '").concat(r.name,"'"),[e.loc,r.loc]);t.set(n,e)}}}));var n=o(e);return new Map(Array.from(t.entries(),(function(e){var t=e[0],r=e[1];return[t,n.getFragment(r.name)]})))}(e),s=e;return u(n,(function(n){var i=n[0],o=n[1],u=c(t,o,i),d=u.identifierField,p=u.path,m=u.node,h=u.transformedFragment,v=function(e,t){var n=[],r=null,i=null;if(a.visit(t,{LinkedField:{enter:function(a){if(n.push(a),!0===a.connection||a.handles&&a.handles.some((function(e){return"connection"===e.name}))){if(null!=r)throw l("Invalid use of @refetchable with @connection in fragment '".concat(t.name,"', at most once @connection can appear in a refetchable fragment."),[a.loc]);var o=n.find((function(t){return e.isList(e.getNullableType(t.type))}));if(o)throw l("Invalid use of @refetchable with @connection in fragment '".concat(t.name,"', refetchable connections cannot appear inside plural fields."),[a.loc,o.loc]);r=a,i=n.map((function(e){return e.alias}))}},leave:function(){n.pop()}}}),null==r||null==i)return;var o=null,s=f(r,"before"),u=f(r,"last");if(s||u){if(!s||!u||"Variable"!==s.value.kind||"Variable"!==u.value.kind)throw l("Invalid use of @refetchable with @connection in fragment '".concat(t.name,"', refetchable connections must use variables for the before and last arguments."),[r.loc,s&&"Variable"!==s.value.kind?s.value.loc:null,u&&"Variable"!==u.value.kind?u.value.loc:null].filter(Boolean));o={count:u.value.variableName,cursor:s.value.variableName}}var c=null,d=f(r,"after"),p=f(r,"first");if(d||p){if(!d||!p||"Variable"!==d.value.kind||"Variable"!==p.value.kind)throw l("Invalid use of @refetchable with @connection in fragment '".concat(t.name,"', refetchable connections must use variables for the after and first arguments."),[r.loc,d&&"Variable"!==d.value.kind?d.value.loc:null,p&&"Variable"!==p.value.kind?p.value.loc:null].filter(Boolean));c={count:p.value.variableName,cursor:d.value.variableName}}return{forward:c,backward:o,path:i}}(e.getSchema(),h);s=(s=s.replace((0,r.default)((0,r.default)({},h),{},{metadata:(0,r.default)((0,r.default)({},h.metadata||{}),{},{refetch:{connection:null!=v?v:null,operation:i,fragmentPathInResult:p,identifierField:d}})}))).add((0,r.default)((0,r.default)({},m),{},{metadata:(0,r.default)((0,r.default)({},m.metadata||{}),{},{derivedFrom:h.name,isRefetchableQuery:!0})}))})),s}}},function(e,t,n){"use strict";var r=n(0)(n(2)),a=n(4),i=n(22),o=n(5);function s(e){return function(t){var n=t.directives.find((function(e){return"relay"===e.name}));if(!n)return this.traverse(t);var a=i(n.args),o=e(a);return this.traverse((0,r.default)((0,r.default)({},t),{},{directives:t.directives.filter((function(e){return e!==n})),metadata:(0,r.default)((0,r.default)({},t.metadata||{}),o)}))}}function l(e){var t=e.mask,n=e.plural;return void 0!==n&&"boolean"!=typeof n&&o(!1,'RelayDirectiveTransform: Expected the "plural" argument to @relay to be a boolean literal if specified.'),void 0!==t&&"boolean"!=typeof t&&o(!1,'RelayDirectiveTransform: Expected the "mask" argument to @relay to be a boolean literal if specified.'),{mask:t,plural:n}}function u(e){var t=e.mask;return void 0!==t&&"boolean"!=typeof t&&o(!1,'RelayDirectiveTransform: Expected the "mask" argument to @relay to be a boolean literal if specified.'),{mask:t}}e.exports={RELAY:"relay",SCHEMA_EXTENSION:"\ndirective @relay(\n  # Marks a fragment as being backed by a GraphQLList.\n  plural: Boolean,\n\n  # Marks a fragment spread which should be unmasked if provided false\n  mask: Boolean = true,\n) on FRAGMENT_DEFINITION | FRAGMENT_SPREAD\n",transform:function(e){return a.transform(e,{Fragment:s(l),FragmentSpread:s(u)})}}},function(e,t,n){"use strict";var r=n(0),a=r(n(3)),i=r(n(2)),o=r(n(6)),s=n(4),l=n(21),u=n(1),c=u.createUserError,f=u.createCompilerError,d=n(12).RelayFeatureFlags;function p(e,t){return b(this.traverse(e,t),t)}function m(e,t){return b(this.traverse(e,t),t)}function h(e,t){var n,r=null!==(n=t.parentAbstractInlineFragment)&&void 0!==n?n:function(e,t){var n=e.typeCondition;if(t.isAbstractType(n))return e;return null}(e,t.schema);return this.traverse(e,(0,i.default)((0,i.default)({},t),{},{parentAbstractInlineFragment:r}))}function v(e){switch(e){case"NONE":return 0;case"LOG":return 1;case"THROW":return 2;default:throw f("Unhandled action type ".concat(e))}}function y(e,t){var n=[].concat((0,o.default)(t.path),[e.alias]),r=(0,i.default)((0,i.default)({},t),{},{currentNodeRequiredChildren:new Map,path:n,parentAbstractInlineFragment:null}),s=this.traverse(e,r),l=n.join(".");!function(e,t,n){var r=n.currentNodeRequiredChildren,i=n.pathRequiredMap,o=n.requiredChildrenMap.get(t);if(null==o)return;var s,l=(0,a.default)(r);try{for(l.s();!(s=l.n()).done;){var u=s.value,c=u[0],d=u[1];if(!o.has(c)){var p=i.get(t);if(null==p)throw f('Could not find other parent node at path "'.concat(t,'".'),[d.loc]);throw _(d,p)}}}catch(e){l.e(e)}finally{l.f()}var m,h=(0,a.default)(o);try{for(h.s();!(m=h.n()).done;){var v=m.value,y=v[0],g=v[1];if(!r.has(y))throw _(g,e)}}catch(e){h.e(e)}finally{h.f()}}(e,l,r),S(s=k(s,l,t.documentName),l,r.pathRequiredMap);var u=w(s);if(null!=u){T(t.schema,u,t.parentAbstractInlineFragment),t.currentNodeRequiredChildren.set(e.alias,s);var d=v(u.action);r.currentNodeRequiredChildren.forEach((function(e){var t=w(e);if(null!=t&&v(t.action)<d)throw c("The @required field [1] may not have an `action` less severe than that of its @required parent [2]. [1] should probably be `action: ".concat(u.action,"`."),[t.actionLoc,u.actionLoc])}))}return t.requiredChildrenMap.set(l,r.currentNodeRequiredChildren),b(s,r)}function g(e,t){var n=[].concat((0,o.default)(t.path),[e.alias]).join("."),r=k(e,n,t.documentName),a=w(r);return null!=a&&(T(t.schema,a,t.parentAbstractInlineFragment),t.currentNodeRequiredChildren.set(e.alias,r)),S(r,n,t.pathRequiredMap),r}function b(e,t){var n,r=(0,a.default)(t.currentNodeRequiredChildren.values());try{for(r.s();!(n=r.n()).done;){var o=w(n.value);if(null!=o&&"THROW"!==o.action){var s=(0,i.default)((0,i.default)({},e.metadata),{},{childrenCanBubbleNull:!0});return(0,i.default)((0,i.default)({},e),{},{metadata:s})}}}catch(e){r.e(e)}finally{r.f()}return e}function T(e,t,n){if(null!=n){var r=n.typeCondition;throw e.isUnion(r)?c("The @required directive [1] may not be used anywhere within an inline fragment on a union type [2].",[t.directiveLoc,n.loc]):e.isInterface(r)?c("The @required directive [1] may not be used anywhere within an inline fragment on an interface type [2].",[t.directiveLoc,n.loc]):f("Unexpected abstract inline fragment type.",[n.loc])}}function S(e,t,n){var r=n.get(t);if(null!=r){var a=w(e),i=w(r);if((null==a?void 0:a.action)!==(null==i?void 0:i.action)){if(null==a)throw c('The field "'.concat(e.alias,'" is @required in [1] but not in [2].'),[r.loc,e.loc]);if(null==i)throw c('The field "'.concat(e.alias,'" is @required in [1] but not in [2].'),[e.loc,r.loc]);throw c('The field "'.concat(e.alias,'" has a different @required action in [1] than in [2].'),[a.actionLoc,i.actionLoc])}}else n.set(t,e)}function w(e){var t;return null===(t=e.metadata)||void 0===t?void 0:t.required}function _(e,t){var n=e.alias;return c('The field "'.concat(n,'" is marked as @required in [1] but is missing in [2].'),[e.loc,t.loc])}function k(e,t,n){var r=l(e.directives,(function(e){return"required"===e.name})),a=r[0],o=r[1];if(0===a.length)return e;if(!function(e){var t=d.ENABLE_REQUIRED_DIRECTIVES;return"boolean"==typeof t?t:"LIMITED"===t?e.startsWith("RelayRequiredTest"):"string"==typeof t&&t.split("|").some((function(t){return e.startsWith(t)}))}(n))throw new c("The @required directive is experimental and not yet supported for use in product code",a.map((function(e){return e.loc})));if(a.length>1)throw new c("Did not expect multiple @required directives.",a.map((function(e){return e.loc})));var s=a[0],u=s.args[0];if(null==u)throw c("The @required directive requires an `action` argument.",[s.loc]);if("Literal"!==u.value.kind)throw c("Expected @required `action` argument to be a literal.",[u.value.loc]);return(0,i.default)((0,i.default)({},e),{},{directives:o,metadata:(0,i.default)((0,i.default)({},e.metadata),{},{required:{action:u.value.value,actionLoc:u.loc,directiveLoc:s.loc,path:t}})})}e.exports={SCHEMA_EXTENSION:"\n  enum RequiredFieldAction {\n    NONE\n    LOG\n    THROW\n  }\n  directive @required(\n    action: RequiredFieldAction!\n  ) on FIELD\n",transform:function(e){var t=e.getSchema();return s.transform(e,{LinkedField:y,ScalarField:g,InlineFragment:h,Fragment:p,Root:m},(function(e){return{schema:t,documentName:e.name,path:[],pathRequiredMap:new Map,currentNodeRequiredChildren:new Map,requiredChildrenMap:new Map,parentAbstractInlineFragment:null}}))}}},function(e,t,n){"use strict";var r=n(5),a=n(39);e.exports={anyTypeAlias:function(e){return a.typeAlias(a.identifier(e),void 0,a.anyTypeAnnotation())},declareExportOpaqueType:function(e,t){return a.declareExportDeclaration(a.declareOpaqueType(a.identifier(e),void 0,a.genericTypeAnnotation(a.identifier(t))))},exactObjectTypeAnnotation:function(e){var t=a.objectTypeAnnotation(e);return t.exact=!0,t},inexactObjectTypeAnnotation:function(e){var t=a.objectTypeAnnotation(e);return t.inexact=!0,t},exportType:function(e,t){return a.exportNamedDeclaration(a.typeAlias(a.identifier(e),void 0,t),[],void 0)},exportTypes:function(e){var t=a.exportNamedDeclaration(void 0,e.map((function(e){return a.exportSpecifier(a.identifier(e),a.identifier(e))})),void 0);return t.exportKind="type",t},importTypes:function(e,t){var n=a.importDeclaration(e.map((function(e){return a.importSpecifier(a.identifier(e),a.identifier(e))})),a.stringLiteral(t));return n.importKind="type",n},intersectionTypeAnnotation:function(e){return e.length>0||r(!1,"RelayFlowBabelFactories: cannot create an intersection of 0 types"),1===e.length?e[0]:a.intersectionTypeAnnotation(e)},lineComments:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.map((function(e){return{type:"CommentLine",value:" "+e}}))},readOnlyArrayOfType:function(e){return a.genericTypeAnnotation(a.identifier("$ReadOnlyArray"),a.typeParameterInstantiation([e]))},readOnlyObjectTypeProperty:function(e,t){var n=a.objectTypeProperty(a.identifier(e),t);return n.variance=a.variance("plus"),n},stringLiteralTypeAnnotation:function(e){return a.stringLiteralTypeAnnotation(e)},unionTypeAnnotation:function(e){return e.length>0||r(!1,"RelayFlowBabelFactories: cannot create a union of 0 types"),1===e.length?e[0]:a.unionTypeAnnotation(e)}}},function(e,t,n){"use strict";var r=n(102),a=n(104),i=n(105),o=n(106),s=n(107),l=n(108),u=n(109),c=n(110),f=n(111),d=n(112),p=n(61),m=n(113),h=n(114),v=n(115),y=n(116),g=n(63),b=n(64),T=n(117),S=n(65),w=n(66),_=n(37),k=n(67),F=n(118),E=n(119),x=n(120),C=n(121),N=n(122),D=n(123),I=n(124),A=n(125),R=n(126),O=n(127),M=n(128),L=[i.SCHEMA_EXTENSION,o.SCHEMA_EXTENSION,v.SCHEMA_EXTENSION,b.SCHEMA_EXTENSION,k.SCHEMA_EXTENSION,S.SCHEMA_EXTENSION,w.SCHEMA_EXTENSION,_.SCHEMA_EXTENSION,A.SCHEMA_EXTENSION,M.SCHEMA_EXTENSION],j=[l.transform,i.transform,w.transform,g.transform,b.transform,S.transform,s.transform,T.transform],V=[a.transform,c.transform,v.transform,p.transformWithOptions({isForCodegen:!0}),k.transform,x.transform],q=[I.transform,u.transform,M.transform,r.transform,R.transform,m.transform,o.transform],P=[f.transform,N.transform,y.transform,a.transform,h.transform,p.transformWithOptions({isForCodegen:!0}),x.transform,A.transform],U=[C.transform,a.transform,F.transform,N.transform,h.transform,p.transformWithOptions({}),E.transform,d.transform,D.transform,O.transform];e.exports={commonTransforms:j,codegenTransforms:P,fragmentTransforms:V,printTransforms:U,queryTransforms:q,schemaExtensions:L}},function(e,t,n){"use strict";
/**
 * Based on implementations by Gary Court and Austin Appleby, 2011, MIT.
 * @preserve-header
 */e.exports=function(e){for(var t,n=e.length,r=3&n,a=n^r,i=0,o=0;o!==a;){var s=e.charCodeAt(o+3);t=e.charCodeAt(o)^e.charCodeAt(o+1)<<8^e.charCodeAt(o+2)<<16^(255&s)<<24^(65280&s)>>8,o+=4,i=5*(i=(i^=t=13715*(t=(t=11601*t+3432906752*(65535&t)>>>0)<<15|t>>>17)+461832192*(65535&t)>>>0)<<13|i>>>19)+3864292196>>>0}switch(t=0,r){case 3:t^=e.charCodeAt(a+2)<<16;case 2:t^=e.charCodeAt(a+1)<<8;case 1:i^=t=13715*(t=(t=11601*(t^=e.charCodeAt(a))+3432906752*(65535&t)>>>0)<<15|t>>>17)+461832192*(65535&t)>>>0}if(i^=n,i=51819*(i^=i>>>16)+2246770688*(65535&i)>>>0,i=44597*(i^=i>>>13)+3266445312*(65535&i)>>>0,i^=i>>>16,!(i>>>=0))return"0";for(var l="";i;){var u=i%62;l="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"[u]+l,i=(i-u)/62}return l}},function(e,t,n){"use strict";var r=n(5),a=n(1).eachWithCombinedError;var i=function(){function e(e,t){this._context=e,this._states=[],this._visitor=t}var t=e.prototype;return t.getContext=function(){return this._context},t.visit=function(e,t){this._states.push(t),this._visit(e),this._states.pop()},t.traverse=function(e,t){this._states.push(t),this._traverse(e),this._states.pop()},t._visit=function(e){var t=this._visitor[e.kind];if(t){var n=this._getState();t.call(this,e,n)}else this._traverse(e)},t._traverse=function(e){switch(e.kind){case"Argument":this._traverseChildren(e,null,["value"]);break;case"Literal":case"LocalArgumentDefinition":case"RootArgumentDefinition":case"Variable":break;case"Defer":this._traverseChildren(e,["selections"],["if"]);break;case"Stream":this._traverseChildren(e,["selections"],["if","initialCount"]);break;case"ClientExtension":this._traverseChildren(e,["selections"]);break;case"Directive":this._traverseChildren(e,["args"]);break;case"ModuleImport":this._traverseChildren(e,["selections"]);break;case"FragmentSpread":case"ScalarField":this._traverseChildren(e,["args","directives"]);break;case"InlineDataFragmentSpread":this._traverseChildren(e,["selections"]);break;case"LinkedField":this._traverseChildren(e,["args","directives","selections"]);break;case"ListValue":this._traverseChildren(e,["items"]);break;case"ObjectFieldValue":this._traverseChildren(e,null,["value"]);break;case"ObjectValue":this._traverseChildren(e,["fields"]);break;case"Condition":this._traverseChildren(e,["directives","selections"],["condition"]);break;case"InlineFragment":this._traverseChildren(e,["directives","selections"]);break;case"Fragment":case"Root":this._traverseChildren(e,["argumentDefinitions","directives","selections"]);break;case"Request":this._traverseChildren(e,null,["fragment","root"]);break;case"SplitOperation":this._traverseChildren(e,["selections"]);break;default:r(!1,"IRValidator: Unknown kind `%s`.",e.kind)}},t._traverseChildren=function(e,t,n){var a=this;t&&t.forEach((function(t){var n=e[t];n&&(Array.isArray(n)||r(!1,"IRValidator: Expected data for `%s` to be an array, got `%s`.",t,n),n.forEach((function(e){return a._visit(e)})))})),n&&n.forEach((function(t){var n=e[t];n&&a._visit(n)}))},t._getState=function(){return this._states.length||r(!1,"IRValidator: Expected a current state to be set but found none. This is usually the result of mismatched number of pushState()/popState() calls."),this._states[this._states.length-1]},e}();e.exports={validate:function(e,t,n){var r=new i(e,t);a(e.documents(),(function(e){if(void 0===n)r.visit(e,void 0);else{var t=n(e);null!=t&&r.visit(e,t)}}))}}},function(e,t,n){"use strict";e.exports={hasUnaliasedSelection:function(e,t){return e.selections.some((function(e){return"ScalarField"===e.kind&&e.alias===t&&e.name===t}))}}},function(e,t,n){"use strict";var r=n(50),a=n(5),i=n(7),o=i.parse,s=i.print;function l(e){return{ast:e,text:s(e)}}e.exports={parseExecutableNode:function(e){var t=o(e).definitions;1!==t.length&&a(!1,"expected exactly 1 definition");var n=t[0];return"OperationDefinition"!==n.kind&&"FragmentDefinition"!==n.kind&&a(!1,"expected an ExecutableDefinitionNode"),n},toASTRecord:l,extractFromJS:function(e,t){if(!t.exists)return null;var n={relPath:t.name,exists:!0,hash:t["content.sha1hex"]};if(!r.getFileFilter(e)(n))return null;var a=r.parseFileWithSources(e,n);if(null==a||0===a.document.definitions.length)return null;var i=a.document,o=a.sources;return{nodes:i.definitions.map((function(e){if("FragmentDefinition"===e.kind||"OperationDefinition"===e.kind)return l(e);throw new Error("Unexpected definition kind: ".concat(e.kind))})),sources:o}}}},function(e,t,n){"use strict";var r=function(e){e.moduleName;var t=e.documentType,n=e.docText,r=e.concreteText,a=e.typeText,i=e.hash,o=e.sourceHash,s=t?"import type { ".concat(t," } from 'relay-runtime';"):"",l=null!=n?"\n/*\n"+n.trim()+"\n*/\n":"",u=null!=i?"\n * ".concat(i):"";return"/**\n * ".concat("@","flow",u,"\n */\n\n/* eslint-disable */\n\n'use strict';\n\n/*::\n").concat(s,"\n").concat(a||"","\n*/\n\n").concat(l,"\nconst node/*: ").concat(t||"empty","*/ = ").concat(r,";\n// prettier-ignore\n(node/*: any*/).hash = '").concat(o,"';\n")};t.formatGeneratedCommonjsModule=function(e){return"".concat(r(e),"\nmodule.exports = node;\n")},t.formatGeneratedESModule=function(e){return"".concat(r(e),"\nexport default node;\n")}},function(e,t,n){"use strict";var r=n(28),a=n(29),i=n(76),o=n(77),s=n(31),l=n(32),u=n(41),c=n(42),f=n(19),d=n(1),p=n(45),m=n(46),h=n(34),v=n(81),y=n(18),g=n(8),b=n(48),T=n(23),S=n(35),w=n(4),_=n(20),k=n(50),F=n(86),E=n(53),x=n(56),C=n(52),N=n(37),D=n(69),I=n(25),A=n(33),R=n(129),O=n(10),M=n(130),L=n(131),j=n(132),V=n(133),q=n(36),P=n(73),U=n(58),G=n(134),B=n(135),W=n(40),Q=n(22),H=n(38),z=n(136),K=n(16),$=n(59),J=n(137).main,X=n(140).SourceControlMercurial,Y=n(60).getReaderSourceDefinitionName,Z=n(74).formatGeneratedCommonjsModule;e.exports={relayCompiler:J,ASTConvert:a,CodegenDirectory:l,CodegenRunner:u,CodegenWatcher:c,CodeMarker:s,CompilerContext:f,CompilerError:d,ConsoleReporter:p,DotGraphQLParser:m,ASTCache:r,IRTransformer:w,IRVisitor:_,Printer:S,Profiler:g,Rollout:R,SchemaUtils:O,SourceControlMercurial:X,WatchmanClient:T,filterContextForNode:U,getIdentifierForArgumentValue:W,getNormalizationOperationName:H,getLiteralArgumentValues:Q,Parser:I,Schema:A,CodeGenerator:E,FlowGenerator:N,FileWriter:x,IRTransforms:D,JSModuleParser:k,MultiReporter:F,Runner:u,TimeReporter:j,compileRelayArtifacts:q,formatGeneratedModule:Z,convertASTDocuments:a.convertASTDocuments,transformASTSchema:a.transformASTSchema,getReaderSourceDefinitionName:Y,writeRelayGeneratedFile:$,Sources:M,__internal:{Artifacts:i,BufferedFilesystem:o,GraphQLASTNodeGroup:v,GraphQLASTUtils:y,GraphQLNodeMap:b,FindGraphQLTags:h,StrictMap:L,RelayFindGraphQLTags:C,compileArtifacts:V,extractFromJS:P.extractFromJS,getChangedNodeNames:G,getDefinitionNodeHash:B,getSchemaInstance:z,md5:K,parseExecutableNode:P.parseExecutableNode,toASTRecord:P.toASTRecord}}},function(e,t,n){"use strict";var r=n(0),a=r(n(3)),i=r(n(6)),o=n(14),s=n(15),l=n(18).getName;function*u(e){var t,n=(0,a.default)(e.artifacts);try{for(n.s();!(t=n.n()).done;){var r,i=t.value,o=i[0],s=i[1],l=(0,a.default)(s.keys());try{for(l.s();!(r=l.n()).done;){var u=r.value;yield[o,u]}}catch(e){l.e(e)}finally{l.f()}}}catch(e){n.e(e)}finally{n.f()}}function c(e){return o.createHash("sha1").update(e).digest("hex")}e.exports={createEmptyState:function(){return{artifacts:new Map,metadata:new Map}},serializeState:function(e){var t,n=[],r=(0,a.default)(e.artifacts);try{for(r.s();!(t=r.n()).done;){var i=t.value,o=i[0],s=i[1];n.push([o,Array.from(s).map((function(t){var n;return[t,null!==(n=e.metadata.get(t))&&void 0!==n?n:""]}))])}}catch(e){r.e(e)}finally{r.f()}return n},deserializeState:function(e){var t=new Map,n=new Map;return e.forEach((function(e){var r=e[0],a=e[1],i=new Set;a.forEach((function(e){var n=e[0],r=e[1];i.add(n),t.set(n,r)})),n.set(r,i)})),{artifacts:n,metadata:t}},updateState:function(e,t,n,r,i){var o,s={artifacts:new Map(e.artifacts),metadata:new Map(e.metadata)},c=new Set,f=new Set,d=(0,a.default)(t.added);try{for(d.s();!(o=d.n()).done;){var p=o.value.ast;f.add(l(p))}}catch(e){d.e(e)}finally{d.f()}var m,h=(0,a.default)(t.removed);try{for(h.s();!(m=h.n()).done;){var v=m.value.ast,y=l(v);if(!f.has(y)){var g=s.artifacts.get(y);if(null!=g){var b,T=(0,a.default)(g.keys());try{for(T.s();!(b=T.n()).done;){var S=b.value;c.add(S)}}catch(e){T.e(e)}finally{T.f()}s.artifacts.delete(y)}}}}catch(e){h.e(e)}finally{h.f()}var w,_=(0,a.default)(n.artifacts);try{for(_.s();!(w=_.n()).done;){var k=w.value,F=k[0],E=k[1],x=s.artifacts.get(F);if(null!=x){var C,N=(0,a.default)(x);try{for(N.s();!(C=N.n()).done;){var D=C.value;E.has(D)||c.add(D)}}catch(e){N.e(e)}finally{N.f()}}s.artifacts.set(F,E);var I,A=(0,a.default)(E.keys());try{for(A.s();!(I=A.n()).done;){var R,O=I.value;s.metadata.set(O,null!==(R=n.metadata.get(O))&&void 0!==R?R:"")}}catch(e){A.e(e)}finally{A.f()}}}catch(e){_.e(e)}finally{_.f()}if(0===c.size)return s;var M,L=new Set,j=(0,a.default)(u(s));try{for(j.s();!(M=j.n()).done;){var V=M.value[1];L.add(V)}}catch(e){j.e(e)}finally{j.f()}var q,P=(0,a.default)(c);try{for(P.s();!(q=P.n()).done;){var U=q.value;if(!L.has(U)){var G=i(U);r.existsSync(G)&&(r.unlinkSync(G),s.metadata.delete(U))}}}catch(e){P.e(e)}finally{P.f()}return s},producedFiles:function(e,t){var n=new Map;return e.forEach((function(e){var r=e.baseDir,a=e.dir,o=a.changes,l=o.deleted,u=o.updated,f=o.created,d=o.unchanged;if(l.length>0)throw new Error("Did not expect to see a deletion entry here.");[].concat((0,i.default)(u),(0,i.default)(f)).forEach((function(e){var t=a.getPath(e).substr(r.length+1),i=c(s(a.read(e)));n.set(t,i)})),d.forEach((function(e){var i=a.getPath(e).substr(r.length+1),o=t.get(i);n.set(i,null!=o?o:c(s(a.read(e))))}))})),n},eachNameAndArtifact:u}},function(e,t,n){"use strict";var r=n(0),a=n(9),i=r(n(3)),o=r(n(30)),s=n(13),l=n(5),u=function(){function e(){(0,o.default)(this,"buffer",new Map),(0,o.default)(this,"committed",!1)}var t=e.prototype;return t._assertNotComitted=function(){this.committed&&l(!1,"BufferedFilesystem: no operations allowed after commit().")},t.commit=function(){var e=a((function*(e){this._assertNotComitted(),this.committed=!0;var t,n=[],r=[],a=(0,i.default)(this.buffer);try{for(a.s();!(t=a.n()).done;){var o=t.value,l=o[0],u=o[1];if(null==u)n.push(l),s.unlinkSync(l);else(s.existsSync(l)?s.readFileSync(l,"utf8"):null)!==u&&(r.push(l),s.writeFileSync(l,u,"utf8"))}}catch(e){a.e(e)}finally{a.f()}e&&(yield e.addRemove(r,n))}));return function(t){return e.apply(this,arguments)}}(),t.hasChanges=function(){return this._assertNotComitted(),this.buffer.size>0},t.getChangesSummary=function(){this._assertNotComitted();var e,t=[],n=[],r=[],a=(0,i.default)(this.buffer);try{for(a.s();!(e=a.n()).done;){var o=e.value,l=o[0];null==o[1]?r.push(l):s.existsSync(l)?n.push(l):t.push(l)}}catch(e){a.e(e)}finally{a.f()}return[t.length>0?"Added:\n".concat(t.map(c).join("")):"",n.length>0?"Updated:\n".concat(n.map(c).join("")):"",r.length>0?"Removed:\n".concat(r.map(c).join("")):""].filter(Boolean).join("\n")},t.getAddedRemovedFiles=function(){this._assertNotComitted();var e,t=[],n=[],r=(0,i.default)(this.buffer);try{for(r.s();!(e=r.n()).done;){var a=e.value,o=a[0];null==a[1]?n.push(o):s.existsSync(o)||t.push(o)}}catch(e){r.e(e)}finally{r.f()}return{added:t,removed:n}},t.existsSync=function(e){return this._assertNotComitted(),this.buffer.has(e)?Boolean(this.buffer.get(e)):s.existsSync(e)},t.mkdirSync=function(e){this._assertNotComitted(),s.mkdirSync(e)},t.readdirSync=function(e){throw this._assertNotComitted(),new Error("BufferedFilesystem: readdirSync is not implemented.")},t.readFileSync=function(e,t){if(this._assertNotComitted(),this.buffer.has(e)){var n=this.buffer.get(e);return null==n&&l(!1,"BufferedFilesystem: trying to read deleted file."),n}return s.readFileSync(e,t)},t.statSync=function(e){return this._assertNotComitted(),s.statSync(e)},t.unlinkSync=function(e){this._assertNotComitted(),this.buffer.set(e,null)},t.writeFileSync=function(e,t,n){this._assertNotComitted(),this.buffer.set(e,t)},t.changedFilesToJSON=function(){this._assertNotComitted();var e,t=[],n=[],r=(0,i.default)(this.buffer);try{for(r.s();!(e=r.n()).done;){var a=e.value,o=a[0],s=a[1];null==s?n.push({path:o}):t.push({path:o,data:s})}}catch(e){r.e(e)}finally{r.f()}return{removed:n,changed:t}},e}();function c(e){var t=e.length-80;return(t>0?"\t - ".concat(e.substr(0,8),"..."):"\t - ")+e.substr(t,e.length)+"\n"}e.exports=u},function(e,t){e.exports=require("fb-watchman")},function(e,t){e.exports=require("chalk")},function(e,t){e.exports=require("@babel/parser")},function(e,t,n){"use strict";var r=n(0),a=r(n(3)),i=r(n(6)),o=n(48),s=n(18).getName,l=n(7).visit;function u(e){var t,n=new Map,r=(0,a.default)(e.values());try{for(r.s();!(t=r.n()).done;){var i=t.value,o=s(i);if(n.has(o))throw new Error("Duplicated definition for ".concat(o));n.set(o,p(i))}}catch(e){r.e(e)}finally{r.f()}return n}function c(e){var t,n=new Map,r=(0,a.default)(e);try{for(r.s();!(t=r.n()).done;){var i,o=t.value,s=(0,a.default)(o.entries());try{for(s.s();!(i=s.n()).done;){var l=i.value,u=l[0],c=l[1];if(n.has(u))throw new Error("Duplicate entry for '".concat(u,"'."));n.set(u,c)}}catch(e){s.e(e)}finally{s.f()}}}catch(e){r.e(e)}finally{r.f()}return n}function f(e,t,n){var r,i=new Set,s=[],l=(0,a.default)(e);try{for(l.s();!(r=l.n()).done;){var u=r.value,c=n.get(u);null!=c&&(s.push(c),i.add(u));var f=t.get(u);null!=f&&s.push(f)}}catch(e){l.e(e)}finally{l.f()}return{baseNames:i,nodes:o.from(s)}}function d(e,t){for(var n=Array.from(e),r=new Set;n.length>0;){var i=n.pop();r.add(i);var o,s=(0,a.default)(t.get(i)||[]);try{for(s.s();!(o=s.n()).done;){var l=o.value;r.has(l)||n.push(l)}}catch(e){s.e(e)}finally{s.f()}}return r}function p(e){var t=[];return l(e,{FragmentSpread:function(e){t.push(e.name.value)}}),t}function m(e){var t,n=new Map,r=(0,a.default)(e.entries());try{for(r.s();!(t=r.n()).done;){var i,o=t.value,s=o[0],l=o[1],u=s,c=(0,a.default)(l);try{for(c.s();!(i=c.n()).done;){var f=i.value,d=n.get(f);null==d&&(d=[],n.set(f,d)),d.push(u)}}catch(e){c.e(e)}finally{c.f()}}}catch(e){r.e(e)}finally{r.f()}return n}e.exports={forChanges:function(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],i=u(e),o=c(r.map(u)),s=c([i,o]),l=m(s),p=c(r),h=d(t,l),v=new Set,y=(0,a.default)(h);try{for(y.s();!(n=y.n()).done;){var g=n.value;e.has(g)&&v.add(g)}}catch(e){y.e(e)}finally{y.f()}var b=d(v,s);return f(b,e,p)},forFullBuild:function(e,t){var n=c([e].concat((0,i.default)(t)).map(u));return f(d(new Set(e.keys()),n),e,c(t))}}},function(e,t){e.exports=require("@babel/runtime/helpers/wrapNativeSuper")},function(e,t,n){"use strict";var r=n(0)(n(30)),a=n(8),i=n(14),o=n(13),s=n(84),l=n(11),u=function(){function e(e,t){(0,r.default)(this,"_dir",null),this._name=e,this._cacheBreaker=t}var t=e.prototype;return t._getFile=function(e){if(null==this._dir){var t=s.userInfo().username,n=i.createHash("md5").update(this._cacheBreaker).update(t).digest("hex"),r=l.join(s.tmpdir(),"".concat(this._name,"-").concat(n));if(!o.existsSync(r))try{o.mkdirSync(r)}catch(e){if("EEXIST"!==e.code)throw e}this._dir=r}return l.join(this._dir,e)},t.getOrCompute=function(e,t){var n=this;return a.run("RelayCompilerCache.getOrCompute",(function(){var r=n._getFile(e);if(o.existsSync(r))try{return JSON.parse(o.readFileSync(r,"utf8"))}catch(e){}var a=t();try{o.writeFileSync(r,JSON.stringify(a),"utf8")}catch(e){}return a}))},e}();e.exports=u},function(e,t){e.exports=require("os")},function(e,t,n){"use strict";var r=n(11);e.exports=function(e){var t=r.basename(e,r.extname(e)),n="index"===(t=t.replace(/(\.(?!ios|android)[_a-zA-Z0-9\\-]+)+/g,""))?r.basename(r.dirname(e)):t;return n=n.replace(/[^a-zA-Z0-9]+(\w?)/g,(function(e,t){return t.toUpperCase()}))}},function(e,t,n){"use strict";var r=function(){function e(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this._reporters=t}var t=e.prototype;return t.reportMessage=function(e){this._reporters.forEach((function(t){t.reportMessage(e)}))},t.reportTime=function(e,t){this._reporters.forEach((function(n){n.reportTime(e,t)}))},t.reportError=function(e,t){this._reporters.forEach((function(n){n.reportError(e,t)}))},e}();e.exports=r},function(e,t,n){"use strict";var r=n(0),a=r(n(2)),i=r(n(6)),o=n(54),s=n(24),l=n(21),u=n(55),c=n(1),f=c.createCompilerError,d=c.createUserError,p=n(12),m=p.getStorageKey,h=p.stableCopy;function v(e,t){var n=[];return t.forEach((function(t){var r;switch(t.kind){case"Condition":n.push(function(e,t){if("Variable"!==t.condition.kind)throw f("NormalizationCodeGenerator: Expected 'Condition' with static value to be pruned or inlined",[t.condition.loc]);return{condition:t.condition.variableName,kind:"Condition",passingValue:t.passingValue,selections:v(e,t.selections)}}(e,t));break;case"ClientExtension":n.push(function(e,t){return{kind:"ClientExtension",selections:v(e,t.selections)}}(e,t));break;case"ScalarField":var o=null===(r=t.metadata)||void 0===r?void 0:r.abstractKey;"string"==typeof o?n.push(function(e){return{kind:"TypeDiscriminator",abstractKey:e}}(o)):n.push.apply(n,(0,i.default)(function(e){var t,n;if(null===(t=e.metadata)||void 0===t?void 0:t.skipNormalizationNode)return[];var r=e.handles&&e.handles.map((function(t){if(null!=t.dynamicKey)throw d("Dynamic key values are not supported on scalar fields.",[t.dynamicKey.loc]);var n={alias:e.alias===e.name?null:e.alias,args:g(e.args),filters:t.filters,handle:t.name,key:t.key,kind:"ScalarHandle",name:e.name};return null!=t.handleArgs&&(n.handleArgs=g(t.handleArgs)),n}))||[],i={alias:e.alias===e.name?null:e.alias,args:g(e.args),kind:"ScalarField",name:e.name,storageKey:null},o=T(i,e.metadata);null!=o&&(i=(0,a.default)((0,a.default)({},i),{},{storageKey:o}));!0===(null===(n=e.metadata)||void 0===n?void 0:n.flight)&&(i=(0,a.default)((0,a.default)({},i),{},{kind:"FlightField"}));return[i].concat(r)}(t)));break;case"ModuleImport":n.push(function(e){var t=e.name,n=t.match(/^([a-zA-Z][a-zA-Z0-9]*)(?:_([a-zA-Z][_a-zA-Z0-9]*))?$/);if(!n)throw f("NormalizationCodeGenerator: @module fragments should be named "+"'FragmentName_propName', got '".concat(t,"'."),[e.loc]);var r=n[2];if("string"!=typeof r)throw f("NormalizationCodeGenerator: @module fragments should be named "+"'FragmentName_propName', got '".concat(t,"'."),[e.loc]);return{args:null,documentName:e.key,fragmentName:t,fragmentPropName:r,kind:"ModuleImport"}}(t));break;case"InlineFragment":n.push(function(e,t){var n=e.getRawType(t.typeCondition),r=e.isAbstractType(n),a=r?s(e,n):null,i=v(e,t.selections);if(r){var o=l(i,(function(e){return"TypeDiscriminator"===e.kind&&e.abstractKey===a})),u=o[0],c=o[1],f=u[0];if(null!=f&&0===c.length)return f;i=c}return{kind:"InlineFragment",selections:i,type:e.getTypeString(n),abstractKey:a}}(e,t));break;case"LinkedField":n.push.apply(n,(0,i.default)(function(e,t){var n=t.handles&&t.handles.map((function(e){var n={alias:t.alias===t.name?null:t.alias,args:g(t.args),filters:e.filters,handle:e.name,key:e.key,kind:"LinkedHandle",name:t.name};if(null!=e.dynamicKey){n=(0,a.default)((0,a.default)({},n),{},{dynamicKey:{kind:"Variable",name:"__dynamicKey",variableName:e.dynamicKey.variableName}})}if(null!=e.handleArgs){var r=g(e.handleArgs);null!=r&&(n=(0,a.default)((0,a.default)({},n),{},{handleArgs:r}))}return n}))||[],r=e.getRawType(t.type),i={alias:t.alias===t.name?null:t.alias,args:g(t.args),concreteType:e.isAbstractType(r)?null:e.getTypeString(r),kind:"LinkedField",name:t.name,plural:S(e,t.type),selections:v(e,t.selections),storageKey:null},o=T(i,t.metadata);null!=o&&(i=(0,a.default)((0,a.default)({},i),{},{storageKey:o}));return[i].concat(n)}(e,t)));break;case"Defer":n.push(function(e,t){var n,r;if(null!=t.if&&"Variable"!==t.if.kind&&("Literal"!==t.if.kind||!0!==t.if.value))throw f("NormalizationCodeGenerator: Expected @defer `if` condition to be a variable, unspecified, or the literal `true`.",[null!==(n=null===(r=t.if)||void 0===r?void 0:r.loc)&&void 0!==n?n:t.loc]);return{if:null!=t.if&&"Variable"===t.if.kind?t.if.variableName:null,kind:"Defer",label:t.label,selections:v(e,t.selections)}}(e,t));break;case"Stream":n.push(function(e,t){var n,r;if(null!=t.if&&"Variable"!==t.if.kind&&("Literal"!==t.if.kind||!0!==t.if.value))throw f("NormalizationCodeGenerator: Expected @stream `if` condition to be a variable, unspecified, or the literal `true`.",[null!==(n=null===(r=t.if)||void 0===r?void 0:r.loc)&&void 0!==n?n:t.loc]);return{if:null!=t.if&&"Variable"===t.if.kind?t.if.variableName:null,kind:"Stream",label:t.label,selections:v(e,t.selections)}}(e,t));break;case"InlineDataFragmentSpread":case"FragmentSpread":throw new f("NormalizationCodeGenerator: Unexpected IR node ".concat(t.kind,"."),[t.loc]);default:throw new Error}})),n}function y(e,t){return t.map((function(e){return{defaultValue:h(e.defaultValue),kind:"LocalArgument",name:e.name}}))}function g(e){var t=[];return e.forEach((function(e){var n=function e(t,n){switch(n.kind){case"Variable":return{kind:"Variable",name:t,variableName:n.variableName};case"Literal":return null===n.value?null:{kind:"Literal",name:t,value:h(n.value)};case"ObjectValue":var r=n.fields.map((function(e){return e.name})).sort(),a=new Map(n.fields.map((function(e){return[e.name,e.value]})));return{fields:r.map((function(t){var n,r=a.get(t);if(null==r)throw f("Expected to have object field value");return null!==(n=e(t,r))&&void 0!==n?n:{kind:"Literal",name:t,value:null}})),kind:"ObjectValue",name:t};case"ListValue":return{items:n.items.map((function(n,r){return e("".concat(t,".").concat(r),n)})),kind:"ListValue",name:t};default:throw d("NormalizationCodeGenerator: Complex argument values (Lists or InputObjects with nested variables) are not supported.",[n.loc])}}(e.name,e.value);null!==n&&t.push(n)})),0===t.length?null:t.sort(b)}function b(e,t){return e.name<t.name?-1:e.name>t.name?1:0}function T(e,t){var n=null==t?void 0:t.storageKey;return"string"==typeof n?n:!e.args||0===e.args.length||e.args.some(o)?null:m(e,{})}function S(e,t){return e.isList(e.getNullableType(t))}e.exports={generate:function(e,t){switch(t.kind){case"Root":return function(e,t){return{argumentDefinitions:y(e,t.argumentDefinitions),kind:"Operation",name:t.name,selections:v(e,t.selections)}}(e,t);case"SplitOperation":return function(e,t){return{kind:"SplitOperation",metadata:u(t.metadata),name:t.name,selections:v(e,t.selections)}}(e,t);default:throw f("NormalizationCodeGenerator: Unsupported AST kind '".concat(t.kind,"'."),[t.loc])}}}},function(e,t,n){"use strict";var r=n(0)(n(2)),a=n(31),i=n(54),o=n(24),s=n(1),l=s.createCompilerError,u=s.createUserError,c=n(12),f=c.getStorageKey,d=c.stableCopy;function p(e,t){return t.map((function(t){var n,a;switch(t.kind){case"ClientExtension":return function(e,t){return{kind:"ClientExtension",selections:p(e,t.selections)}}(e,t);case"FragmentSpread":return{args:v((a=t).args),kind:"FragmentSpread",name:a.name};case"Condition":return function(e,t){if("Variable"!==t.condition.kind)throw l("ReaderCodeGenerator: Expected 'Condition' with static value to be pruned or inlined",[t.condition.loc]);return{condition:t.condition.variableName,kind:"Condition",passingValue:t.passingValue,selections:p(e,t.selections)}}(e,t);case"ScalarField":return null!=(null===(n=t.metadata)||void 0===n?void 0:n.abstractKey)?null:function(e,t){var n,a,i={alias:t.alias===t.name?null:t.alias,args:v(t.args),kind:"ScalarField",name:t.name,storageKey:null},o=g(i,t.metadata);o&&(i=(0,r.default)((0,r.default)({},i),{},{storageKey:o}));!0===(null===(n=t.metadata)||void 0===n?void 0:n.flight)&&(i=(0,r.default)((0,r.default)({},i),{},{kind:"FlightField"}));var s=null===(a=t.metadata)||void 0===a?void 0:a.required;if(null!=s){if("FlightField"===i.kind)throw new u("@required cannot be used on a ReactFlightComponent.",[t.loc]);return h(i,s)}return i}(0,t);case"ModuleImport":return function(e,t){var n=t.name,r=n.match(/^([a-zA-Z][a-zA-Z0-9]*)(?:_([a-zA-Z][_a-zA-Z0-9]*))?$/);if(!r)throw l("ReaderCodeGenerator: @match fragments should be named "+"'FragmentName_propName', got '".concat(n,"'."),[t.loc]);var a=r[2];if("string"!=typeof a)throw l("ReaderCodeGenerator: @module fragments should be named "+"'FragmentName_propName', got '".concat(n,"'."),[t.loc]);return{documentName:t.key,fragmentName:n,fragmentPropName:a,kind:"ModuleImport"}}(0,t);case"InlineDataFragmentSpread":return function(e,t){return{kind:"InlineDataFragmentSpread",name:t.name,selections:p(e,t.selections)}}(e,t);case"InlineFragment":return function(e,t){var n=e.getRawType(t.typeCondition);return{kind:"InlineFragment",selections:p(e,t.selections),type:e.getTypeString(n),abstractKey:e.isAbstractType(n)?o(e,n):null}}(e,t);case"LinkedField":return function(e,t){var n,a=e.getRawType(t.type),i={alias:t.alias===t.name?null:t.alias,args:v(t.args),concreteType:e.isAbstractType(a)?null:e.getTypeString(a),kind:"LinkedField",name:t.name,plural:b(e,t.type),selections:p(e,t.selections),storageKey:null},o=g(i,t.metadata);o&&(i=(0,r.default)((0,r.default)({},i),{},{storageKey:o}));var s=null===(n=t.metadata)||void 0===n?void 0:n.required;if(null!=s)return h(i,s);return i}(e,t);case"Defer":return function(e,t){return{kind:"Defer",selections:p(e,t.selections)}}(e,t);case"Stream":return function(e,t){return{kind:"Stream",selections:p(e,t.selections)}}(e,t);default:throw new Error}})).filter(Boolean)}function m(e,t){return t.map((function(e){switch(e.kind){case"LocalArgumentDefinition":return{defaultValue:d(e.defaultValue),kind:"LocalArgument",name:e.name};case"RootArgumentDefinition":return{kind:"RootArgument",name:e.name};default:throw new Error}})).sort((function(e,t){return e.name>t.name?1:e.name<t.name?-1:0}))}function h(e,t){return{kind:"RequiredField",field:e,action:t.action,path:t.path}}function v(e){var t=[];return e.forEach((function(e){var n=function e(t,n){switch(n.kind){case"Variable":return{kind:"Variable",name:t,variableName:n.variableName};case"Literal":return null===n.value?null:{kind:"Literal",name:t,value:d(n.value)};case"ObjectValue":var r=n.fields.map((function(e){return e.name})).sort(),a=new Map(n.fields.map((function(e){return[e.name,e.value]})));return{fields:r.map((function(t){var n,r=a.get(t);if(null==r)throw l("Expected to have object field value");return null!==(n=e(t,r))&&void 0!==n?n:{kind:"Literal",name:t,value:null}})),kind:"ObjectValue",name:t};case"ListValue":return{items:n.items.map((function(n,r){return e("".concat(t,".").concat(r),n)})),kind:"ListValue",name:t};default:throw u("ReaderCodeGenerator: Complex argument values (Lists or InputObjects with nested variables) are not supported.",[n.loc])}}(e.name,e.value);null!==n&&t.push(n)})),0===t.length?null:t.sort(y)}function y(e,t){return e.name<t.name?-1:e.name>t.name?1:0}function g(e,t){var n=null==t?void 0:t.storageKey;return"string"==typeof n?n:!e.args||0===e.args.length||e.args.some(i)?null:f(e,{})}function b(e,t){return e.isList(e.getNullableType(t))}e.exports={generate:function(e,t){if(null==t)return t;var n=null;if(null!=t.metadata){var i,s,l,u,c=t.metadata,f=c.mask,d=c.plural,h=c.connection,v=c.refetch;if(Array.isArray(h))(n=null!==(i=n)&&void 0!==i?i:{}).connection=h;if("boolean"==typeof f)(n=null!==(s=n)&&void 0!==s?s:{}).mask=f;if(!0===d)(n=null!==(l=n)&&void 0!==l?l:{}).plural=!0;if(null!=v&&"object"==typeof v)(n=null!==(u=n)&&void 0!==u?u:{}).refetch={connection:v.connection,fragmentPathInResult:v.fragmentPathInResult,operation:a.moduleDependency(v.operation+".graphql")},"string"==typeof v.identifierField&&(n.refetch=(0,r.default)((0,r.default)({},n.refetch),{},{identifierField:v.identifierField}))}var y=e.getRawType(t.type);return{argumentDefinitions:m(e,t.argumentDefinitions),kind:"Fragment",metadata:n,name:t.name,selections:p(e,t.selections),type:e.getTypeString(y),abstractKey:e.isAbstractType(y)?o(e,y):null}}}},function(e,t,n){"use strict";var r=n(1).createCompilerError;e.exports=function(e){if(0===e.length)throw r("Expected an array of strings. Got empty array");if(1===e.length)return e[0];if(e.length>5)return e.slice(0,5).join(", ")+", ...";var t=e.slice(),n=t.pop();return t.join(", ")+" or "+n}},function(e,t){e.exports=require("@babel/runtime/helpers/objectWithoutPropertiesLoose")},function(e,t,n){"use strict";e.exports=function(){return function(e){return"require('./".concat(e,"')")}}},function(e,t,n){"use strict";e.exports=function(e){var t=new Map,n=new WeakMap,r=[];!function e(r){if(null==r||"object"!=typeof r)return JSON.stringify(r);var a;if(Array.isArray(r)){a="[";for(var i=0;i<r.length;i++)a+=e(r[i])+","}else for(var o in a="{",r)r.hasOwnProperty(o)&&void 0!==r[o]&&(a+=o+":"+e(r[o])+",");var s=t.get(a);s||(s={value:r,hash:a,count:0},t.set(a,s));return n.set(r,s),a}(e),function e(t){if(null==t||"object"!=typeof t)return;var r=n.get(t);if(r&&r.hash.length>2&&(r.count++,r.count>1))return;if(Array.isArray(t))for(var a=0;a<t.length;a++)e(t[a]);else for(var i in t)t.hasOwnProperty(i)&&void 0!==t[i]&&e(t[i])}(e);var a=function e(t,a,i){if(null==i||"object"!=typeof i)return JSON.stringify(i);if(""!==a){var o=n.get(i);if(o&&o.count>1){var s=o.varName;if(null==s){var l=e(!0,"",i);s=o.varName="v"+r.length,r.push(o.varName+" = "+l)}return"("+s+"/*: any*/)"}}var u,c=!0,f=a+"  ";if(Array.isArray(i)){if(t&&0===i.length)return"([]/*: any*/)";u="[";for(var d=0;d<i.length;d++)u+=(c?"\n":",\n")+f+e(t,f,i[d]),c=!1;u+=c?"]":"\n".concat(a,"]")}else{for(var p in u="{",i)i.hasOwnProperty(p)&&void 0!==i[p]&&(u+=(c?"\n":",\n")+f+JSON.stringify(p)+": "+e(t,f,i[p]),c=!1);u+=c?"}":"\n".concat(a,"}")}return u}(!1,"",e);return 0===r.length?a:"(function(){\nvar ".concat(r.join(",\n"),";\nreturn ").concat(a,";\n})()")}},function(e,t,n){"use strict";var r=[],a=[];e.exports=
/**
 * Checks if two values are equal. Values may be primitives, arrays, or objects.
 * Returns true if both arguments have the same keys and values.
 *
 * @see http://underscorejs.org
 * @copyright 2009-2013 Jeremy Ashkenas, DocumentCloud Inc.
 * @license MIT
 */
function(e,t){var n=r.length?r.pop():[],i=a.length?a.pop():[],o=function e(t,n,r,a){if(t===n)return 0!==t||1/t==1/n;if(null==t||null==n)return!1;if("object"!=typeof t||"object"!=typeof n)return!1;var i=Object.prototype.toString,o=i.call(t);if(o!==i.call(n))return!1;switch(o){case"[object String]":return t===String(n);case"[object Number]":return!isNaN(t)&&!isNaN(n)&&t===Number(n);case"[object Date]":case"[object Boolean]":return+t==+n;case"[object RegExp]":return t.source===n.source&&t.global===n.global&&t.multiline===n.multiline&&t.ignoreCase===n.ignoreCase}var s=r.length;for(;s--;)if(r[s]===t)return a[s]===n;r.push(t),a.push(n);var l=0;if("[object Array]"===o){if((l=t.length)!==n.length)return!1;for(;l--;)if(!e(t[l],n[l],r,a))return!1}else{if(t.constructor!==n.constructor)return!1;if(t.hasOwnProperty("valueOf")&&n.hasOwnProperty("valueOf"))return t.valueOf()===n.valueOf();var u=Object.keys(t);if(u.length!==Object.keys(n).length)return!1;for(var c=0;c<u.length;c++)if(!("_owner"===u[c]||n.hasOwnProperty(u[c])&&e(t[u[c]],n[u[c]],r,a)))return!1}return r.pop(),a.pop(),!0}(e,t,n,i);return n.length=0,i.length=0,r.push(n),a.push(i),o}},function(e,t,n){"use strict";var r=n(1).createUserError;e.exports=function(e,t,n,a){var i=new Map;return t.argumentDefinitions.forEach((function(e){i.set(e.name,e)})),n.forEach((function(t){var n=i.get(t.name),o=null==n?t:function(e,t,n,a){if(t.kind!==n.kind)throw r("Cannot combine global and local variables when applying "+"".concat(a,"."),[t.loc,n.loc]);if("LocalArgumentDefinition"===t.kind&&"LocalArgumentDefinition"===n.kind&&t.defaultValue!==n.defaultValue)throw r("Cannot combine local variables with different defaultValues when "+"applying ".concat(a,"."),[t.loc,n.loc]);if(e.isTypeSubTypeOf(n.type,t.type))return n;if(e.isTypeSubTypeOf(t.type,n.type))return t;var i=null!=t.type?e.getTypeString(t.type):"unknown",o=null!=n.type?e.getTypeString(n.type):"unknown";throw r("Cannot combine variables with incompatible types "+"".concat(i," and ").concat(o," ")+"when applying ".concat(a,"."),[t.loc,n.loc])}(e,n,t,a);i.set(o.name,o)})),Array.from(i.values())}},function(e,t,n){"use strict";var r=n(0)(n(3)),a=n(96),i=n(97),o=n(98),s=n(99),l=n(1).createUserError,u=[s,o,i,a];e.exports={buildRefetchOperation:function(e,t,n){var a,i=(0,r.default)(u);try{for(i.s();!(a=i.n()).done;){var o=a.value.buildRefetchOperation(e,t,n);if(null!=o)return o}}catch(e){i.e(e)}finally{i.f()}throw l("Invalid use of @refetchable on fragment '".concat(t.name,"', only ")+"supported are fragments on:\n"+u.map((function(e){return" - ".concat(e.description)})).join("\n"),[t.loc])}}},function(e,t,n){"use strict";var r=n(0),a=r(n(2)),i=r(n(6)),o=n(10),s=n(1).createUserError,l=n(27),u=l.buildFragmentSpread,c=l.buildOperationArgumentDefinitions;function f(e,t,n){if(t.selections.find((function(t){return"ScalarField"===t.kind&&t.name===n&&t.alias===n&&e.areEqualTypes(e.getNullableType(t.type),e.expectIdType())})))return t;var r=o.generateIDField(e,t.type);return r.alias=n,r.name=n,(0,a.default)((0,a.default)({},t),{},{selections:[].concat((0,i.default)(t.selections),[r])})}e.exports={description:"@fetchable types",buildRefetchOperation:function(e,t,n){var r=null;if(e.isObject(t.type)){var a=e.assertObjectType(t.type);r=e.getFetchableFieldName(a)}if(null==r)return null;var l=e.getFieldConfig(e.expectField(t.type,r));if(!e.isId(e.getRawType(l.type))){var d=e.getTypeString(t.type);throw s("Invalid use of @refetchable on fragment '".concat(t.name,"', the type ")+"'".concat(d,"' is @fetchable but the identifying field '").concat(r,"' ")+"does not have type 'ID'.",[t.loc])}var p=e.expectQueryType(),m="fetch__".concat(e.getTypeString(t.type)),h=e.getFieldConfig(e.expectField(p,m));if(!(null!=h&&e.isObject(h.type)&&e.areEqualTypes(h.type,t.type)&&e.areEqualTypes(e.getNullableType(h.args[0].type),e.expectIdType()))){var v=e.getTypeString(t.type);throw s("Invalid use of @refetchable on fragment '".concat(t.name,"', the type ")+"'".concat(v,"' is @fetchable but there is no corresponding '").concat(m,"'")+"field or it is invalid (expected '".concat(m,"(id: ID!): ").concat(v,"')."),[t.loc])}var y=h.args[0].name,g=h.args[0].type,b=o.getNonNullIdInput(e),T=c(t.argumentDefinitions),S=T.find((function(e){return"id"===e.name}));if(null!=S)throw s("Invalid use of @refetchable on fragment `".concat(t.name,"`, this ")+"fragment already has an `$id` variable in scope.",[S.loc]);return{identifierField:r,path:[m],node:{argumentDefinitions:[].concat((0,i.default)(T),[{defaultValue:null,kind:"LocalArgumentDefinition",loc:{kind:"Derived",source:t.loc},name:"id",type:b}]),directives:[],kind:"Root",loc:{kind:"Derived",source:t.loc},metadata:null,name:n,operation:"query",selections:[{alias:m,args:[{kind:"Argument",loc:{kind:"Derived",source:t.loc},name:y,type:e.assertInputType(g),value:{kind:"Variable",loc:{kind:"Derived",source:t.loc},variableName:"id",type:b}}],connection:!1,directives:[],handles:null,kind:"LinkedField",loc:{kind:"Derived",source:t.loc},metadata:null,name:m,selections:[u(t)],type:t.type}],type:p},transformedFragment:f(e,t,r)}}}},function(e,t,n){"use strict";var r=n(0),a=r(n(2)),i=r(n(6)),o=n(10),s=n(15),l=n(1).createUserError,u=n(27),c=u.buildFragmentSpread,f=u.buildOperationArgumentDefinitions;function d(e,t){if(t.selections.find((function(t){return"ScalarField"===t.kind&&"id"===t.name&&"id"===t.alias&&e.areEqualTypes(e.getNullableType(t.type),e.expectIdType())})))return t;var n=e.getFieldByName(t.type,"id"),r=e.assertCompositeType(s(e.getTypeFromString("Node"))),l=n?o.generateIDField(e,t.type):{kind:"InlineFragment",directives:[],loc:{kind:"Generated"},metadata:null,selections:[o.generateIDField(e,r)],typeCondition:r};return(0,a.default)((0,a.default)({},t),{},{selections:[].concat((0,i.default)(t.selections),[l])})}e.exports={description:"the Node interface or types implementing the Node interface",buildRefetchOperation:function(e,t,n){if(!("Node"===e.getTypeString(t.type)||e.isObject(t.type)&&e.getInterfaces(e.assertCompositeType(t.type)).some((function(t){return e.areEqualTypes(t,e.expectTypeFromString("Node"))}))||e.isAbstractType(t.type)&&Array.from(e.getPossibleTypes(e.assertAbstractType(t.type))).every((function(t){return e.implementsInterface(e.assertCompositeType(t),e.assertInterfaceType(e.expectTypeFromString("Node")))}))))return null;var r=e.expectQueryType(),a=e.getTypeFromString("Node"),s=e.getFieldConfig(e.expectField(r,"node"));if(!(a&&e.isInterface(a)&&e.isInterface(s.type)&&e.areEqualTypes(s.type,a)&&1===s.args.length&&e.areEqualTypes(e.getNullableType(s.args[0].type),e.expectIdType())&&(e.isObject(t.type)&&e.getInterfaces(e.assertCompositeType(t.type)).some((function(t){return e.areEqualTypes(t,a)}))||e.isAbstractType(t.type)&&Array.from(e.getPossibleTypes(e.assertAbstractType(t.type))).every((function(t){return e.getInterfaces(e.assertCompositeType(t)).some((function(t){return e.areEqualTypes(t,a)}))})))))throw l("Invalid use of @refetchable on fragment '".concat(t.name,"', check ")+"that your schema defines a `Node { id: ID }` interface and has a `node(id: ID): Node` field on the query type (the id argument may also be non-null).",[t.loc]);var u=s.args[0].name,p=s.args[0].type,m=o.getNonNullIdInput(e),h=f(t.argumentDefinitions),v=h.find((function(e){return"id"===e.name}));if(null!=v)throw l("Invalid use of @refetchable on fragment `".concat(t.name,"`, this ")+"fragment already has an `$id` variable in scope.",[v.loc]);return{identifierField:"id",path:["node"],node:{argumentDefinitions:[].concat((0,i.default)(h),[{defaultValue:null,kind:"LocalArgumentDefinition",loc:{kind:"Derived",source:t.loc},name:"id",type:m}]),directives:[],kind:"Root",loc:{kind:"Derived",source:t.loc},metadata:null,name:n,operation:"query",selections:[{alias:"node",args:[{kind:"Argument",loc:{kind:"Derived",source:t.loc},name:u,type:e.assertInputType(p),value:{kind:"Variable",loc:{kind:"Derived",source:t.loc},variableName:"id",type:m}}],connection:!1,directives:[],handles:null,kind:"LinkedField",loc:{kind:"Derived",source:t.loc},metadata:null,name:"node",selections:[c(t)],type:e.assertLinkedFieldType(a)}],type:r},transformedFragment:d(e,t)}}}},function(e,t,n){"use strict";var r=n(27),a=r.buildFragmentSpread,i=r.buildOperationArgumentDefinitions;e.exports={description:"the Query type",buildRefetchOperation:function(e,t,n){var r=e.expectQueryType();return e.areEqualTypes(t.type,r)?{identifierField:null,path:[],node:{argumentDefinitions:i(t.argumentDefinitions),directives:[],kind:"Root",loc:{kind:"Derived",source:t.loc},metadata:null,name:n,operation:"query",selections:[a(t)],type:r},transformedFragment:t}:null}}},function(e,t,n){"use strict";var r=n(1).createUserError,a=n(27),i=a.buildFragmentSpread,o=a.buildOperationArgumentDefinitions;e.exports={description:"the Viewer type",buildRefetchOperation:function(e,t,n){if("Viewer"!==e.getTypeString(t.type))return null;var a=e.expectQueryType(),s=e.getTypeFromString("Viewer"),l=e.getFieldConfig(e.expectField(a,"viewer")),u=e.getNullableType(l.type);if(!(s&&e.isObject(s)&&e.isObject(u)&&e.areEqualTypes(u,s)&&0===l.args.length&&e.areEqualTypes(t.type,s)))throw r("Invalid use of @refetchable on fragment '".concat(t.name,"', check ")+"that your schema defines a 'Viewer' object type and has a 'viewer: Viewer' field on the query type.",[t.loc]);return{identifierField:null,path:["viewer"],node:{argumentDefinitions:o(t.argumentDefinitions),directives:[],kind:"Root",loc:{kind:"Derived",source:t.loc},metadata:null,name:n,operation:"query",selections:[{alias:"viewer",args:[],connection:!1,directives:[],handles:null,kind:"LinkedField",loc:{kind:"Derived",source:t.loc},metadata:null,name:"viewer",selections:[i(t)],type:e.assertLinkedFieldType(l.type)}],type:a},transformedFragment:t}}}},function(e,t,n){"use strict";var r=n(39),a=n(68),i=a.exactObjectTypeAnnotation,o=a.readOnlyArrayOfType;function s(e,t,n,a){return e.isNonNull(t)?l(e,e.getNullableType(t),n,a):r.nullableTypeAnnotation(l(e,t,n,a))}function l(e,t,n,r){if(e.isList(t))return o(s(e,e.getListItemType(t),n,r));if(e.isObject(t)||e.isUnion(t)||e.isInterface(t))return r;if(e.isScalar(t))return u(e.getTypeString(t),n);if(e.isEnum(t))return c(e,e.assertEnumType(t),n);throw new Error("Could not convert from GraphQL type ".concat(String(t)))}function u(e,t){var n=t.customScalars[e];if("function"==typeof n)return n(r);switch(null!=n?n:e){case"ID":case"String":return r.stringTypeAnnotation();case"Float":case"Int":return r.numberTypeAnnotation();case"Boolean":return r.booleanTypeAnnotation();default:return null==n?r.anyTypeAnnotation():r.genericTypeAnnotation(r.identifier(n))}}function c(e,t,n){return n.usedEnums[e.getTypeString(t)]=t,r.genericTypeAnnotation(r.identifier(e.getTypeString(t)))}function f(e,t,n){return e.isNonNull(t)?d(e,e.getNullableType(t),n):r.nullableTypeAnnotation(d(e,t,n))}function d(e,t,n){if(e.isList(t))return o(f(e,e.getListItemType(t),n));if(e.isScalar(t))return u(e.getTypeString(t),n);if(e.isEnum(t))return c(e,e.assertEnumType(t),n);if(e.isInputObject(t)){var a=function(e,t){return e.getTypeString(t)}(e,t);if(n.generatedInputObjectTypes[a])return r.genericTypeAnnotation(r.identifier(a));n.generatedInputObjectTypes[a]="pending";var s=e.getFields(e.assertInputObjectType(t)).map((function(t){var a=e.getFieldType(t),i=e.getFieldName(t),o=r.objectTypeProperty(r.identifier(i),f(e,a,n));return(n.optionalInputFields.indexOf(i)>=0||!e.isNonNull(a))&&(o.optional=!0),o}));return n.generatedInputObjectTypes[a]=i(s),r.genericTypeAnnotation(r.identifier(a))}throw new Error("Could not convert from GraphQL type ".concat(e.getTypeString(t)))}e.exports={transformInputType:f,transformScalarType:s}},function(e,t){e.exports=require("@babel/generator")},function(e,t,n){"use strict";var r=n(0),a=r(n(2)),i=r(n(3)),o=r(n(6)),s=n(4),l=n(103),u=n(40),c=n(70),f=n(1),d=f.createCompilerError,p=f.createNonRecoverableUserError,m=l.getFragmentScope,h=l.getRootScope;function v(e,t,n,r,i){var o=g(e,t,n,r.selections,i);if(!o)return null;if(r.hasOwnProperty("directives")){var s=b(n,r.directives,i);return(0,a.default)((0,a.default)({},r),{},{directives:s,selections:o})}return(0,a.default)((0,a.default)({},r),{},{selections:o})}function y(e,t,n,r,i){var s=b(n,r.directives,i),l=function(e,t,n,r,i,s){var l=e.getSchema(),f=e.getFragment(r.name,r.loc),d=function(e,t,n){if(!e.length)return null;var r=(0,o.default)(e).sort((function(e,t){return e.name<t.name?-1:e.name>t.name?1:0})),a=JSON.stringify(r.map((function(e){var r,a;if("Variable"===e.value.kind){if(null==(r=t[e.value.variableName]))throw p("Variable '$".concat(e.value.variableName,"' is not in scope."),[null===(a=n[0])||void 0===a?void 0:a.loc,e.value.loc].filter(Boolean))}else r=e.value;return{name:e.name,value:u(r)}})));return c(a)}(i,n,s),h=d?"".concat(f.name,"_").concat(d):f.name,v=t.get(h);if(v){if("resolved"===v.kind)return v.value;throw p("Found a circular reference from fragment '".concat(f.name,"'."),s.map((function(e){return e.loc})))}var y=m(l,f.argumentDefinitions,i,n,r);t.set(h,{kind:"pending"});var b=null,T=g(e,t,y,f.selections,s);T&&(b=(0,a.default)((0,a.default)({},f),{},{selections:T,name:h,argumentDefinitions:[]}));return t.set(h,{kind:"resolved",value:b}),b}(e,t,n,r,r.args,[].concat((0,o.default)(i),[r]));return l?(0,a.default)((0,a.default)({},r),{},{kind:"FragmentSpread",args:[],directives:s,name:l.name}):null}function g(e,t,n,r,i){var s=null;return r.forEach((function(r){var l;if("ClientExtension"===r.kind||"InlineDataFragmentSpread"===r.kind||"InlineFragment"===r.kind||"ModuleImport"===r.kind)l=v(e,t,n,r,i);else if("Defer"===r.kind||"Stream"===r.kind)l=function(e,t,n,r,a){var i=v(e,t,n,r,a);if(!i)return null;if(i.if){var o=S(n,i.if,a);if("Literal"===o.kind&&!1===o.value&&r.selections&&1===r.selections.length)return r.selections[0];i.if=o}return i.useCustomizedBatch&&(i.useCustomizedBatch=S(n,i.useCustomizedBatch,a)),i.initialCount&&(i.initialCount=S(n,i.initialCount,a)),i}(e,t,n,r,i);else if("FragmentSpread"===r.kind)l=y(e,t,n,r,i);else if("Condition"===r.kind){var u,c=function(e,t,n,r,i){var o=S(n,r.condition,i);if("Literal"!==o.kind&&"Variable"!==o.kind)throw p("A non-scalar value was applied to an @include or @skip directive, the `if` argument value must be a variable or a literal Boolean.",[o.loc]);if("Literal"===o.kind&&o.value!==r.passingValue)return null;var s=g(e,t,n,r.selections,i);return s?"Literal"===o.kind&&o.value===r.passingValue?s:[(0,a.default)((0,a.default)({},r),{},{condition:o,selections:s})]:null}(e,t,n,r,i);if(c)(u=s=s||[]).push.apply(u,(0,o.default)(c))}else{if("LinkedField"!==r.kind&&"ScalarField"!==r.kind)throw d("ApplyFragmentArgumentTransform: Unsupported kind '".concat(r.kind,"'."),[r.loc]);l=function(e,t,n,r,i){var o=T(n,r.args,i),s=b(n,r.directives,i);if("LinkedField"===r.kind){var l=g(e,t,n,r.selections,i);return l?(0,a.default)((0,a.default)({},r),{},{args:o,directives:s,selections:l}):null}return(0,a.default)((0,a.default)({},r),{},{args:o,directives:s})}(e,t,n,r,i)}l&&(s=s||[]).push(l)})),s}function b(e,t,n){return t.map((function(t){var r=T(e,t.args,n);return(0,a.default)((0,a.default)({},t),{},{args:r})}))}function T(e,t,n){return t.map((function(t){var r=S(e,t.value,n);return r===t.value?t:(0,a.default)((0,a.default)({},t),{},{value:r})}))}function S(e,t,n){if("Variable"===t.kind){var r,i=e[t.variableName];if(null==i)throw p("Variable '$".concat(t.variableName,"' is not in scope."),[null===(r=n[0])||void 0===r?void 0:r.loc,t.loc].filter(Boolean));return i}return"ObjectValue"===t.kind?(0,a.default)((0,a.default)({},t),{},{fields:t.fields.map((function(t){return(0,a.default)((0,a.default)({},t),{},{value:S(e,t.value,n)})}))}):"ListValue"===t.kind?(0,a.default)((0,a.default)({},t),{},{items:t.items.map((function(t){return S(e,t,n)}))}):t}e.exports={transform:function(e){var t,n=new Map,r=s.transform(e,{Root:function(t){var r=h(t.argumentDefinitions);return v(e,n,r,t,[t])},SplitOperation:function(t){return v(e,n,{},t,[t])},Fragment:function(){return null}}),a=(0,i.default)(n.values());try{for(a.s();!(t=a.n()).done;){var o=t.value;"resolved"===o.kind&&o.value&&(r=r.add(o.value))}}catch(e){a.e(e)}finally{a.f()}return r}}},function(e,t,n){"use strict";var r=n(1),a=r.createUserError,i=r.eachWithCombinedError;e.exports={getFragmentScope:function(e,t,n,r,o){var s=new Map;n.forEach((function(e){"Literal"===e.value.kind?s.set(e.name,e.value):"Variable"===e.value.kind&&s.set(e.name,r[e.value.variableName])}));var l={};return i(t,(function(t){if("RootArgumentDefinition"===t.kind){if(s.has(t.name)){var r,i=n.find((function(e){return e.name===t.name}));throw a("Unexpected argument '".concat(t.name,"' supplied to fragment '").concat(o.name,"'. @arguments may only be provided for variables defined in the fragment's @argumentDefinitions."),[null!==(r=null==i?void 0:i.loc)&&void 0!==r?r:o.loc])}l[t.name]={kind:"Variable",loc:t.loc,variableName:t.name,type:t.type}}else{var u=s.get(t.name);if(null==u||"Literal"===u.kind&&null==u.value){if(null==t.defaultValue&&e.isNonNull(t.type)){var c,f=n.find((function(e){return e.name===t.name}));throw a("No value found for required argument '".concat(t.name,": ").concat(e.getTypeString(t.type),"' on fragment '").concat(o.name,"'."),[null!==(c=null==f?void 0:f.loc)&&void 0!==c?c:o.loc])}l[t.name]={kind:"Literal",value:t.defaultValue}}else l[t.name]=u}})),l},getRootScope:function(e){var t={};return e.forEach((function(e){t[e.name]={kind:"Variable",loc:e.loc,variableName:e.name,type:e.type}})),t}}},function(e,t,n){"use strict";var r=n(0),a=r(n(2)),i=r(n(6)),o=n(4),s=n(1),l=s.createCompilerError,u=s.createUserError,c=new Map;function f(e){var t,n=this.getContext(),r=n.getSchema();switch(e.kind){case"Root":switch(e.operation){case"query":t=r.getQueryType();break;case"mutation":t=r.getMutationType();break;case"subscription":t=r.getSubscriptionType();break;default:e.operation}break;case"SplitOperation":if(!r.isServerType(e.type))throw u("ClientExtensionTransform: SplitOperation (@module) can be created only for fragments that defined on a server type",[e.loc]);t=e.type;break;case"Fragment":t=e.type}if(null==t)throw u("ClientExtensionTransform: Expected the type of `".concat(e.name,"` to have been defined in the schema. Make sure both server and ")+"client schema are up to date.",[e.loc]);return function e(t,n,r){var o=c.get(t);null==o&&(o=new Map,c.set(t,o));var s=o.get(r);if(null!=s)return s;var u=n.getSchema(),f=[],d=function(e,t){for(var n=0;n<e.length;n++){var r=e[n],a=t(r);if(r!==a){var i=e.slice(0,n);null!=a&&i.push(a);for(var o=n+1;o<e.length;o++){var s=t(e[o]);null!=s&&i.push(s)}return i}}return e}(t.selections,(function(t){switch(t.kind){case"ClientExtension":throw l("Unexpected ClientExtension node before ClientExtensionTransform",[t.loc]);case"Condition":case"Defer":case"InlineDataFragmentSpread":case"ModuleImport":case"Stream":return e(t,n,r);case"ScalarField":return u.isClientDefinedField(u.assertCompositeType(u.getRawType(r)),t)?(f.push(t),null):t;case"LinkedField":return u.isClientDefinedField(u.assertCompositeType(u.getRawType(r)),t)?(f.push(t),null):e(t,n,t.type);case"InlineFragment":return!u.isServerType(t.typeCondition)?(f.push(t),null):e(t,n,t.typeCondition);case"FragmentSpread":return t;default:throw l("ClientExtensionTransform: Unexpected selection of kind `".concat(t.kind,"`."),[t.loc])}}));s=0===f.length?d===t.selections?t:(0,a.default)((0,a.default)({},t),{},{selections:d}):(0,a.default)((0,a.default)({},t),{},{selections:[].concat((0,i.default)(d),[{kind:"ClientExtension",loc:t.loc,metadata:null,selections:f}])});return o.set(r,s),s}(e,n,t)}e.exports={transform:function(e){return c=new Map,o.transform(e,{Fragment:f,Root:f,SplitOperation:f})}}},function(e,t,n){"use strict";var r=n(0),a=r(n(2)),i=r(n(6)),o=n(4),s=n(25),l=n(10),u=n(22),c=n(1),f=c.createCompilerError,d=c.createUserError,p=n(7).parse,m=n(12),h=m.ConnectionInterface,v=m.RelayFeatureFlags;function y(e,t){var n=this.traverse(e,t),r=t.connectionMetadata;return r.length?(0,a.default)((0,a.default)({},n),{},{metadata:(0,a.default)((0,a.default)({},n.metadata),{},{connection:r})}):n}function g(e,t){var n,r=this.getContext().getSchema(),o=r.getNullableType(e.type),c=r.isList(o),m=t.path.concat(c?null:e.alias||e.name),y=this.traverse(e,(0,a.default)((0,a.default)({},t),{},{path:m})),g=e.directives.find((function(e){return"connection"===e.name||"stream_connection"===e.name}));if(!g)return y;if(!r.isObject(o)&&!r.isInterface(o))throw new d("@".concat(g.name," used on invalid field '").concat(e.name,"'. ")+"Expected the return type to be a non-plural interface or object, "+"got '".concat(r.getTypeString(e.type),"'."),[y.loc]);!function(e){var t=h.get().EDGES;if(!T(e,"first")&&!T(e,"last"))throw d("Expected field '".concat(e.name,"' to have a '").concat("first","' or '").concat("last","' ")+"argument.",[e.loc]);if(!e.selections.some((function(e){return"LinkedField"===e.kind&&e.name===t})))throw d("Expected field '".concat(e.name,"' to have an '").concat(t,"' selection."),[e.loc])}(y),function(e,t,n,r){var a=r.name,i=h.get(),o=i.CURSOR,s=i.EDGES,l=i.END_CURSOR,u=i.HAS_NEXT_PAGE,c=i.HAS_PREV_PAGE,f=i.NODE,p=i.PAGE_INFO,m=i.START_CURSOR,v=e.getTypeString(n);if(!e.hasField(n,s))throw d("@".concat(a," used on invalid field '").concat(t.name,"'. Expected the ")+"field type '".concat(v,"' to have an '").concat(s,"' field"),[t.loc]);var y=e.getFieldConfig(e.expectField(n,s)),g=e.getNullableType(y.type);if(!e.isList(g))throw d("@".concat(a," used on invalid field '").concat(t.name,"'. Expected the ")+"field type '".concat(v,"' to have an '").concat(s,"' field that returns ")+"a list of objects.",[t.loc]);var b=e.getNullableType(e.getListItemType(g));if(!e.isObject(b)&&!e.isInterface(b))throw d("@".concat(a," used on invalid field '").concat(t.name,"'. Expected the ")+"field type '".concat(v,"' to have an '").concat(s,"' field that returns ")+"a list of objects.",[t.loc]);if(b=e.assertCompositeType(b),!e.hasField(b,f))throw d("@".concat(a," used on invalid field '").concat(t.name,"'. Expected the ")+"field type '".concat(v,"' to have an '").concat(s," { ").concat(f," }' field ")+"that returns an object, interface, or union.",[t.loc]);var T=e.getFieldConfig(e.expectField(b,f)),S=e.getNullableType(T.type);if(!e.isAbstractType(S)&&!e.isObject(S))throw d("@".concat(a," used on invalid field '").concat(t.name,"'. Expected the ")+"field type '".concat(v,"' to have an '").concat(s," { ").concat(f," }' field ")+"that returns an object, interface, or union.",[t.loc]);if(!e.hasField(b,o))throw d("@".concat(a," used on invalid field '").concat(t.name,"'. Expected the ")+"field type '".concat(v,"' to have an '").concat(s," { ").concat(o," }' field ")+"that returns a scalar value.",[t.loc]);var w=e.getFieldConfig(e.expectField(b,o));if(!e.isScalar(e.getNullableType(w.type)))throw d("@".concat(a," used on invalid field '").concat(t.name,"'. Expected the ")+"field type '".concat(v,"' to have an '").concat(s," { ").concat(o," }' field ")+"that returns a scalar value.",[t.loc]);if(!e.hasField(n,p))throw d("@".concat(a," used on invalid field '").concat(t.name,"'. Expected the ")+"field type '".concat(v,"' to have a '").concat(p,"' field that returns ")+"an object.",[t.loc]);var _=e.getFieldConfig(e.expectField(n,p)),k=e.getNullableType(_.type);if(!e.isObject(k))throw d("@".concat(a," used on invalid field '").concat(t.name,"'. Expected the ")+"field type '".concat(v,"' to have a '").concat(p,"' field that ")+"returns an object.",[t.loc]);[l,u,c,m].forEach((function(n){var r=e.getFieldConfig(e.expectField(e.assertObjectType(k),n));if(!e.isScalar(e.getNullableType(r.type)))throw d("@".concat(a," used on invalid field '").concat(t.name,"'. Expected ")+"the field type '".concat(v,"' to have a '").concat(p," { ").concat(n," }' ")+"field returns a scalar.",[t.loc])}))}(r,y,r.assertCompositeType(o),g);var S=function(e,t){var n=u(t.args),r=n.handler,a=n.key,i=(n.label,n.filters);if(null!=r&&"string"!=typeof r){var o,s,l=t.args.find((function(e){return"handler"===e.name}));throw d("Expected the ".concat("handler"," argument to @").concat(t.name," to ")+"be a string literal for field ".concat(e.name,"."),[null!==(o=null==l||null===(s=l.value)||void 0===s?void 0:s.loc)&&void 0!==o?o:t.loc])}if("string"!=typeof a){var c,f,p=t.args.find((function(e){return"key"===e.name}));throw d("Expected the ".concat("key"," argument to @").concat(t.name," to be a ")+"string literal for field ".concat(e.name,"."),[null!==(c=null==p||null===(f=p.value)||void 0===f?void 0:f.loc)&&void 0!==c?c:t.loc])}var m=e.alias||e.name;if(!a.endsWith("_"+m)){var y,g,b=t.args.find((function(e){return"key"===e.name}));throw d("Expected the ".concat("key"," argument to @").concat(t.name," to be of ")+"form <SomeName>_".concat(m,", got '").concat(a,"'. ")+"For a detailed explanation, check out https://relay.dev/docs/en/pagination-container#connection",[null!==(y=null==b||null===(g=b.value)||void 0===g?void 0:g.loc)&&void 0!==y?y:t.loc])}if(null!=i&&(!Array.isArray(i)||i.some((function(e){return"string"!=typeof e})))){var T,S,w=t.args.find((function(e){return"filters"===e.name}));throw d("Expected the 'filters' argument to @".concat(t.name," to be ")+"a string literal.",[null!==(T=null==w||null===(S=w.value)||void 0===S?void 0:S.loc)&&void 0!==T?T:t.loc])}var _=i;if(null==_){var k=e.args.filter((function(e){return!h.isConnectionCall({name:e.name,value:null})})).map((function(e){return e.name}));_=0!==k.length?k:null}var F=null;if("stream_connection"===t.name){var E=t.args.find((function(e){return"initial_count"===e.name})),x=t.args.find((function(e){return"use_customized_batch"===e.name})),C=t.args.find((function(e){return"if"===e.name}));F={if:C,initialCount:E,useCustomizedBatch:x,label:a}}var N=t.args.find((function(e){return"dynamicKey_UNSTABLE"===e.name})),D=null;if(null!=N){if(!v.ENABLE_VARIABLE_CONNECTION_KEY||"Variable"!==N.value.kind)throw d("Unsupported 'dynamicKey_UNSTABLE' argument to @".concat(t.name,". This argument is only valid when the feature flag is enabled and ")+"the variable must be a variable",[t.loc]);D=N.value}return{handler:r,key:a,dynamicKey:D,filters:_,stream:F}}(y,g),w=b(y,m,null!=S.stream);t.connectionMetadata.push(w);var _={name:null!==(n=S.handler)&&void 0!==n?n:"connection",key:S.key,dynamicKey:S.dynamicKey,filters:S.filters},k=w.direction;if(null!=k){var F=function(e,t,n,r,o,u,c){var m,v,y,g=e.getSchema(),b={kind:"Derived",source:t.loc},T={kind:"Derived",source:u},S=h.get(),w=S.CURSOR,_=S.EDGES,k=S.END_CURSOR,F=S.HAS_NEXT_PAGE,E=S.HAS_PREV_PAGE,x=S.NODE,C=S.PAGE_INFO,N=S.START_CURSOR;t.selections.forEach((function(e){if("LinkedField"===e.kind){if(e.name===_){if(null!=m)throw f("ConnectionTransform: Unexpected duplicate field '".concat(_,"'."),[m.loc,e.loc]);return void(m=e)}if(e.name===C){if(null!=v)throw f("ConnectionTransform: Unexpected duplicate field '".concat(C,"'."),[v.loc,e.loc]);return void(v=e)}}}));var D=o.stream;null!=D&&(y={args:[D.if,D.initialCount,D.useCustomizedBatch,{kind:"Argument",loc:T,name:"label",type:l.getNullableStringInput(g),value:{kind:"Literal",loc:T,value:D.label}}].filter(Boolean),kind:"Directive",loc:T,name:"stream"});if(m&&m.alias!==m.name){if(D)throw d("@stream_connection does not support aliasing the '".concat(_,"' field."),[m.loc]);m=null}if(v&&v.alias!==v.name){if(D)throw d("@stream_connection does not support aliasing the '".concat(C,"' field."),[v.loc]);v=null}var I=m,A=v,R=g.getFieldConfig(g.expectField(n,_)).type,O=g.getFieldConfig(g.expectField(n,C)).type;null==I&&(I={alias:_,args:[],connection:!1,directives:[],handles:null,kind:"LinkedField",loc:b,metadata:null,name:_,selections:[],type:g.assertLinkedFieldType(R)});null==A&&(A={alias:C,args:[],connection:!1,directives:[],handles:null,kind:"LinkedField",loc:b,metadata:null,name:C,selections:[],type:g.assertLinkedFieldType(O)});var M,L=g.getRawType(O);M="forward"===r?"fragment PageInfo on ".concat(g.getTypeString(L)," {\n      ").concat(k,"\n      ").concat(F,"\n    }"):"backward"===r?"fragment PageInfo on ".concat(g.getTypeString(L),"  {\n      ").concat(E,"\n      ").concat(N,"\n    }"):"fragment PageInfo on ".concat(g.getTypeString(L),"  {\n      ").concat(k,"\n      ").concat(F,"\n      ").concat(E,"\n      ").concat(N,"\n    }");var j,V,q=p(M),P=s.transform(g,[q.definitions[0]])[0];if("LinkedField"!==A.kind)throw f("ConnectionTransform: Expected generated pageInfo selection to be a LinkedField",[t.loc]);A=(0,a.default)((0,a.default)({},A),{},{selections:[].concat((0,i.default)(A.selections),[{directives:[],kind:"InlineFragment",loc:b,metadata:null,selections:P.selections,typeCondition:P.type}])}),null!=D&&(A={if:null!==(j=null===(V=D.if)||void 0===V?void 0:V.value)&&void 0!==j?j:null,label:"".concat(c,"$defer$").concat(D.label,"$").concat(C),kind:"Defer",loc:b,selections:[A]});var U="\n    fragment Edges on ".concat(g.getTypeString(g.getRawType(R))," {\n      ").concat(w,"\n      ").concat(x,' {\n        __typename # rely on GenerateRequisiteFieldTransform to add "id"\n      }\n    }\n  '),G=p(U),B=s.transform(g,[G.definitions[0]])[0];I=(0,a.default)((0,a.default)({},I),{},{directives:null!=y?[].concat((0,i.default)(I.directives),[y]):I.directives,selections:[].concat((0,i.default)(I.selections),[{directives:[],kind:"InlineFragment",loc:b,metadata:null,selections:B.selections,typeCondition:B.type}])});var W=t.selections.map((function(e){return null!=I&&null!=m&&e===m?I:null!=A&&null!=v&&e===v?A:e}));null==m&&null!=I&&W.push(I);null==v&&null!=A&&W.push(A);return W}(this.getContext(),y,r.assertCompositeType(o),k,S,g.loc,t.documentName);y=(0,a.default)((0,a.default)({},y),{},{selections:F})}return(0,a.default)((0,a.default)({},y),{},{directives:y.directives.filter((function(e){return e!==g})),connection:!0,handles:y.handles?[].concat((0,i.default)(y.handles),[_]):[_]})}function b(e,t,n){var r=t.includes(null),a=T(e,"first"),i=T(e,"last"),o=null,s=null,l=null;a&&!i?(o="forward",s=a,l=T(e,"after")):i&&!a?(o="backward",s=i,l=T(e,"before")):i&&a&&(o="bidirectional");var u=s&&"Variable"===s.value.kind?s.value.variableName:null,c=l&&"Variable"===l.value.kind?l.value.variableName:null;return n?{count:u,cursor:c,direction:o,path:r?null:t,stream:!0}:{count:u,cursor:c,direction:o,path:r?null:t}}function T(e,t){return e.args&&e.args.find((function(e){return e.name===t}))}e.exports={buildConnectionMetadata:b,CONNECTION:"connection",SCHEMA_EXTENSION:"\n  directive @connection(\n    key: String!\n    filters: [String]\n    handler: String\n    dynamicKey_UNSTABLE: String\n  ) on FIELD\n\n  directive @stream_connection(\n    key: String!\n    filters: [String]\n    handler: String\n    initial_count: Int!\n    if: Boolean = true\n    use_customized_batch: Boolean = false\n    dynamicKey_UNSTABLE: String\n  ) on FIELD\n",transform:function(e){return o.transform(e,{Fragment:y,LinkedField:g,Root:y},(function(e){return{documentName:e.name,path:[],connectionMetadata:[]}}))}}},function(e,t,n){"use strict";var r=n(0),a=r(n(3)),i=r(n(2)),o=r(n(6)),s=n(4),l=n(1).createUserError,u=n(12).ConnectionInterface,c=["appendEdge","prependEdge"],f=["appendNode","prependNode"],d=[].concat(c,f),p="\n  directive @".concat("deleteRecord"," on FIELD\n  directive @").concat("deleteEdge","(\n    connections: [ID!]!\n  ) on FIELD\n  directive @").concat("appendEdge","(\n    connections: [ID!]!\n  ) on FIELD\n  directive @").concat("prependEdge","(\n    connections: [ID!]!\n  ) on FIELD\n  directive @").concat("appendNode","(\n    connections: [ID!]!\n    edgeTypeName: String!\n  ) on FIELD\n  directive @").concat("prependNode","(\n    connections: [ID!]!\n    edgeTypeName: String!\n  ) on FIELD\n");function m(e){return e}function h(e){var t=e.directives.find((function(e){return d.indexOf(e.name)>-1}));if(null!=t)throw l("Invalid use of @".concat(t.name," on scalar field '").concat(e.name,"'"),[t.loc]);var n=e.directives.find((function(e){return"deleteRecord"===e.name})),r=e.directives.find((function(e){return"deleteEdge"===e.name}));if(null!=n&&null!=r)throw l("Both @deleteNode and @deleteEdge are used on field '".concat(e.name,"'. Only one directive is supported for now."),[n.loc,r.loc]);var a=null!=n?n:r;if(null==a)return e;var s=this.getContext().getSchema();if(!s.isId(s.getRawType(e.type)))throw l("Invalid use of @".concat(a.name," on field '").concat(e.name,"'. Expected field to return an ID or list of ID values, got ").concat(s.getTypeString(e.type),"."),[a.loc]);var u=a.args.find((function(e){return"connections"===e.name})),c={name:a.name,key:"",dynamicKey:null,filters:null,handleArgs:u?[u]:void 0};return(0,i.default)((0,i.default)({},e),{},{directives:e.directives.filter((function(e){return e!==a})),handles:e.handles?[].concat((0,o.default)(e.handles),[c]):[c]})}function v(e){var t=this.traverse(e),n=t.directives.find((function(e){return"deleteRecord"===e.name}));if(null!=n)throw l("Invalid use of @".concat(n.name," on scalar field '").concat(t.name,"'."),[n.loc]);var r=t.directives.find((function(e){return c.indexOf(e.name)>-1})),s=t.directives.find((function(e){return f.indexOf(e.name)>-1}));if(null==r&&null==s)return t;if(null!=r&&null!=s)throw l("Invalid use of @".concat(r.name," and @").concat(s.name," on field '").concat(t.name,"' - these directives cannot be used together."),[r.loc]);var d=null!=r?r:s,p=d.args.find((function(e){return"connections"===e.name}));if(null==p)throw l("Expected the 'connections' argument to be defined on @".concat(d.name,"."),[d.loc]);var m=this.getContext().getSchema();if(r){var h,v,y,g=m.getRawType(t.type),b=m.getFields(g),T=(0,a.default)(b);try{for(T.s();!(y=T.n()).done;){var S=y.value,w=m.getFieldName(S);w===u.get().CURSOR?h=S:w===u.get().NODE&&(v=S)}}catch(e){T.e(e)}finally{T.f()}if(null!=h&&null!=v){var _={name:r.name,key:"",dynamicKey:null,filters:null,handleArgs:[p]};return(0,i.default)((0,i.default)({},t),{},{directives:t.directives.filter((function(e){return e!==r})),handles:t.handles?[].concat((0,o.default)(t.handles),[_]):[_]})}throw l("Unsupported use of @".concat(r.name," on field '").concat(t.name,"', expected an edge field (a field with 'cursor' and 'node' selection)."),[d.loc])}var k=s.args.find((function(e){return"edgeTypeName"===e.name}));if(!k)throw l("Unsupported use of @".concat(s.name," on field '").concat(t.name,"', 'edgeTypeName' argument must be provided."),[d.loc]);var F=m.getRawType(t.type);if(m.canHaveSelections(F)){var E={name:s.name,key:"",dynamicKey:null,filters:null,handleArgs:[p,k]};return(0,i.default)((0,i.default)({},t),{},{directives:t.directives.filter((function(e){return e!==s})),handles:t.handles?[].concat((0,o.default)(t.handles),[E]):[E]})}throw l("Unsupported use of @".concat(s.name," on field '").concat(t.name,"'. Expected an object, union or interface, but got '").concat(m.getTypeString(t.type),"'."),[s.loc])}e.exports={SCHEMA_EXTENSION:p,transform:function(e){return s.transform(e,{ScalarField:h,LinkedField:v,SplitOperation:m,Fragment:m})}}},function(e,t,n){"use strict";var r=n(0),a=r(n(2)),i=r(n(6)),o=n(4),s=n(40),l=n(70),u=n(1).createUserError;function c(e,t){var n,r,i,o=this.getContext().getSchema(),s=this.traverse(e,t),l=s.directives.find((function(e){return"stream"===e.name}));if(null==l)return s;var c=o.getNullableType(e.type);if(!o.isList(c))throw u("Invalid use of @stream on non-plural field '".concat(e.name,"'"),[l.loc]);s=(0,a.default)((0,a.default)({},s),{},{directives:s.directives.filter((function(e){return"stream"!==e.name}))});var f=l.args.find((function(e){return"if"===e.name}));if(v(f))return s;var d=l.args.find((function(e){return"initial_count"===e.name}));if(null==d)throw u("Invalid use of @stream, the 'initial_count' argument is required.",[l.loc]);var p=l.args.find((function(e){return"use_customized_batch"===e.name})),y=null!==(n=m(l,"label"))&&void 0!==n?n:e.alias,g=h(t.documentName,"stream",y);return t.recordLabel(g,l),{if:null!==(r=null==f?void 0:f.value)&&void 0!==r?r:null,initialCount:d.value,useCustomizedBatch:null!==(i=null==p?void 0:p.value)&&void 0!==i?i:null,kind:"Stream",label:g,loc:{kind:"Derived",source:l.loc},metadata:null,selections:[s]}}function f(e,t){var n=e.directives.find((function(e){return"stream"===e.name}));if(null!=n)throw u("Invalid use of @stream on scalar field '".concat(e.name,"'"),[n.loc]);return this.traverse(e,t)}function d(e,t){if(null!=e.directives.find((function(e){return"defer"===e.name})))throw u("Invalid use of @defer on an inline fragment, @defer is only supported on fragment spreads.",[e.loc]);return this.traverse(e,t)}function p(e,t){var n,r,o=this.traverse(e,t),u=o.directives.find((function(e){return"defer"===e.name}));if(null==u)return o;o=(0,a.default)((0,a.default)({},o),{},{directives:o.directives.filter((function(e){return"defer"!==e.name}))});var c=u.args.find((function(e){return"if"===e.name}));if(v(c))return o;var f=null!==(n=m(u,"label"))&&void 0!==n?n:function(e){if(0===e.args.length)return e.name;var t=(0,i.default)(e.args).sort((function(e,t){return e.name<t.name?-1:e.name>t.name?1:0})).map((function(e){return{name:e.name,value:s(e.value)}})),n=l(JSON.stringify(t));return"".concat(e.name,"_").concat(n)}(e),d=h(t.documentName,"defer",f);return t.recordLabel(d,u),{if:null!==(r=null==c?void 0:c.value)&&void 0!==r?r:null,kind:"Defer",label:d,loc:{kind:"Derived",source:u.loc},selections:[o]}}function m(e,t){var n=e.args.find((function(e){return e.name===t}));if(null==n)return null;var r="Literal"===n.value.kind?n.value.value:null;if(null==r||"string"!=typeof r)throw u("Expected the '".concat(t,"' value to @").concat(e.name," to be a string literal if provided."),[n.value.loc]);return r}function h(e,t,n){return"".concat(e,"$").concat(t,"$").concat(n)}function v(e){return null!=e&&"Literal"===e.value.kind&&!1===e.value.value}e.exports={transform:function(e){return o.transform(e,{FragmentSpread:p,InlineFragment:d,LinkedField:c,ScalarField:f},(function(e){var t=new Map;return{documentName:e.name,recordLabel:function(e,n){var r=t.get(e);if(r){var a,i=n.args.find((function(e){return"label"===e.name})),o=r.args.find((function(e){return"label"===e.name})),s=null!==(a=null==o?void 0:o.loc)&&void 0!==a?a:r.loc;throw i?u("Invalid use of @".concat(n.name,", the provided label is ")+"not unique. Specify a unique 'label' as a literal string.",[null==i?void 0:i.loc,s]):u("Invalid use of @".concat(n.name,", could not generate a ")+"default label that is unique. Specify a unique 'label' as a literal string.",[n.loc,s])}t.set(e,n)}}}))}}},function(e,t,n){"use strict";var r=n(4),a=n(1).createUserError;function i(e){if("id"===e.alias&&"id"!==e.name)throw a("Relay does not allow aliasing fields to `id`. This name is reserved for the globally unique `id` field on `Node`.",[e.loc]);return this.traverse(e)}e.exports={transform:function(e){return r.transform(e,{ScalarField:i,LinkedField:i})}}},function(e,t,n){"use strict";var r=n(0)(n(3)),a=n(71),i=n(1).createUserError;function o(e){var t,n=(0,r.default)(e.selections);try{for(n.s();!(t=n.n()).done;){var a=t.value;if("ScalarField"===a.kind&&"__typename"===a.name)throw i("Relay does not allow `__typename` field on Query, Mutation or Subscription",[a.loc])}}catch(e){n.e(e)}finally{n.f()}}function s(){}e.exports={transform:function(e){return a.validate(e,{Root:o,Fragment:s}),e}}},function(e,t,n){"use strict";var r=n(0)(n(2)),a=n(4),i=n(10),o=n(5),s=n(15),l=n(12).getRelayHandleKey;function u(e){var t="LinkedField"===e.kind?this.traverse(e):e,n=t.handles;if(!n||!n.length)return t;1!==n.length&&o(!1,'FieldHandleTransform: Expected fields to have at most one "handle" property, got `%s`.',n.join(", "));var a=this.getContext().getSchema(),u=t.alias,c=n[0],f=l(c.name,c.key,t.name),d=c.filters,p=d?t.args.filter((function(e){return-1!==d.indexOf(e.name)})):[];return null!=c.dynamicKey&&p.push({kind:"Argument",loc:c.dynamicKey.loc,name:"__dynamicKey",type:i.getNullableStringInput(a),value:s(c.dynamicKey)}),(0,r.default)((0,r.default)({},t),{},{args:p,alias:u,name:f,handles:null})}e.exports={transform:function(e){return a.transform(e,{LinkedField:u,ScalarField:u})}}},function(e,t,n){"use strict";var r=n(4),a=new Set(["required"]);e.exports={transform:function(e){return r.transform(e,{Directive:function(e){return a.has(e.name)?null:e}})}}},function(e,t,n){"use strict";var r=n(4);e.exports={transform:function(e){var t=new Set(e.getSchema().getDirectives().filter((function(e){return!e.isClient})).map((function(e){return e.name})));return r.transform(e,{Directive:function(e){return t.has(e.name)?e:null}})}}},function(e,t,n){"use strict";var r=n(0),a=r(n(2)),i=r(n(6)),o=n(4),s=n(10).generateIDField,l=n(72).hasUnaliasedSelection;function u(e,t){var n=this.traverse(e,t);if(l(e,"id"))return n;var r=this.getContext().getSchema(),o=r.assertCompositeType(r.getRawType(e.type));if(r.canHaveSelections(o)&&r.hasId(o))return(0,a.default)((0,a.default)({},n),{},{selections:[].concat((0,i.default)(n.selections),[t.idFieldForType(o)])});var s=r.getTypeFromString("Node");if(!s)return n;var u=r.assertInterfaceType(s);if(r.isAbstractType(o)){var c=(0,i.default)(n.selections);return r.mayImplement(o,u)&&c.push(t.idFragmentForType(u)),Array.from(r.getPossibleTypes(r.assertAbstractType(o)).values()).filter((function(e){return!r.implementsInterface(r.assertCompositeType(e),u)&&r.hasId(e)})).sort((function(e,t){return r.getTypeString(e)<r.getTypeString(t)?-1:1})).forEach((function(e){c.push(t.idFragmentForType(e))})),(0,a.default)((0,a.default)({},n),{},{selections:c})}return n}e.exports={transform:function(e){var t=e.getSchema(),n=new Map;function r(e){var r=n.get(e);return null==r&&(r=s(t,e),n.set(e,r)),r}var a=new Map,i={idFieldForType:r,idFragmentForType:function(e){var t=a.get(e);return null==t&&(t={kind:"InlineFragment",directives:[],loc:{kind:"Generated"},metadata:null,selections:[r(e)],typeCondition:e},a.set(e,t)),t}};return o.transform(e,{LinkedField:u},(function(){return i}))}}},function(e,t,n){"use strict";var r=n(0),a=r(n(2)),i=r(n(6)),o=n(4),s=n(24),l=n(72).hasUnaliasedSelection,u="__typename",c=new Map;function f(e,t){var n=this.getContext().getSchema(),r=n.getRawType(e.type),o=this.traverse(e,t);if(!!n.isServerType(r)&&n.isAbstractType(r)){var l=s(n,r);o=(0,a.default)((0,a.default)({},o),{},{selections:[{kind:"ScalarField",alias:l,args:[],directives:[],handles:null,loc:{kind:"Generated"},metadata:{abstractKey:l},name:u,type:n.assertScalarFieldType(n.getNonNullType(n.expectStringType()))}].concat((0,i.default)(o.selections))})}return o}function d(e,t){var n=this.getContext().getSchema(),r=c.get(e);if(null!=r&&"InlineFragment"===r.kind)return r;var o=n.getRawType(e.typeCondition);if(r=this.traverse(e,t),!!n.isServerType(o)&&n.isAbstractType(o)){var l=s(n,o);r=(0,a.default)((0,a.default)({},r),{},{selections:[{kind:"ScalarField",alias:l,args:[],directives:[],handles:null,loc:{kind:"Generated"},metadata:{abstractKey:l},name:u,type:n.assertScalarFieldType(n.getNonNullType(n.expectStringType()))}].concat((0,i.default)(r.selections))})}return c.set(e,r),r}function p(e,t){var n=this.getContext().getSchema(),r=c.get(e);return null!=r&&"LinkedField"===r.kind||(r=this.traverse(e,t),n.isAbstractType(n.getRawType(r.type))&&!l(r,u)&&(r=(0,a.default)((0,a.default)({},r),{},{selections:[t.typenameField].concat((0,i.default)(r.selections))})),c.set(e,r)),r}e.exports={transform:function(e){c=new Map;var t=e.getSchema(),n={kind:"ScalarField",alias:u,args:[],directives:[],handles:null,loc:{kind:"Generated"},metadata:null,name:u,type:t.assertScalarFieldType(t.getNonNullType(t.expectStringType()))};return o.transform(e,{Fragment:f,LinkedField:p,InlineFragment:d},(function(e){return{typenameField:n}}))}}},function(e,t,n){"use strict";var r=n(0)(n(2)),a=n(4),i=n(1).createUserError;function o(e){var t=this.traverse(e),n=t.directives.find((function(e){return"inline"===e.name}));return null==n?t:(0,r.default)((0,r.default)({},t),{},{directives:t.directives.filter((function(e){return e!==n})),metadata:(0,r.default)((0,r.default)({},t.metadata||{}),{},{inlineData:!0})})}function s(e){var t=this.traverse(e),n=this.getContext().get(t.name);if(!n||"Fragment"!==n.kind||!n.directives.some((function(e){return"inline"===e.name})))return t;if(n.argumentDefinitions.length>0||t.args.length>0)throw i("Variables are not yet supported inside @inline fragments.",[n.argumentDefinitions[0].loc]);if(t.directives.length>0)throw i("Directives on fragment spreads for @inline fragments are not yet supported",[t.loc]);var r=this.visit(n);return{kind:"InlineDataFragmentSpread",loc:t.loc,metadata:t.metadata,name:t.name,selections:[{directives:[],kind:"InlineFragment",loc:{kind:"Derived",source:t.loc},metadata:null,selections:r.selections,typeCondition:r.type}]}}e.exports={SCHEMA_EXTENSION:"\ndirective @inline on FRAGMENT_DEFINITION\n",transform:function(e){return a.transform(e,{FragmentSpread:s,Fragment:o})}}},function(e,t,n){"use strict";var r=n(4),a=n(5);function i(e){return null}e.exports={transform:function(e){var t,n=(t=new Map,function(e){var n=t.get(e);if(null!=n)return n;0!==e.args.length&&a(!1,"InlineFragmentsTransform: Cannot flatten fragment spread `%s` with arguments. Use the `ApplyFragmentArgumentTransform` before flattening",e.name);var r=this.getContext().getFragment(e.name,e.loc),i={kind:"InlineFragment",directives:e.directives,loc:{kind:"Derived",source:e.loc},metadata:e.metadata,selections:r.selections,typeCondition:r.type};return n=this.traverse(i),t.set(e,n),n});return r.transform(e,{Fragment:i,FragmentSpread:n})}}},function(e,t,n){"use strict";var r=n(0)(n(2)),a=n(4),i=n(1),o=i.createUserError,s=i.createCompilerError,l=n(12).RelayFeatureFlags;function u(e,t){var n;return this.traverse(e,{parentType:null!==(n=e.typeCondition)&&void 0!==n?n:t.parentType,types:t.types})}function c(e,t){return this.traverse(e,{parentType:e.type,types:t.types})}function f(e,t){var n=this.getContext().getSchema();if(n.getRawType(e.type)!==t.types.componentType)return e;var a=n.getFieldByName(t.parentType,e.name);if(null==a)throw s("Definition not found for field '".concat(n.getTypeString(t.parentType),".").concat(e.name,"'"),[e.loc]);var i=a.directives.find((function(e){return"react_flight_component"===e.name})),l=null==i?void 0:i.args.find((function(e){return"name"===e.name}));if(null==l||"StringValue"!==l.value.kind||"string"!=typeof l.value.value)throw o("Invalid Flight field, expected the schema extension to specify the component's module name with the '@react_flight_component' directive",[e.loc]);var u=l.value.value,c=n.getFieldByName(t.parentType,"flight");if(null==c)throw o("Invalid Flight field, expected the parent type '".concat(n.getTypeString(t.parentType),"' ")+"to define a 'flight(component: String, props: ReactFlightProps): ReactFlightComponent' field",[e.loc]);var f=c.args.get("component"),d=c.args.get("props");if(null==f||null==d||n.getRawType(f.type)!==n.getTypeFromString("String")||n.getRawType(d.type)!==t.types.propsType||n.getRawType(c.type)!==t.types.componentType)throw o("Invalid Flight field, expected the parent type '".concat(n.getTypeString(t.parentType),"' ")+"to define a 'flight(component: String, props: ReactFlightProps): ReactFlightComponent' field",[e.loc]);return(0,r.default)((0,r.default)({},e),{},{name:"flight",args:[{kind:"Argument",loc:e.loc,name:"component",type:n.getTypeFromString("String"),value:{kind:"Literal",value:u,loc:e.loc}},{kind:"Argument",loc:e.loc,name:"props",type:t.types.propsType,value:{kind:"ObjectValue",fields:e.args.map((function(e){return{kind:"ObjectFieldValue",loc:e.loc,name:e.name,value:e.value}})),loc:e.loc}}],metadata:(0,r.default)((0,r.default)({},e.metadata||{}),{},{flight:!0}),type:t.types.componentType})}e.exports={transform:function(e){var t=e.getSchema(),n=t.getTypeFromString("ReactFlightProps");n=n?t.asInputType(n):null;var r=t.getTypeFromString("ReactFlightComponent");if(r=r?t.asScalarFieldType(r):null,!l.ENABLE_REACT_FLIGHT_COMPONENT_FIELD||null==n||null==r)return e;var i={propsType:n,componentType:r};return a.transform(e,{ScalarField:f,LinkedField:c,InlineFragment:u},(function(e){return{parentType:e.type,types:i}}))}}},function(e,t,n){"use strict";var r=n(4);function a(e){return this.getContext().getSchema().isServerType(e.type)?this.traverse(e):null}function i(e){var t=this.getContext(),n=t.getFragment(e.name,e.loc);return t.getSchema().isServerType(n.type)?e:null}function o(e,t){return null}e.exports={transform:function(e){return r.transform(e,{Fragment:a,FragmentSpread:i,ClientExtension:o})}}},function(e,t,n){"use strict";var r=n(0)(n(2)),a=n(4);function i(e){var t=this.traverse(e);return t.handles?(0,r.default)((0,r.default)({},t),{},{handles:null}):t}e.exports={transform:function(e){return a.transform(e,{LinkedField:i,ScalarField:i})}}},function(e,t,n){"use strict";var r=n(0),a=r(n(2)),i=r(n(6)),o=n(4),s=n(17).Map,l=n(21),u=n(62),c=n(5);var f=new Map;function d(e){return f=new Map,function e(t,n,r){var o,d=0===r.size;if(d&&null!=(o=f.get(n)))return o;var p=[];(function(e){var t=l(e,(function(e){return"ScalarField"===e.kind||"LinkedField"===e.kind})),n=t[0],r=t[1];return[].concat((0,i.default)(n),(0,i.default)(r))})(n.selections).forEach((function(n){var a=u(t,n);switch(n.kind){case"ScalarField":case"FragmentSpread":r.has(a)||(p.push(n),r=r.set(a,null));break;case"Defer":case"Stream":case"ModuleImport":case"ClientExtension":case"InlineDataFragmentSpread":case"LinkedField":var i=e(t,n,r.get(a)||new s);i.node&&(p.push(i.node),r=r.set(a,i.selectionMap));break;case"InlineFragment":case"Condition":var o=e(t,n,r.get(a)||r);o.node&&(p.push(o.node),r=r.set(a,o.selectionMap));break;default:c(!1,"SkipRedundantNodesTransform: Unexpected node kind `%s`.",n.kind)}}));var m=p.length?(0,a.default)((0,a.default)({},n),{},{selections:p}):null;o={selectionMap:r,node:m},d&&f.set(n,o);return o}(this.getContext().getSchema(),e,new s).node}e.exports={transform:function(e){return o.transform(e,{Root:d,SplitOperation:d,Fragment:d})}}},function(e,t,n){"use strict";var r=n(4);function a(){return null}e.exports={transform:function(e){return r.transform(e,{SplitOperation:a})}}},function(e,t,n){"use strict";var r=n(0),a=r(n(2)),i=r(n(6)),o=n(4),s=n(5);function l(e,t,n){for(var r,o=(0,i.default)(n.selections);o.length;){var c=o.shift(),f=void 0;switch(c.kind){case"Condition":var d=u(c);"pass"===d?o.unshift.apply(o,(0,i.default)(c.selections)):"variable"===d&&(f=l(e,t,c));break;case"FragmentSpread":if(!t.has(c.name)){var p=e.getFragment(c.name),m=l(e,t,p);t.set(c.name,m)}t.get(c.name)&&(f=c);break;case"ClientExtension":case"ModuleImport":case"LinkedField":case"InlineFragment":case"Defer":case"Stream":f=l(e,t,c);break;case"ScalarField":f=c;break;case"InlineDataFragmentSpread":s(!1,"SkipUnreachableNodeTransform: Did not expect an InlineDataFragmentSpread here. Only expecting InlineDataFragmentSpread in reader ASTs and this transform to run only on normalization ASTs.");default:c.kind,s(!1,"SkipUnreachableNodeTransform: Unexpected selection kind `%s`.",c.kind)}f&&(r=r||[]).push(f)}return r?(0,a.default)((0,a.default)({},n),{},{selections:r}):null}function u(e){return"Variable"===e.condition.kind?"variable":e.condition.value===e.passingValue?"pass":"fail"}e.exports={transform:function(e){var t=new Map,n=o.transform(e,{Root:function(n){return l(e,t,n)},SplitOperation:function(n){return l(e,t,n)},Fragment:function(e){return null}});return Array.from(t.values()).reduce((function(e,t){return t?e.add(t):e}),n)}}},function(e,t,n){"use strict";var r=n(0),a=r(n(2)),i=r(n(3)),o=n(26);e.exports={transform:function(e){var t=o(e);return e.withMutations((function(e){var n,r=e,o=(0,i.default)(r.documents());try{var s=function(){var e=n.value;if("Root"!==e.kind)return"continue";var i=new Set(t.getRoot(e.name).argumentDefinitions.map((function(e){return e.name}))),o=e.argumentDefinitions.filter((function(e){return i.has(e.name)}));o.length!==e.argumentDefinitions.length&&(r=r.replace((0,a.default)((0,a.default)({},e),{},{argumentDefinitions:o})))};for(o.s();!(n=o.n()).done;)s()}catch(e){o.e(e)}finally{o.f()}return r}))}}},function(e,t,n){"use strict";var r=n(4),a=n(38);function i(e,t){return this.traverse(e,{parentType:e.type,splitOperations:t.splitOperations})}function o(e,t){return this.traverse(e,{parentType:e.typeCondition,splitOperations:t.splitOperations})}function s(e,t){var n=a(e.name),r=t.splitOperations.get(n);if(r)return r.parentSources.add(e.sourceDocument),e;var i=this.traverse(e,t),o={kind:"SplitOperation",name:n,selections:i.selections,loc:{kind:"Derived",source:e.loc},parentSources:new Set([e.sourceDocument]),metadata:{derivedFrom:i.name},type:t.parentType};return t.splitOperations.set(n,o),i}e.exports={transform:function(e){var t=new Map;return r.transform(e,{LinkedField:i,InlineFragment:o,ModuleImport:s},(function(e){return{parentType:e.type,splitOperations:t}})).addAll(Array.from(t.values()))}}},function(e,t,n){"use strict";var r=n(0)(n(2)),a=n(4);function i(e,t){var n=e.getNullableType(t),r=!e.isNonNull(t),a=e.isList(n),i=e.getRawType(n);return{enumValues:e.isEnum(i)?e.getEnumValues(e.assertEnumType(i)):null,nullable:r,plural:a,type:e.getTypeString(i)}}function o(e){var t=this.getContext().getSchema(),n=e.directives.find((function(e){return"relay_test_operation"===e.name}));if(null==n)return e;for(var a=[{selections:e.selections,path:null}],o={},s=function(){var e=a.pop(),n=e.selections,r=e.path;n.forEach((function(e){switch(e.kind){case"FragmentSpread":break;case"ScalarField":var n=null===r?e.alias:"".concat(r,".").concat(e.alias);o[n]=i(t,e.type);break;case"LinkedField":var s=null===r?e.alias:"".concat(r,".").concat(e.alias);o[s]=i(t,e.type),a.push({selections:e.selections,path:s});break;case"Condition":case"Defer":case"InlineDataFragmentSpread":case"InlineFragment":case"ModuleImport":case"Stream":a.push({selections:e.selections,path:r})}}))};a.length>0;)s();var l=Object.keys(o).sort((function(e,t){return e<t?-1:e>t?1:0})),u={};return l.forEach((function(e){u[e]=o[e]})),(0,r.default)((0,r.default)({},e),{},{directives:e.directives.filter((function(e){return e!==n})),metadata:(0,r.default)((0,r.default)({},e.metadata||{}),{},{relayTestingSelectionTypeInfo:u})})}e.exports={SCHEMA_EXTENSION:"directive @relay_test_operation on QUERY | MUTATION | SUBSCRIPTION",transform:function(e){return a.transform(e,{Fragment:function(e){return e},Root:o,SplitOperation:function(e){return e}})}}},function(e,t,n){"use strict";var r=n(0)(n(3)),a=n(26),i=n(1),o=i.createUserError,s=i.eachWithCombinedError;function l(e){var t,n=new Map,a=(0,r.default)(e);try{for(a.s();!(t=a.n()).done;){var i=t.value;n.set(i.name,i)}}catch(e){a.e(e)}finally{a.f()}return n}e.exports={transform:function(e){var t=a(e);return s(e.documents(),(function(n){if("Root"===n.kind){var a,i=t.getRoot(n.name),s=l(n.argumentDefinitions),u=l(i.argumentDefinitions),c=[],f=(0,r.default)(u.values());try{for(f.s();!(a=f.n()).done;){var d=a.value;s.has(d.name)||c.push(d)}}catch(e){f.e(e)}finally{f.f()}if(0!==c.length)throw o("Operation '".concat(n.name,"' references undefined variable(s):\n").concat(c.map((function(t){return"- $".concat(t.name,": ").concat(e.getSchema().getTypeString(t.type))})).join("\n"),"."),c.map((function(e){return e.loc})))}})),e}}},function(e,t,n){"use strict";var r=n(0)(n(3)),a=n(71),i=n(1).createUserError,o=n(57).getFieldDefinitionStrict;function s(e,t){var n=t.rootNode,r=this.getContext(),a=r.getSchema().getDirective(e.name);null!=a&&c(r.getSchema(),e,a.args,n)}function l(e,t){var n=t.rootNode;this.traverse(e,{rootNode:n,parentType:e.typeCondition})}function u(e,t){var n=t.parentType,r=t.rootNode,a=this.getContext().getSchema(),s=o(a,n,e.name);if(null==s){if(!e.directives.some((function(e){return"fixme_fat_interface"===e.name})))throw i("Unknown field '".concat(e.name,"' on type ")+"'".concat(a.getTypeString(n),"'."),[e.loc])}else c(a,e,a.getFieldConfig(s).args,r);this.traverse(e,{rootNode:r,parentType:e.type})}function c(e,t,n,a){var o,s=new Set(t.args.map((function(e){return e.name}))),l=(0,r.default)(n);try{for(l.s();!(o=l.n()).done;){var u=o.value;if(null==u.defaultValue&&e.isNonNull(u.type)&&!s.has(u.name))throw i("Required argument '".concat(u.name,": ").concat(e.getTypeString(u.type),"' ")+"is missing on '".concat(t.name,"' in '").concat(a.name,"'."),[t.loc,a.loc])}}catch(e){l.e(e)}finally{l.f()}}e.exports={transform:function(e){return a.validate(e,{Directive:s,InlineFragment:l,LinkedField:u,ScalarField:u},(function(e){return{rootNode:e,parentType:e.type}})),e}}},function(e,t,n){"use strict";var r=n(0)(n(3)),a=n(26),i=n(1),o=i.createUserError,s=i.eachWithCombinedError;e.exports={transform:function(e){var t=a(e);return s(e.documents(),(function(e){if("Root"===e.kind){var n,a=new Map(e.argumentDefinitions.map((function(e){return[e.name,e.loc]}))),i=function(e){var t,n=new Map,a=(0,r.default)(e);try{for(a.s();!(t=a.n()).done;){var i=t.value;n.set(i.name,i)}}catch(e){a.e(e)}finally{a.f()}return n}(t.getRoot(e.name).argumentDefinitions),s=(0,r.default)(i.keys());try{for(s.s();!(n=s.n()).done;){var l=n.value;a.delete(l)}}catch(e){s.e(e)}finally{s.f()}var u=e.directives.find((function(e){return"DEPRECATED__relay_ignore_unused_variables_error"===e.name}));if(a.size>0&&!u){var c=a.size>1;throw o("Variable".concat(c?"s":""," '$").concat(Array.from(a.keys()).join("', '$"),"' ").concat(c?"are":"is"," never used in operation '").concat(e.name,"'."),Array.from(a.values()))}if(0===a.size&&u)throw o("Invalid usage of '@DEPRECATED__relay_ignore_unused_variables_error.'"+"No unused variables found in the query '".concat(e.name,"'"),[u.loc])}})),e},SCHEMA_EXTENSION:"directive @DEPRECATED__relay_ignore_unused_variables_error on QUERY | MUTATION | SUBSCRIPTION"}},function(e,t,n){"use strict";var r=null;e.exports={set:function(e){r=e},check:function(e,t){if(null==r)return!0;var n=r.get(e);return null==n||n.has(t)}}},function(e,t,n){"use strict";var r=n(0),a=r(n(2)),i=r(n(3)),o=n(5),s=n(16),l=n(73).toASTRecord,u=n(7),c=u.Source,f=u.parse,d=function(){function e(e){var t=e.extractFromFile,n=e.state;this._extractFromFile=t,this._state=(0,a.default)({},n)}e.fromSavedState=function(t){var n,r=t.extractFromFile,a=t.savedState,u={},d=(0,i.default)(a);try{var p=function(){var e,t=n.value,r=t.file,a=t.sources,d={},p=[],m=(0,i.default)(a);try{for(m.s();!(e=m.n()).done;){var h=e.value,v=f(new c(h,r));v.definitions.length||o(!1,"expected not empty list of definitions"),v.definitions.map((function(e){return l(e)})).forEach((function(e){d[s(e.text)]=e.ast})),p.push(h)}}catch(e){m.e(e)}finally{m.f()}u[r]={nodes:d,sources:p}};for(d.s();!(n=d.n()).done;)p()}catch(e){d.e(e)}finally{d.f()}return new e({extractFromFile:r,state:u})};var t=e.prototype;return t.processChanges=function(t,n){var r,o,l,u=[],c=[],f=(0,a.default)({},this._state),d=(0,i.default)(n);try{for(d.s();!(l=d.n()).done;){var p,m,h=l.value,v=void 0,y=void 0;try{var g=this._extractFromFile(t,h);null!=g&&(v=g.nodes,y=g.sources)}catch(e){throw new Error("RelayCompiler: Sources module failed to parse ".concat(h.name,":\n").concat(e.message))}var b=f.hasOwnProperty(h.name),T=null!==(p=null===(m=f[h.name])||void 0===m?void 0:m.nodes)&&void 0!==p?p:{};if(null!=v&&v.length>0){var S,w={},_=new Set,k=(0,i.default)(v);try{for(k.s();!(S=k.n()).done;){var F=S.value,E=F.ast,x=F.text,C=s(x);if(_.has(C)){var N="unknown";switch(E.kind){case"FragmentDefinition":N=E.name.value;break;case"OperationDefinition":N=null!==(r=null===(o=E.name)||void 0===o?void 0:o.value)&&void 0!==r?r:"unnamed operation"}throw new Error("Duplicate definition of `".concat(N,"` in `").concat(h.name,"`"))}_.add(C),b&&null!=T[C]?w[C]=T[C]:(w[C]=E,u.push({file:h.name,ast:E}))}}catch(e){k.e(e)}finally{k.f()}if(b)for(var D=0,I=Object.keys(T);D<I.length;D++){var A=I[D],R=T[A];_.has(A)||c.push({file:h.name,ast:R})}f[h.name]={nodes:w,sources:y}}else if(b){for(var O=0,M=Object.keys(T);O<M.length;O++){var L=T[M[O]];c.push({file:h.name,ast:L})}delete f[h.name]}}}catch(e){d.e(e)}finally{d.f()}return{changes:{added:u,removed:c},sources:new e({extractFromFile:this._extractFromFile,state:f})}},t.nodes=function*(){for(var e in this._state)for(var t=this._state[e],n=0,r=Object.values(t.nodes);n<r.length;n++){var a=r[n];yield a}},t.serializeState=function(){var e=[];for(var t in this._state)e.push({file:t,sources:this._state[t].sources});return e},e}();e.exports=d},function(e,t,n){"use strict";var r=n(0),a=n(9),i=r(n(3)),o=n(5),s=function(){function e(e){return this._map=new Map(e),this}var t=e.prototype;return t.clear=function(){this._map.clear()},t.delete=function(e){return this._map.delete(e)},t.entries=function(){return this._map.entries()},t.forEach=function(e,t){this._map.forEach(e,t)},t.map=function(t){var n,r=new e,a=(0,i.default)(this._map);try{for(a.s();!(n=a.n()).done;){var o=n.value,s=o[0],l=o[1];r.set(s,t(l,s,this))}}catch(e){a.e(e)}finally{a.f()}return r},t.asyncMap=function(){var t=a((function*(t){var n,r=this,a=[],o=(0,i.default)(this._map);try{var s=function(){var e=n.value,i=e[0],o=e[1];a.push(t(o,i,r).then((function(e){return[i,e]})))};for(o.s();!(n=o.n()).done;)s()}catch(e){o.e(e)}finally{o.f()}return new e(yield Promise.all(a))}));return function(e){return t.apply(this,arguments)}}(),t.get=function(e){return this.has(e)||o(!1,"StrictMap: trying to read non-existent key `%s`.",String(e)),this._map.get(e)},t.has=function(e){return this._map.has(e)},t.keys=function(){return this._map.keys()},t.set=function(e,t){return this._map.set(e,t),this},t.values=function(){return this._map.values()},e}();e.exports=s},function(e,t,n){"use strict";var r=n(9),a=n(12).isPromise;function i(e,t,n){var r=Date.now(),i=n();if(a(i))throw new Error("reportAndReturnTime: fn(...) returned an unexpected promise. Please use `reportAndReturnAsyncTime` method instead.");var o=Date.now()-r;return e.reportTime(t,o),[i,o]}function o(){return(o=r((function*(e,t,n){var r=Date.now(),i=n();if(!a(i))throw new Error("reportAsyncTime: fn(...) expected to return a promise.");var o=yield i,s=Date.now()-r;return e.reportTime(t,s),[o,s]}))).apply(this,arguments)}function s(){return(s=r((function*(e,t,n){var r=Date.now(),i=n();if(!a(i))throw new Error("reportAsyncTime: fn(...) expected to return a promise.");var o=yield i,s=Date.now()-r;return e.reportTime(t,s),o}))).apply(this,arguments)}e.exports={reportTime:function(e,t,n){return i(e,t,n)[0]},reportAndReturnTime:i,reportAsyncTime:function(e,t,n){return s.apply(this,arguments)},reportAndReturnAsyncTime:function(e,t,n){return o.apply(this,arguments)}}},function(e,t,n){"use strict";var r=n(29),a=n(19),i=n(25),o=n(36);e.exports=function(e){var t=e.schema,n=e.compilerTransforms,s=e.definitions,l=e.reporter,u=e.typeGenerator,c=r.convertASTDocuments(t,[{kind:"Document",definitions:s}],i.transform),f=new a(t).addAll(c);return{transformedTypeContext:f.applyTransforms(u.transforms,l),artifacts:o(f,n,l)}}},function(e,t,n){"use strict";var r=n(0)(n(3)),a=n(18).getName;e.exports=function(e,t){var n,i=new Set,o=(0,r.default)(t);try{for(o.s();!(n=o.n()).done;){var s,l=n.value,u=e.get(l),c=(0,r.default)(u.initialDirty);try{for(c.s();!(s=c.n()).done;){var f=s.value;i.add(f)}}catch(e){c.e(e)}finally{c.f()}var d,p=(0,r.default)(u.changes.added);try{for(p.s();!(d=p.n()).done;){var m=d.value.ast;i.add(a(m))}}catch(e){p.e(e)}finally{p.f()}var h,v=(0,r.default)(u.changes.removed);try{for(v.s();!(h=v.n()).done;){var y=h.value.ast;i.add(a(y))}}catch(e){v.e(e)}finally{v.f()}}}catch(e){o.e(e)}finally{o.f()}return i}},function(e,t,n){"use strict";var r=n(16),a=n(7).print;e.exports=function(e){return r(a(e))}},function(e,t,n){"use strict";var r=n(33).create,a=new Map;e.exports=function(e,t,n){var i=e(),o=a.get(i);return null==o&&(o=r(i,t(),n),a.set(i,o)),o}},function(module,exports,__webpack_require__){"use strict";var _interopRequireDefault=__webpack_require__(0),_asyncToGenerator=__webpack_require__(9),_createForOfIteratorHelper2=_interopRequireDefault(__webpack_require__(3)),_defineProperty2=_interopRequireDefault(__webpack_require__(30)),_objectSpread2=_interopRequireDefault(__webpack_require__(2)),_toConsumableArray2=_interopRequireDefault(__webpack_require__(6)),CodegenRunner=__webpack_require__(41),ConsoleReporter=__webpack_require__(45),DotGraphQLParser=__webpack_require__(46),RelayFileWriter=__webpack_require__(56),RelayIRTransforms=__webpack_require__(69),RelayLanguagePluginJavaScript=__webpack_require__(138),RelaySourceModuleParser=__webpack_require__(51),WatchmanClient=__webpack_require__(23),crypto=__webpack_require__(14),fs=__webpack_require__(13),glob=__webpack_require__(139),invariant=__webpack_require__(5),path=__webpack_require__(11),_require=__webpack_require__(7),buildClientSchema=_require.buildClientSchema,Source=_require.Source,printSchema=_require.printSchema,commonTransforms=RelayIRTransforms.commonTransforms,codegenTransforms=RelayIRTransforms.codegenTransforms,fragmentTransforms=RelayIRTransforms.fragmentTransforms,printTransforms=RelayIRTransforms.printTransforms,queryTransforms=RelayIRTransforms.queryTransforms,relaySchemaExtensions=RelayIRTransforms.schemaExtensions;function buildWatchExpression(e){return["allof",["type","f"],["anyof"].concat((0,_toConsumableArray2.default)(e.extensions.map((function(e){return["suffix",e]})))),["anyof"].concat((0,_toConsumableArray2.default)(e.include.map((function(e){return["match",e,"wholename"]}))))].concat((0,_toConsumableArray2.default)(e.exclude.map((function(e){return["not",["match",e,"wholename"]]}))))}function getFilepathsFromGlob(e,t){var n=t.extensions,r=t.include,a=t.exclude,i=new Set;return r.forEach((function(t){return glob.sync("".concat(t,"/*.+(").concat(n.join("|"),")"),{cwd:e,ignore:a}).forEach((function(e){return i.add(e)}))})),Array.from(i)}function getLanguagePlugin(language,options){if("javascript"===language)return RelayLanguagePluginJavaScript({eagerESModules:Boolean(options&&options.eagerESModules)});var languagePlugin;if("string"==typeof language){var pluginPath=path.resolve(process.cwd(),language),requirePath=fs.existsSync(pluginPath)?pluginPath:"relay-compiler-language-".concat(language);try{languagePlugin=eval("require")(requirePath),languagePlugin.default&&(languagePlugin=languagePlugin.default)}catch(t){var e=new Error("Unable to load language plugin ".concat(requirePath,": ").concat(t.message));throw e.stack=t.stack,e}}else languagePlugin=language;if(null!=languagePlugin.default&&(languagePlugin=languagePlugin.default),"function"==typeof languagePlugin)return languagePlugin();throw new Error("Expected plugin to be a initializer function.")}function getPersistQueryFunction(config){var configValue=config.persistFunction;if(null==configValue)return null;if("string"!=typeof configValue){if("function"==typeof configValue)return configValue;throw new Error("Expected persistFunction to be a path string or a function.")}try{var persistFunction=eval("require")(path.resolve(process.cwd(),configValue));return persistFunction.default?persistFunction.default:persistFunction}catch(t){var e=new Error("Unable to load persistFunction ".concat(configValue,": ").concat(t.message));throw e.stack=t.stack,e}}function main(e){return _main.apply(this,arguments)}function _main(){return(_main=_asyncToGenerator((function*(e){if(e.verbose&&e.quiet)throw new Error("I can't be quiet and verbose at the same time");var t=getPathBasedConfig(e);t=yield getWatchConfig(t);var n=module.exports.getCodegenRunner(t),r=t.watch?yield n.watchAll():yield n.compileAll();"ERROR"===r&&process.exit(100),t.validate&&"NO_CHANGES"!==r&&process.exit(101)}))).apply(this,arguments)}function getPathBasedConfig(e){var t=path.resolve(process.cwd(),e.schema);if(!fs.existsSync(t))throw new Error("--schema path does not exist: ".concat(t));var n=path.resolve(process.cwd(),e.src);if(!fs.existsSync(n))throw new Error("--src path does not exist: ".concat(n));var r=e.persistOutput;if("string"==typeof r){r=path.resolve(process.cwd(),r);var a=path.dirname(r);if(!fs.existsSync(a))throw new Error("--persistOutput path does not exist: ".concat(r))}return(0,_objectSpread2.default)((0,_objectSpread2.default)({},e),{},{schema:t,src:n,persistOutput:r})}function getWatchConfig(e){return _getWatchConfig.apply(this,arguments)}function _getWatchConfig(){return(_getWatchConfig=_asyncToGenerator((function*(e){var t=e.watchman&&(yield WatchmanClient.isAvailable());if(e.watch){if(!t)return console.error("Watchman is required to watch for changes. Running with watch mode disabled."),(0,_objectSpread2.default)((0,_objectSpread2.default)({},e),{},{watch:!1,watchman:!1});if(!module.exports.hasWatchmanRootFile(e.src))throw new Error('\n--watch requires that the src directory have a valid watchman "root" file.\n\nRoot files can include:\n- A .git/ Git folder\n- A .hg/ Mercurial folder\n- A .watchmanconfig file\n\nEnsure that one such file exists in '.concat(e.src," or its parents.\n      ").trim())}else t&&!e.validate&&console.log("HINT: pass --watch to keep watching for changes.");return(0,_objectSpread2.default)((0,_objectSpread2.default)({},e),{},{watchman:t})}))).apply(this,arguments)}function getCodegenRunner(e){var t,n=new ConsoleReporter({verbose:e.verbose,quiet:e.quiet}),r=getSchemaSource(e.schema),a=getLanguagePlugin(e.language,{eagerESModules:!0===e.eagerESModules}),i=getPersistQueryFunction(e),o=e.extensions||a.inputExtensions,s=a.outputExtension,l=o.join("/"),u=s,c=RelaySourceModuleParser(a.findGraphQLTags,a.getFileFilter),f=e.artifactDirectory,d=null!=f?path.resolve(process.cwd(),f):null,p=null!=d?d:"__generated__",m={extensions:o,include:e.include,exclude:["**/*.graphql.*"].concat((0,_toConsumableArray2.default)(e.exclude))},h={extensions:["graphql"],include:e.include,exclude:[path.relative(e.src,e.schema)].concat(e.exclude)},v=a.schemaExtensions?[].concat((0,_toConsumableArray2.default)(a.schemaExtensions),(0,_toConsumableArray2.default)(relaySchemaExtensions)):relaySchemaExtensions,y=(t={},(0,_defineProperty2.default)(t,l,{baseDir:e.src,getFileFilter:c.getFileFilter,getParser:c.getParser,getSchemaSource:function(){return r},schemaExtensions:v,watchmanExpression:e.watchman?buildWatchExpression(m):null,filepaths:e.watchman?null:getFilepathsFromGlob(e.src,m)}),(0,_defineProperty2.default)(t,"graphql",{baseDir:e.src,getParser:DotGraphQLParser.getParser,getSchemaSource:function(){return r},schemaExtensions:v,watchmanExpression:e.watchman?buildWatchExpression(h):null,filepaths:e.watchman?null:getFilepathsFromGlob(e.src,h)}),t),g=(0,_defineProperty2.default)({},u,{writeFiles:getRelayFileWriter(e.src,a,e.noFutureProofEnums,d,e.persistOutput,e.customScalars,i,e.repersist),isGeneratedFile:a.isGeneratedFile?a.isGeneratedFile:function(e){return e.endsWith(".graphql."+s)&&e.includes(p)},parser:l,baseParsers:["graphql"]});return new CodegenRunner({reporter:n,parserConfigs:y,writerConfigs:g,onlyValidate:e.validate,sourceControl:null})}function defaultPersistFunction(e){var t=crypto.createHash("md5");t.update(e);var n=t.digest("hex");return Promise.resolve(n)}function getRelayFileWriter(e,t,n,r,a,i,o,s){return function(){var l=_asyncToGenerator((function*(l){var u,c,f=l.onlyValidate,d=l.schema,p=l.documents,m=l.baseDocuments,h=l.sourceControl,v=l.reporter;if(null!=o||null!=a){c=new Map;var y=o||defaultPersistFunction;u=function(){var e=_asyncToGenerator((function*(e){var t=yield y(e);return"string"!=typeof t&&invariant(!1,"Expected persist function to return a string, got `%s`.",t),c.set(t,e),t}));return function(t){return e.apply(this,arguments)}}()}var g=t.schemaExtensions?[].concat((0,_toConsumableArray2.default)(t.schemaExtensions),(0,_toConsumableArray2.default)(relaySchemaExtensions)):relaySchemaExtensions,b=yield RelayFileWriter.writeAll({config:{baseDir:e,compilerTransforms:{commonTransforms:commonTransforms,codegenTransforms:codegenTransforms,fragmentTransforms:fragmentTransforms,printTransforms:printTransforms,queryTransforms:queryTransforms},customScalars:i||{},formatModule:t.formatModule,optionalInputFieldsForFlow:[],schemaExtensions:g,useHaste:!1,noFutureProofEnums:n,extension:t.outputExtension,typeGenerator:t.typeGenerator,outputDir:r,persistQuery:u,repersist:s},onlyValidate:f,schema:d,baseDocuments:m,documents:p,reporter:v,sourceControl:h,languagePlugin:t});if(null!=c&&null!=a){var T={};if(fs.existsSync(a))try{var S=fs.readFileSync(a,"utf8"),w=JSON.parse(S);null!=w&&"object"==typeof w?T=w:console.error("Invalid data in persisted query file '".concat(a,"', expected an object."))}catch(e){console.error(e)}var _,k=(0,_createForOfIteratorHelper2.default)(c.entries());try{for(k.s();!(_=k.n()).done;){var F=_.value,E=F[0],x=F[1];T[E]=x}}catch(e){k.e(e)}finally{k.f()}var C=JSON.stringify(T,null,2);fs.writeFileSync(a,C,"utf8")}return b}));return function(e){return l.apply(this,arguments)}}()}function getSchemaSource(e){var t=fs.readFileSync(e,"utf8");return".json"===path.extname(e)&&(t=printSchema(buildClientSchema(JSON.parse(t).data))),t="\n  directive @include(if: Boolean) on FRAGMENT_SPREAD | FIELD | INLINE_FRAGMENT\n  directive @skip(if: Boolean) on FRAGMENT_SPREAD | FIELD | INLINE_FRAGMENT\n\n  ".concat(t,"\n  "),new Source(t,e)}var WATCHMAN_ROOT_FILES=[".git",".hg",".watchmanconfig"];function hasWatchmanRootFile(e){for(;path.dirname(e)!==e;){if(WATCHMAN_ROOT_FILES.some((function(t){return fs.existsSync(path.join(e,t))})))return!0;e=path.dirname(e)}return!1}module.exports={getCodegenRunner:getCodegenRunner,getLanguagePlugin:getLanguagePlugin,getWatchConfig:getWatchConfig,hasWatchmanRootFile:hasWatchmanRootFile,main:main}},function(e,t,n){"use strict";var r=n(37),a=n(34).find,i=n(74),o=i.formatGeneratedCommonjsModule,s=i.formatGeneratedESModule;e.exports=function(e){return{inputExtensions:["js","jsx"],outputExtension:"js",typeGenerator:r,formatModule:e&&e.eagerESModules?s:o,findGraphQLTags:a}}},function(e,t){e.exports=require("glob")},function(e,t,n){"use strict";var r=n(0),a=n(9),i=r(n(6)),o=n(43);function s(e,t){return new Promise((function(n,r){o.execFile(e,t,(function(e){e?r(e):n()}))}))}var l,u={addRemove:(l=a((function*(e,t){e.length>0&&(yield s("hg",["add"].concat((0,i.default)(e)))),t.length>0&&(yield s("hg",["forget"].concat((0,i.default)(t))))})),function(e,t){return l.apply(this,arguments)})};e.exports={SourceControlMercurial:u}}]);